---
description: 
globs: 
alwaysApply: true
---
# UI/UX Modification Guidelines

## Core Principles
- Preserve all existing functionality when modifying UI
- Maintain accessibility standards
- Follow Material Design 3 guidelines
- Ensure responsive design for all screen sizes
- Support both light and dark modes
- Implement adaptive layouts based on screen size
- Apply proper safe area insets for modern devices
- Use proper contrast ratios for text readability

## Design System
- Use consistent color scheme from [lib/config/theme.dart](mdc:lib/config/theme)
- Follow typography hierarchy (heading, body, caption)
- Maintain consistent spacing and padding
- Use standard elevation levels
- Implement consistent animations and transitions

## UI Components
- Prefer reusing existing components in [lib/widgets/](mdc:lib/widgets)
- Document new components with usage examples
- Test components across different screen sizes
- Create reusable components for repeated UI patterns
- Extract complex UI into smaller components

## Animation Guidelines
- Keep animations under 300ms for UI transitions
- Use standard curves (ease, easeIn, easeOut, easeInOut)
- Implement staggered animations for complex UI
- Ensure animations can be disabled for accessibility
- Use Hero animations for shared element transitions

## Gesture Handling
- Implement intuitive page turning gestures
- Support pinch-to-zoom for text and images
- Use haptic feedback for important interactions
- Implement consistent long-press interactions
- Support system back gestures

## Inspiration Implementation
- When adapting from design inspiration:
  - Identify core UI patterns to implement
  - Break down complex designs into Flutter components
  - Maintain app's existing navigation patterns
  - Ensure performance is not compromised
  - Document design decisions

## Testing UI Changes
- Verify all existing functionality remains intact
- Test on multiple device sizes
- Verify both light and dark mode appearance
- Check performance impact of UI changes
- Ensure accessibility is maintained

## Chinese Text Rendering
- Maintain proper rendering of Chinese characters
- Ensure dictionary lookup works with new UI
- Verify pinyin display compatibility
- Test text selection in modified UI
- Keep annotation features working properly

## Refactoring Process
1. Take screenshots of existing screens before changes
2. Document current functionality and user flows
3. Implement UI changes in isolation when possible
4. Use feature flags for major UI changes
5. Conduct thorough regression testing
