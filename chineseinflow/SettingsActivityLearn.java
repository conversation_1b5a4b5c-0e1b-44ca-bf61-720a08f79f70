package com.gamestolearnenglish.chineseinflow;

import android.content.Intent;
import android.graphics.Typeface;
import android.os.Bundle;
import android.support.v7.app.AppCompatActivity;
import android.view.View;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.LinearLayout;
import android.widget.TextView;

public class SettingsActivityLearn extends AppCompatActivity {
    private View.OnClickListener backListener = new View.OnClickListener() {
        public void onClick(View arg0) {
            SettingsActivityLearn.this.finish();
        }
    };
    /* access modifiers changed from: private */
    public boolean delayTextBool;
    private CheckBox delayTextBox;
    /* access modifiers changed from: private */
    public Intent myData = new Intent();
    private View.OnClickListener okListener = new View.OnClickListener() {
        public void onClick(View arg0) {
            SettingsActivityLearn.this.updateBooleans();
            SettingsActivityLearn.this.myData.putExtra("altBool", true);
            SettingsActivityLearn.this.myData.putExtra("playAudBool", SettingsActivityLearn.this.playAudBool);
            SettingsActivityLearn.this.myData.putExtra("showPinyinBool", SettingsActivityLearn.this.showPinyinBool);
            SettingsActivityLearn.this.myData.putExtra("showEnglishBool", SettingsActivityLearn.this.showEnglishBool);
            SettingsActivityLearn.this.myData.putExtra("showTextBool", SettingsActivityLearn.this.showTextBool);
            SettingsActivityLearn.this.myData.putExtra("delayTextBool", SettingsActivityLearn.this.delayTextBool);
            SettingsActivityLearn.this.myData.putExtra("showPinyinButtonsBool", SettingsActivityLearn.this.showPinyinButtonsBool);
            SettingsActivityLearn.this.setResult(0, SettingsActivityLearn.this.myData);
            SettingsActivityLearn.this.finish();
        }
    };
    /* access modifiers changed from: private */
    public boolean playAudBool;
    private CheckBox playAudBox;
    /* access modifiers changed from: private */
    public boolean showEnglishBool;
    private CheckBox showEnglishBox;
    /* access modifiers changed from: private */
    public boolean showPinyinBool;
    private CheckBox showPinyinBox;
    /* access modifiers changed from: private */
    public boolean showPinyinButtonsBool;
    private CheckBox showPinyinButtonsBox;
    /* access modifiers changed from: private */
    public boolean showTextBool;
    private CheckBox showTextBox;

    /* access modifiers changed from: protected */
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setVolumeControlStream(3);
        setContentView((int) R.layout.activity_settings_learn);
        Intent gotInt = getIntent();
        this.playAudBool = gotInt.getBooleanExtra("playAudBool", false);
        this.showPinyinBool = gotInt.getBooleanExtra("showPinyinBool", false);
        this.showEnglishBool = gotInt.getBooleanExtra("showEnglishBool", false);
        this.showTextBool = gotInt.getBooleanExtra("showTextBool", false);
        this.delayTextBool = gotInt.getBooleanExtra("delayTextBool", false);
        this.showPinyinButtonsBool = gotInt.getBooleanExtra("showPinyinButtonsBool", false);
        this.playAudBox = (CheckBox) findViewById(R.id.playAudBoxCheck);
        this.showPinyinBox = (CheckBox) findViewById(R.id.showPinyinBoxCheck);
        this.showEnglishBox = (CheckBox) findViewById(R.id.showEnglishBoxCheck);
        this.showTextBox = (CheckBox) findViewById(R.id.showTextBoxCheck);
        this.delayTextBox = (CheckBox) findViewById(R.id.delayTextBoxCheck);
        this.showPinyinButtonsBox = (CheckBox) findViewById(R.id.showPinyinButtonBoxCheck);
        setBoxesDefault();
        ((LinearLayout) findViewById(R.id.frontPart)).setOnClickListener((View.OnClickListener) null);
        ((Button) findViewById(R.id.settingsOkBut)).setOnClickListener(this.okListener);
        TextView[] tViews = new TextView[7];
        for (int i = 0; i < 7; i++) {
            tViews[i] = (TextView) findViewById(getResources().getIdentifier("learnSettingsText" + i, "id", getPackageName()));
            tViews[i].setTypeface(Typeface.createFromAsset(getAssets(), "AmaBold.ttf"));
        }
        this.myData.putExtra("altBool", false);
        setResult(0, this.myData);
    }

    public void checkClick(View v) {
    }

    private void setBoxesDefault() {
        if (this.playAudBool) {
            this.playAudBox.setChecked(true);
        }
        if (this.showPinyinBool) {
            this.showPinyinBox.setChecked(true);
        }
        if (this.showEnglishBool) {
            this.showEnglishBox.setChecked(true);
        }
        if (this.showTextBool) {
            this.showTextBox.setChecked(true);
        }
        if (this.delayTextBool) {
            this.delayTextBox.setChecked(true);
        }
        if (this.showPinyinButtonsBool) {
            this.showPinyinButtonsBox.setChecked(true);
        }
    }

    /* access modifiers changed from: private */
    public void updateBooleans() {
        boolean z;
        boolean z2;
        boolean z3;
        boolean z4;
        boolean z5 = true;
        this.playAudBool = this.playAudBox.isChecked();
        if (this.showPinyinBox.isChecked()) {
            z = true;
        } else {
            z = false;
        }
        this.showPinyinBool = z;
        if (this.showEnglishBox.isChecked()) {
            z2 = true;
        } else {
            z2 = false;
        }
        this.showEnglishBool = z2;
        if (this.showTextBox.isChecked()) {
            z3 = true;
        } else {
            z3 = false;
        }
        this.showTextBool = z3;
        if (this.delayTextBox.isChecked()) {
            z4 = true;
        } else {
            z4 = false;
        }
        this.delayTextBool = z4;
        if (!this.showPinyinButtonsBox.isChecked()) {
            z5 = false;
        }
        this.showPinyinButtonsBool = z5;
    }
}
