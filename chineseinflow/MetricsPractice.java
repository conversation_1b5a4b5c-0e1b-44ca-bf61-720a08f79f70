package com.gamestolearnenglish.chineseinflow;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class MetricsPractice {
    public Boolean FINISHED = false;
    private int LIM_1 = 6;
    private int LIM_2 = 502;
    public Boolean SDFLAG = false;
    public Boolean SDMODE = false;
    private int[] cArr = new int[30];
    private int countVar = 0;
    private int[] curRun = new int[8];
    private int[] dArr = new int[30];
    private Boolean missFlag = false;
    private int nLoop;
    private int[] pLast = new int[30];
    private int rVar;
    private int[] sArr = new int[30];
    private int sdCount;
    private int stCount = 0;
    public int timeDelay = 1000;
    private int unknownCount = 0;

    public MetricsPractice() {
        init();
    }

    public int[] getRoundVar() {
        this.countVar++;
        if (this.SDMODE.booleanValue()) {
            return sdRound();
        }
        return stRound();
    }

    private int[] sdRound() {
        int ini = this.sArr[this.sdCount];
        this.dArr = randomizeIntArrayInit(this.dArr, ini);
        int[] myOut = new int[8];
        for (int i = 0; i < 8; i++) {
            myOut[i] = this.dArr[i];
        }
        this.sdCount++;
        this.rVar = ini;
        return myOut;
    }

    private int[] stRound() {
        this.rVar = this.curRun[0];
        int[] myOut = new int[8];
        for (int i = 0; i < 8; i++) {
            myOut[i] = this.curRun[i];
        }
        return myOut;
    }

    private void setIncVar() {
        this.timeDelay -= 50;
        if (this.timeDelay < 50) {
            this.timeDelay = 0;
        }
    }

    private void init() {
        for (int i = 0; i < 30; i++) {
            this.cArr[i] = 0;
            this.dArr[i] = i;
            this.pLast[i] = 0;
        }
        ranSeq();
        for (int i2 = 0; i2 < 8; i2++) {
            this.curRun[i2] = this.sArr[i2];
        }
        this.nLoop = 8;
    }

    public void hit() {
        hitUpdateStats();
        hitNextRound();
        sendMetrics();
        if (this.sdCount >= 30) {
            this.FINISHED = true;
        }
    }

    private void hitUpdateStats() {
        if (!this.missFlag.booleanValue()) {
            setIncVar();
            if (this.cArr[this.rVar] == 0) {
                this.stCount++;
                this.cArr[this.rVar] = 6;
            } else {
                this.cArr[this.rVar] = this.cArr[this.rVar] + 1;
            }
        }
        this.missFlag = false;
        this.pLast[this.rVar] = this.countVar + 1;
    }

    private void hitNextRound() {
        if (this.SDMODE.booleanValue()) {
            return;
        }
        if (checkGood().booleanValue()) {
            initSdMode();
        } else {
            updateCurRun();
        }
    }

    private void updateCurRun() {
        if (this.cArr[this.curRun[0]] == this.LIM_1) {
            this.cArr[this.curRun[0]] = 500;
            prodNew();
        } else if (this.cArr[this.curRun[0]] == this.LIM_2) {
            prodNew();
        }
        int curShuf = 1;
        for (int i = 2; i < 8; i++) {
            if (getUrg(this.curRun[i]) > getUrg(this.curRun[curShuf])) {
                curShuf = i;
            }
        }
        int tmp = this.curRun[0];
        this.curRun[0] = this.curRun[curShuf];
        this.curRun[curShuf] = tmp;
        int tmp2 = this.curRun[curShuf];
        for (int i2 = curShuf; i2 < 7; i2++) {
            this.curRun[i2] = this.curRun[i2 + 1];
        }
        this.curRun[7] = tmp2;
    }

    private void prodNew() {
        if (this.nLoop < 30) {
            this.curRun[0] = this.sArr[this.nLoop];
            this.nLoop++;
            return;
        }
        if (this.nLoop == 30) {
            setLim();
            this.sArr = randomizeIntArray(this.sArr);
            this.nLoop++;
        }
        int i = 0;
        while (i < 30) {
            if (this.cArr[this.sArr[i]] >= this.LIM_2 || !checkAlready(this.sArr[i]).booleanValue()) {
                i++;
            } else {
                this.curRun[0] = this.sArr[i];
                return;
            }
        }
    }

    private Boolean checkAlready(int $val) {
        for (int i : this.curRun) {
            if (i == $val) {
                return false;
            }
        }
        return true;
    }

    private void setLim() {
        this.LIM_2 = 501;
        if (this.unknownCount <= 15) {
            this.LIM_2 = 501;
        }
        if (this.unknownCount <= 5) {
            this.LIM_2 = 500;
        }
    }

    private Boolean checkGood() {
        if (this.countVar == 10 && this.unknownCount == 0) {
            return true;
        }
        if (this.countVar == 15 && this.unknownCount < 2) {
            return true;
        }
        for (int i : this.cArr) {
            if (i < this.LIM_2) {
                return false;
            }
        }
        return true;
    }

    public void miss() {
        this.missFlag = true;
        if (this.cArr[this.rVar] >= 498) {
            this.cArr[this.rVar] = 498;
        } else {
            if (this.cArr[this.rVar] > 4) {
                this.cArr[this.rVar] = 4;
            }
            if (this.cArr[this.rVar] == 0) {
                this.cArr[this.rVar] = 1;
                this.unknownCount++;
            }
        }
        sendMetrics();
    }

    private void initSdMode() {
        this.sdCount = 0;
        this.stCount = 30;
        this.sArr = randomizeIntArray(this.sArr);
        this.SDFLAG = true;
        this.SDMODE = true;
    }

    private void sendMetrics() {
        int mOut;
        int cTmp = this.cArr[this.rVar];
        if (cTmp > 10) {
            int cTmp2 = cTmp - 494;
        }
        int mOut2 = 0;
        int cnt = 0;
        for (int i = 0; i < 30; i++) {
            if (this.cArr[i] > 0) {
                int cTmp3 = this.cArr[i];
                if (cTmp3 > 10) {
                    cTmp3 -= 494;
                }
                mOut2 += cTmp3;
                cnt++;
            }
        }
        if (cnt == 0) {
            mOut = 0;
        } else {
            mOut = mOut2 / cnt;
        }
        int mOut3 = Math.round((float) (mOut * 10)) / 10;
    }

    private int getLastPlayVar(int $var) {
        if (this.pLast[$var] == 0) {
            return 0;
        }
        return (this.countVar + 1) - this.pLast[$var];
    }

    private int getUrg(int $var) {
        return getLastPlayVar($var) - this.cArr[$var];
    }

    public int getProg() {
        int stProg = this.stCount;
        if (stProg > 30) {
            stProg = 30;
        }
        return this.sdCount + stProg;
    }

    private void ranSeq() {
        ArrayList<Integer> tArr1 = new ArrayList<>();
        for (int i = 0; i < 30; i++) {
            tArr1.add(Integer.valueOf(i));
        }
        Collections.shuffle(tArr1);
        for (int i2 = 0; i2 < 30; i2++) {
            this.sArr[i2] = tArr1.get(i2).intValue();
        }
    }

    private int[] randomizeIntArray(int[] $inArr) {
        List<Integer> tList = new ArrayList<>();
        for (int i : $inArr) {
            tList.add(Integer.valueOf(i));
        }
        Collections.shuffle(tList);
        int[] outArr = new int[tList.size()];
        for (int i2 = 0; i2 < tList.size(); i2++) {
            outArr[i2] = tList.get(i2).intValue();
        }
        return outArr;
    }

    private int[] randomizeIntArrayInit(int[] $inArr, int $init) {
        List<Integer> tList = new ArrayList<>();
        for (int i : $inArr) {
            tList.add(Integer.valueOf(i));
        }
        Collections.shuffle(tList);
        List<Integer> tmpList = new ArrayList<>();
        tmpList.add(Integer.valueOf($init));
        int tmp = Collections.indexOfSubList(tList, tmpList);
        if (tmp != 0) {
            Collections.swap(tList, 0, tmp);
        }
        int[] outArr = new int[tList.size()];
        for (int i2 = 0; i2 < tList.size(); i2++) {
            outArr[i2] = tList.get(i2).intValue();
        }
        return outArr;
    }
}
