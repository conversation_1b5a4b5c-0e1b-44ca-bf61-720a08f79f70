package com.gamestolearnenglish.chineseinflow;

import android.content.Intent;
import android.graphics.Typeface;
import android.os.Bundle;
import android.support.v7.app.AppCompatActivity;
import android.view.ContextMenu;
import android.view.MenuItem;
import android.view.View;
import android.widget.Button;
import android.widget.TextView;
import database.DatabaseWrapper;
import java.util.Timer;
import java.util.TimerTask;

public class MenuActivity extends AppCompatActivity {
    private Button MenuLearnButton;
    private Button MenuPracticeButton;
    private Button MenuReviewButton;
    private Intent gotInt;
    private int grpBase;
    /* access modifiers changed from: private */
    public long learnProgress;
    /* access modifiers changed from: private */
    public TextView menuStat00;
    /* access modifiers changed from: private */
    public TextView menuStat01;
    /* access modifiers changed from: private */
    public TextView menuStat10;
    /* access modifiers changed from: private */
    public TextView menuStat11;
    /* access modifiers changed from: private */
    public TextView menuStat20;
    /* access modifiers changed from: private */
    public TextView menuStat21;
    private TextView menuTitleText;
    private Timer myTimer;
    /* access modifiers changed from: private */
    public long practiceProgress;
    /* access modifiers changed from: private */
    public long reviewProgress;
    private Runnable setTexts = new Runnable() {
        public void run() {
            MenuActivity.this.getStats();
            if (!MenuActivity.this.textsBool) {
                boolean unused = MenuActivity.this.textsBool = true;
                MenuActivity.this.menuStat00.setText("Total character views");
                MenuActivity.this.menuStat10.setText("Total time");
                MenuActivity.this.menuStat20.setText("Total progress");
                int minutesTime = (int) Math.floor((double) (MenuActivity.this.totalTime / 60));
                int secondsTime = ((int) MenuActivity.this.totalTime) % 60;
                MenuActivity.this.menuStat01.setText(Long.toString(MenuActivity.this.viewsCount));
                if (minutesTime > 0) {
                    MenuActivity.this.menuStat11.setText(minutesTime + "m " + secondsTime + "s");
                } else {
                    MenuActivity.this.menuStat11.setText(secondsTime + "s");
                }
                MenuActivity.this.menuStat21.setText(((MenuActivity.this.learnProgress * 4) + (MenuActivity.this.practiceProgress * 6) + (MenuActivity.this.reviewProgress * 6)) + "%");
                return;
            }
            boolean unused2 = MenuActivity.this.textsBool = false;
            MenuActivity.this.menuStat00.setText("Learn progress");
            MenuActivity.this.menuStat10.setText("Practice progress");
            MenuActivity.this.menuStat20.setText("Review progress");
            MenuActivity.this.menuStat01.setText((MenuActivity.this.learnProgress * 10) + "%");
            MenuActivity.this.menuStat11.setText(MenuActivity.this.practiceProgress + "/5");
            MenuActivity.this.menuStat21.setText(MenuActivity.this.reviewProgress + "/5");
        }
    };
    private int tarBase;
    /* access modifiers changed from: private */
    public boolean textsBool;
    /* access modifiers changed from: private */
    public long totalTime;
    /* access modifiers changed from: private */
    public long viewsCount;

    /* access modifiers changed from: protected */
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView((int) R.layout.activity_menu);
        setVolumeControlStream(3);
        initItems();
    }

    public void onResume() {
        super.onResume();
        this.myTimer = new Timer();
        this.myTimer.schedule(new TimerTask() {
            public void run() {
                MenuActivity.this.goSetTexts();
            }
        }, 0, 3000);
    }

    public void onPause() {
        super.onPause();
        if (this.myTimer != null) {
            this.myTimer.cancel();
            this.myTimer.purge();
            this.myTimer = null;
        }
    }

    /* access modifiers changed from: private */
    public void goSetTexts() {
        runOnUiThread(this.setTexts);
    }

    /* access modifiers changed from: private */
    public void getStats() {
        DatabaseWrapper myWrap = new DatabaseWrapper(this, this.grpBase, this.tarBase);
        long[] myStats = myWrap.getStats();
        myWrap.closeDatabase();
        this.totalTime = myStats[0];
        this.viewsCount = myStats[1];
        this.learnProgress = myStats[2];
        this.practiceProgress = myStats[3];
        this.reviewProgress = myStats[4];
    }

    private void initItems() {
        this.gotInt = getIntent();
        this.tarBase = this.gotInt.getIntExtra("contentSelected", 0);
        this.grpBase = this.gotInt.getIntExtra("groupSelected", 0);
        getStats();
        this.textsBool = false;
        this.menuTitleText = (TextView) findViewById(R.id.activityMenuTitleText);
        this.menuStat00 = (TextView) findViewById(R.id.activityMenuStat00);
        this.menuStat01 = (TextView) findViewById(R.id.activityMenuStat01);
        this.menuStat10 = (TextView) findViewById(R.id.activityMenuStat10);
        this.menuStat11 = (TextView) findViewById(R.id.activityMenuStat11);
        this.menuStat20 = (TextView) findViewById(R.id.activityMenuStat20);
        this.menuStat21 = (TextView) findViewById(R.id.activityMenuStat21);
        this.MenuReviewButton = (Button) findViewById(R.id.activityMenuReviewButton);
        this.MenuLearnButton = (Button) findViewById(R.id.activityMenuLearnButton);
        this.MenuPracticeButton = (Button) findViewById(R.id.activityMenuPracticeButton);
        this.menuTitleText.setTypeface(Typeface.createFromAsset(getAssets(), "AmaBold.ttf"));
        this.menuStat00.setTypeface(Typeface.createFromAsset(getAssets(), "AmaBold.ttf"));
        this.menuStat01.setTypeface(Typeface.createFromAsset(getAssets(), "AmaBold.ttf"));
        this.menuStat10.setTypeface(Typeface.createFromAsset(getAssets(), "AmaBold.ttf"));
        this.menuStat11.setTypeface(Typeface.createFromAsset(getAssets(), "AmaBold.ttf"));
        this.menuStat20.setTypeface(Typeface.createFromAsset(getAssets(), "AmaBold.ttf"));
        this.menuStat21.setTypeface(Typeface.createFromAsset(getAssets(), "AmaBold.ttf"));
        this.MenuReviewButton.setTypeface(Typeface.createFromAsset(getAssets(), "AmaBold.ttf"));
        this.MenuLearnButton.setTypeface(Typeface.createFromAsset(getAssets(), "AmaBold.ttf"));
        this.MenuPracticeButton.setTypeface(Typeface.createFromAsset(getAssets(), "AmaBold.ttf"));
        this.menuTitleText.setText("Characters   " + (this.tarBase + 1 + (this.grpBase * 150)) + " - " + (this.tarBase + 30 + (this.grpBase * 150)));
        registerForContextMenu(this.MenuLearnButton);
        registerForContextMenu(this.MenuPracticeButton);
        registerForContextMenu(this.MenuReviewButton);
    }

    public void reviewClick(View v) {
        Intent myInt = new Intent(getApplicationContext(), ReviewActivity.class);
        myInt.putExtra("contentSelected", this.tarBase);
        myInt.putExtra("groupSelected", this.grpBase);
        myInt.setFlags(65536);
        startActivity(myInt);
        overridePendingTransition(0, 0);
        finish();
    }

    public void learnClick(View v) {
        Intent myInt = new Intent(getApplicationContext(), LearnActivity.class);
        myInt.putExtra("contentSelected", this.tarBase);
        myInt.putExtra("groupSelected", this.grpBase);
        myInt.setFlags(65536);
        startActivity(myInt);
        overridePendingTransition(0, 0);
        finish();
    }

    public void practiceClick(View v) {
        Intent myInt = new Intent(getApplicationContext(), PracticeActivity.class);
        myInt.putExtra("contentSelected", this.tarBase);
        myInt.putExtra("groupSelected", this.grpBase);
        myInt.setFlags(65536);
        startActivity(myInt);
        overridePendingTransition(0, 0);
        finish();
    }

    public void onCreateContextMenu(ContextMenu m, View v, ContextMenu.ContextMenuInfo menuInfo) {
        super.onCreateContextMenu(m, v, menuInfo);
        m.setHeaderTitle("What to do?");
        switch (v.getId()) {
            case R.id.activityMenuLearnButton /*2131558565*/:
                m.add(0, 0, 0, "Start");
                m.add(0, 3, 0, "Reset learn data for this set");
                break;
            case R.id.activityMenuPracticeButton /*2131558566*/:
                m.add(0, 1, 0, "Start practice");
                m.add(0, 4, 0, "Reset practice data for this set");
                break;
            case R.id.activityMenuReviewButton /*2131558567*/:
                m.add(0, 2, 0, "Start review");
                m.add(0, 5, 0, "Reset review data for this set");
                break;
        }
        m.add(0, 6, 0, "Reset all data for this set");
    }

    public boolean onContextItemSelected(MenuItem item) {
        switch (item.getItemId()) {
            case 0:
                learnClick((View) null);
                break;
            case 1:
                practiceClick((View) null);
                break;
            case 2:
                reviewClick((View) null);
                break;
            case 3:
                dataReseter(0);
                break;
            case 4:
                dataReseter(1);
                break;
            case 5:
                dataReseter(2);
                break;
            case 6:
                dataReseter(3);
                break;
        }
        return true;
    }

    private void dataReseter(int $switchInt) {
        DatabaseWrapper myWrap = new DatabaseWrapper(this, this.grpBase, this.tarBase);
        switch ($switchInt) {
            case 0:
                myWrap.resetData(false, true, false, false);
                break;
            case 1:
                myWrap.resetData(false, false, true, false);
                break;
            case 2:
                myWrap.resetData(false, false, false, true);
                break;
            case 3:
                myWrap.resetData(true, true, true, true);
                break;
        }
        myWrap.closeDatabase();
        if (!this.textsBool) {
            this.textsBool = true;
        } else {
            this.textsBool = false;
        }
        runOnUiThread(this.setTexts);
    }

    /* access modifiers changed from: protected */
    public void onStop() {
        super.onStop();
    }
}
