package com.gamestolearnenglish.chineseinflow;

import android.content.Intent;
import android.content.SharedPreferences;
import android.graphics.Typeface;
import android.net.Uri;
import android.os.Bundle;
import android.support.v7.app.AppCompatActivity;
import android.view.View;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.TextView;

public class EndActivity extends AppCompatActivity {
    private static final String APP_PNAME = "com.gamestolearnenglish.chineseinflow";
    private int actType;
    private TextView endInfoText;
    private TextView endSummaryText0;
    private TextView endSummaryText1;
    private TextView endTitleText;
    private int grpBase;
    private Intent myInt;
    private int tarBase;

    /* access modifiers changed from: protected */
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView((int) R.layout.activity_end);
        setVolumeControlStream(3);
        setFields();
        this.myInt = getIntent();
        this.tarBase = this.myInt.getIntExtra("contentSelected", 0);
        this.grpBase = this.myInt.getIntExtra("groupSelected", 0);
        this.actType = this.myInt.getIntExtra("activity", 0);
        if (this.actType == 2) {
            showReview();
        }
        if (this.actType == 1) {
            showPractice();
        }
        if (this.actType == 0) {
            showLearn();
        }
        setupRate();
    }

    private void setFields() {
        this.endTitleText = (TextView) findViewById(R.id.activityEndTitleText);
        this.endSummaryText0 = (TextView) findViewById(R.id.activityEndSummaryText0);
        this.endSummaryText1 = (TextView) findViewById(R.id.activityEndSummaryText1);
        this.endInfoText = (TextView) findViewById(R.id.activityEndInfoText);
        this.endInfoText.setTypeface(Typeface.createFromAsset(getAssets(), "AmaBold.ttf"));
        this.endTitleText.setTypeface(Typeface.createFromAsset(getAssets(), "AmaBold.ttf"));
        this.endSummaryText0.setTypeface(Typeface.createFromAsset(getAssets(), "AmaBold.ttf"));
        this.endSummaryText1.setTypeface(Typeface.createFromAsset(getAssets(), "AmaBold.ttf"));
        ((Button) findViewById(R.id.activityEndFeedbackButton)).setTypeface(Typeface.createFromAsset(getAssets(), "AmaBold.ttf"));
        ((Button) findViewById(R.id.activityEndAgainButton)).setTypeface(Typeface.createFromAsset(getAssets(), "AmaBold.ttf"));
        ((Button) findViewById(R.id.activityEndBackButton)).setTypeface(Typeface.createFromAsset(getAssets(), "AmaBold.ttf"));
    }

    private void showLearn() {
        this.endTitleText.setText("Completed");
        this.endInfoText.setText(R.string.end_activity_info_learn);
        int misses = this.myInt.getIntExtra("misses", 0);
        long myTime = this.myInt.getLongExtra("duration", 0);
        int minutesTime = (int) Math.floor((double) (myTime / 60));
        int secondsTime = ((int) myTime) % 60;
        this.endSummaryText0.setText("Misses " + misses);
        if (minutesTime > 0) {
            this.endSummaryText1.setText("Time spent " + minutesTime + "m " + secondsTime + "s");
        } else {
            this.endSummaryText1.setText("Time spent " + secondsTime + "s");
        }
    }

    private void showPractice() {
        int outcome = this.myInt.getIntExtra("outcome", 0);
        int myProg = this.myInt.getIntExtra("myProg", 0);
        long myTime = this.myInt.getLongExtra("duration", 0);
        int minutesTime = (int) Math.floor((double) (myTime / 60));
        int secondsTime = ((int) myTime) % 60;
        this.endTitleText.setText("Practice Completed");
        switch (outcome) {
            case 0:
                this.endInfoText.setText(R.string.end_activity_info_practice_complete);
                break;
            case 1:
                this.endInfoText.setText(R.string.end_activity_info_practice_timeout);
                this.endTitleText.setText("Time Over");
                break;
            case 2:
                this.endInfoText.setText(R.string.end_activity_info_practice_sdtimeout);
                break;
            case 3:
                this.endInfoText.setText(R.string.end_activity_info_practice_sdmiss);
                break;
        }
        this.endSummaryText0.setText("Completed " + myProg + "/60");
        if (minutesTime > 0) {
            this.endSummaryText1.setText("Time spent " + minutesTime + "m " + secondsTime + "s");
        } else {
            this.endSummaryText1.setText("Time spent " + secondsTime + "s");
        }
    }

    private void showReview() {
        this.endTitleText.setText("Review Completed");
        this.endInfoText.setText(R.string.end_activity_info_review);
        int repeats = this.myInt.getIntExtra("repeats", 0);
        long myTime = this.myInt.getLongExtra("duration", 0);
        int minutesTime = (int) Math.floor((double) (myTime / 60));
        int secondsTime = ((int) myTime) % 60;
        this.endSummaryText0.setText("Repeats " + repeats);
        if (minutesTime > 0) {
            this.endSummaryText1.setText("Time spent " + minutesTime + "m " + secondsTime + "s");
        } else {
            this.endSummaryText1.setText("Time spent " + secondsTime + "s");
        }
    }

    public void backClick(View v) {
        overridePendingTransition(0, 0);
        finish();
    }

    public void againClick(View v) {
        if (this.actType == 0) {
            Intent newInt = new Intent(getApplicationContext(), LearnActivity.class);
            newInt.putExtra("contentSelected", this.tarBase);
            newInt.putExtra("groupSelected", this.grpBase);
            newInt.setFlags(65536);
            startActivity(newInt);
        }
        if (this.actType == 1) {
            Intent newInt2 = new Intent(getApplicationContext(), PracticeActivity.class);
            newInt2.putExtra("contentSelected", this.tarBase);
            newInt2.putExtra("groupSelected", this.grpBase);
            newInt2.setFlags(65536);
            startActivity(newInt2);
        }
        if (this.actType == 2) {
            Intent newInt3 = new Intent(getApplicationContext(), ReviewActivity.class);
            newInt3.putExtra("contentSelected", this.tarBase);
            newInt3.putExtra("groupSelected", this.grpBase);
            newInt3.setFlags(65536);
            startActivity(newInt3);
        }
        overridePendingTransition(0, 0);
        finish();
    }

    private void setupRate() {
        if (getSharedPreferences("apprater", 0).getBoolean("showRated", true)) {
            ((LinearLayout) findViewById(R.id.activityEndRateLayout)).setVisibility(0);
        }
    }

    public void feedbackClick(View v) {
        SharedPreferences.Editor editor = getSharedPreferences("apprater", 0).edit();
        editor.putBoolean("showRated", false);
        editor.commit();
        try {
            startActivity(new Intent("android.intent.action.VIEW", Uri.parse("market://details?id=com.gamestolearnenglish.chineseinflow")));
        } catch (Exception e) {
        }
    }
}
