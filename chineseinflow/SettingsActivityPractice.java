package com.gamestolearnenglish.chineseinflow;

import android.content.Intent;
import android.graphics.Typeface;
import android.os.Bundle;
import android.support.v7.app.AppCompatActivity;
import android.view.View;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.LinearLayout;
import android.widget.TextView;

public class SettingsActivityPractice extends AppCompatActivity {
    private View.OnClickListener backListener = new View.OnClickListener() {
        public void onClick(View arg0) {
            SettingsActivityPractice.this.finish();
        }
    };
    /* access modifiers changed from: private */
    public boolean delayTextBool;
    private CheckBox delayTextBox;
    /* access modifiers changed from: private */
    public Intent myData = new Intent();
    private View.OnClickListener okListener = new View.OnClickListener() {
        public void onClick(View arg0) {
            SettingsActivityPractice.this.updateBooleans();
            SettingsActivityPractice.this.myData.putExtra("altBool", true);
            SettingsActivityPractice.this.myData.putExtra("playAudBool", SettingsActivityPractice.this.playAudBool);
            SettingsActivityPractice.this.myData.putExtra("showPinyinBool", SettingsActivityPractice.this.showPinyinBool);
            SettingsActivityPractice.this.myData.putExtra("showEnglishBool", SettingsActivityPractice.this.showEnglishBool);
            SettingsActivityPractice.this.myData.putExtra("delayTextBool", SettingsActivityPractice.this.delayTextBool);
            SettingsActivityPractice.this.setResult(0, SettingsActivityPractice.this.myData);
            SettingsActivityPractice.this.finish();
        }
    };
    /* access modifiers changed from: private */
    public boolean playAudBool;
    private CheckBox playAudBox;
    /* access modifiers changed from: private */
    public boolean showEnglishBool;
    private CheckBox showEnglishBox;
    /* access modifiers changed from: private */
    public boolean showPinyinBool;
    private CheckBox showPinyinBox;

    /* access modifiers changed from: protected */
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView((int) R.layout.activity_settings_practice);
        setVolumeControlStream(3);
        Intent gotInt = getIntent();
        this.playAudBool = gotInt.getBooleanExtra("playAudBool", false);
        this.showPinyinBool = gotInt.getBooleanExtra("showPinyinBool", false);
        this.showEnglishBool = gotInt.getBooleanExtra("showEnglishBool", false);
        this.delayTextBool = gotInt.getBooleanExtra("delayTextBool", false);
        this.playAudBox = (CheckBox) findViewById(R.id.playAudBoxCheck);
        this.showPinyinBox = (CheckBox) findViewById(R.id.showPinyinBoxCheck);
        this.showEnglishBox = (CheckBox) findViewById(R.id.showEnglishBoxCheck);
        this.delayTextBox = (CheckBox) findViewById(R.id.delayTextBoxCheck);
        setBoxesDefault();
        ((LinearLayout) findViewById(R.id.frontPart)).setOnClickListener((View.OnClickListener) null);
        ((Button) findViewById(R.id.settingsOkBut)).setOnClickListener(this.okListener);
        TextView[] tViews = new TextView[7];
        for (int i = 0; i < 5; i++) {
            tViews[i] = (TextView) findViewById(getResources().getIdentifier("practiceSettingsText" + i, "id", getPackageName()));
            tViews[i].setTypeface(Typeface.createFromAsset(getAssets(), "AmaBold.ttf"));
        }
        this.myData.putExtra("altBool", false);
        setResult(0, this.myData);
    }

    public void checkClick(View v) {
    }

    private void setBoxesDefault() {
        if (this.playAudBool) {
            this.playAudBox.setChecked(true);
        }
        if (this.showPinyinBool) {
            this.showPinyinBox.setChecked(true);
        }
        if (this.showEnglishBool) {
            this.showEnglishBox.setChecked(true);
        }
        if (this.delayTextBool) {
            this.delayTextBox.setChecked(true);
        }
    }

    /* access modifiers changed from: private */
    public void updateBooleans() {
        boolean z;
        boolean z2;
        boolean z3;
        boolean z4 = true;
        if (this.playAudBox.isChecked()) {
            z = true;
        } else {
            z = false;
        }
        this.playAudBool = z;
        if (this.showPinyinBox.isChecked()) {
            z2 = true;
        } else {
            z2 = false;
        }
        this.showPinyinBool = z2;
        if (this.showEnglishBox.isChecked()) {
            z3 = true;
        } else {
            z3 = false;
        }
        this.showEnglishBool = z3;
        if (!this.delayTextBox.isChecked()) {
            z4 = false;
        }
        this.delayTextBool = z4;
    }
}
