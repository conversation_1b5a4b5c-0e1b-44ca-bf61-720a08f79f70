package com.gamestolearnenglish.chineseinflow;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Iterator;
import java.util.List;

public class MetricsLearn {
    public boolean FINISHED;
    private int countVar;
    private int curItemInd;
    private ArrayList<Integer> curItemsList;
    public long delayValue;
    public int itemDisplayNumber;
    public int itemDisplayType;
    private int[] itemOrderArr;
    private int learnItemsInt;
    private ArrayList<LearnItem> learnItemsList;
    public boolean missedFlag;
    private Comparator<LearnItem> myComp;
    private Comparator<LearnItem> myCompInd;
    private int newItemsLength;
    private ArrayList<Integer> repeatList;
    private ArrayList<Integer> returnItemsList;
    private boolean saveNowFlag;
    private int stageCount;
    private boolean stageFourBool;
    private int stageLength;
    private boolean stageSevenBool1;
    private boolean stageSevenBool2;
    private int stageVar;

    public MetricsLearn() {
        this.returnItemsList = new ArrayList<>();
        this.curItemsList = new ArrayList<>();
        this.learnItemsList = new ArrayList<>();
        this.repeatList = new ArrayList<>();
        this.itemOrderArr = new int[30];
        this.learnItemsInt = 0;
        this.newItemsLength = 3;
        this.delayValue = 100;
        this.missedFlag = false;
        this.saveNowFlag = false;
        this.countVar = 0;
        this.stageFourBool = true;
        this.stageSevenBool1 = true;
        this.stageSevenBool2 = true;
        this.myComp = new Comparator<LearnItem>() {
            public int compare(LearnItem i0, LearnItem i1) {
                int com2 = Integer.valueOf(i0.curLevel).compareTo(Integer.valueOf(i1.curLevel));
                if (com2 == 0) {
                    return Integer.valueOf(i0.lastShown).compareTo(Integer.valueOf(i1.lastShown));
                }
                return com2;
            }
        };
        this.myCompInd = new Comparator<LearnItem>() {
            public int compare(LearnItem i0, LearnItem i1) {
                return Integer.valueOf(i0.ind).compareTo(Integer.valueOf(i1.ind));
            }
        };
        this.itemOrderArr = ranSeq();
    }

    public void init() {
        for (int i = 0; i < 30; i++) {
            this.learnItemsList.add(new LearnItem(i, 0, 15));
        }
        this.stageVar = 1;
        addNewItems();
        initNewStage();
    }

    public void reinit(int $stageVar, int[] $levels) {
        this.stageVar = $stageVar;
        for (int i = 0; i < 30; i++) {
            this.learnItemsList.add(new LearnItem(i, 0, $levels[i]));
        }
        for (int i2 : $levels) {
            if (i2 != 15) {
                this.learnItemsInt++;
            }
        }
        if (this.learnItemsInt > 3) {
            this.stageFourBool = false;
        }
        if (this.learnItemsInt > 17) {
            this.stageSevenBool1 = false;
        }
        if (this.stageVar == 7) {
            this.stageSevenBool1 = false;
        }
        if (this.learnItemsInt > 25) {
            this.stageSevenBool2 = false;
        }
        if (this.learnItemsInt > 22 && this.stageVar == 7) {
            this.stageSevenBool2 = false;
        }
        initNewStage();
    }

    public void setRestartStageCount(int $stageCount) {
        this.stageCount = $stageCount;
        for (int i = 0; i < this.curItemsList.size(); i++) {
            if (i < this.stageCount) {
                this.learnItemsList.get(this.curItemsList.get(i).intValue()).curLevel++;
            }
        }
    }

    public ArrayList<Integer> newRound() {
        this.countVar++;
        if (this.stageCount <= this.curItemsList.size()) {
            this.curItemInd = this.curItemsList.get(this.stageCount).intValue();
        } else {
            this.stageCount = 0;
            this.curItemInd = this.curItemsList.get(this.stageCount).intValue();
        }
        this.returnItemsList = randomizeListArrayInit(this.curItemsList, this.curItemInd);
        return this.returnItemsList;
    }

    public ArrayList<MyPair> updateStats() {
        this.learnItemsList.get(this.curItemInd).lastShown = this.countVar;
        if (!this.missedFlag) {
            if (this.learnItemsList.get(this.curItemInd).curLevel < this.stageVar + 1) {
                this.learnItemsList.get(this.curItemInd).curLevel = this.stageVar + 1;
            }
            this.stageCount++;
        } else {
            this.repeatList.add(Integer.valueOf(this.curItemInd));
            this.stageLength = this.curItemsList.size();
            randomizeFromPoint(this.curItemsList, this.stageCount);
        }
        this.missedFlag = false;
        ArrayList<MyPair> retCurItemsList = new ArrayList<>();
        if (stageCompleteCheck()) {
            this.stageVar++;
            if (this.stageVar > 9) {
                this.stageVar = 9;
            }
            if (this.stageVar == 7 || this.stageVar == 8) {
                resetLevels();
            }
            Iterator<Integer> it = this.curItemsList.iterator();
            while (it.hasNext()) {
                int i = it.next().intValue();
                retCurItemsList.add(new MyPair(i, this.learnItemsList.get(i).curLevel));
            }
            if (this.stageVar == 4 && this.stageFourBool) {
                resetToStageOne();
            }
            if (this.stageVar == 7 && !stageSevenCheck()) {
                resetToStageOne();
            }
            if (this.stageVar == 8 && this.learnItemsInt < 30) {
                resetToStageOne();
            }
            if (this.stageVar >= 9) {
                this.FINISHED = true;
            } else {
                initNewStage();
            }
        }
        return retCurItemsList;
    }

    private void learnAgainReset() {
        for (int i = 0; i < 30; i++) {
            this.learnItemsList.get(i).curLevel = 8;
        }
    }

    private void resetLevels() {
        Iterator<Integer> it = this.repeatList.iterator();
        while (it.hasNext()) {
            int i = it.next().intValue();
            if (this.learnItemsList.get(i).curLevel == 2) {
                this.learnItemsList.get(i).curLevel = 1;
            }
            if (this.learnItemsList.get(i).curLevel == 4) {
                this.learnItemsList.get(i).curLevel = 2;
            }
            if (this.learnItemsList.get(i).curLevel == 5) {
                this.learnItemsList.get(i).curLevel = 4;
            }
            if (this.learnItemsList.get(i).curLevel == 7) {
                this.learnItemsList.get(i).curLevel = 5;
            }
        }
        this.repeatList.clear();
    }

    private void resetToStageOne() {
        this.stageFourBool = false;
        this.stageVar = 1;
        addNewItems();
    }

    private boolean stageSevenCheck() {
        if (this.learnItemsInt > 14 && this.stageSevenBool1) {
            this.stageSevenBool1 = false;
            return true;
        } else if (this.learnItemsInt > 22 && this.stageSevenBool2) {
            this.stageSevenBool2 = false;
            return true;
        } else if (this.learnItemsInt <= 27) {
            return false;
        } else {
            return true;
        }
    }

    private boolean stageCompleteCheck() {
        if (this.stageCount >= this.stageLength) {
            return true;
        }
        return false;
    }

    private void addNewItems() {
        int loopVar = 0;
        int addedInt = 0;
        this.saveNowFlag = true;
        while (addedInt < this.newItemsLength) {
            if (this.learnItemsList.get(this.itemOrderArr[loopVar]).curLevel == 15) {
                this.learnItemsList.get(this.itemOrderArr[loopVar]).curLevel = 1;
                addedInt++;
                this.learnItemsInt++;
            }
            loopVar++;
            if (loopVar == 30) {
                addedInt = this.newItemsLength;
            }
        }
    }

    private void initNewStage() {
        this.itemDisplayType = 0;
        switch (this.stageVar) {
            case 1:
                fillCurItemsList(3);
                this.itemDisplayNumber = 1;
                break;
            case 2:
                fillCurItemsList(3);
                this.itemDisplayNumber = 3;
                break;
            case 3:
                fillCurItemsList(3);
                this.itemDisplayNumber = 3;
                this.itemDisplayType = 1;
                break;
            case 4:
                fillCurItemsList(5);
                this.itemDisplayNumber = 5;
                break;
            case 5:
                fillCurItemsList(5);
                this.itemDisplayNumber = 5;
                break;
            case 6:
                fillCurItemsList(5);
                this.itemDisplayNumber = 5;
                this.itemDisplayType = 1;
                break;
            case 7:
                fillCurItemsList(15);
                this.itemDisplayNumber = 8;
                break;
            case 8:
                fillCurItemsList(30);
                this.itemDisplayNumber = 1;
                this.itemDisplayType = 2;
                break;
            case 9:
                learnAgainReset();
                fillCurItemsList(30);
                this.itemDisplayNumber = 1;
                this.itemDisplayType = 2;
                break;
        }
        this.stageLength = this.curItemsList.size();
        this.stageCount = 0;
        do {
            Collections.shuffle(this.curItemsList);
        } while (this.curItemsList.get(0).intValue() == this.curItemInd);
    }

    private void fillCurItemsList(int $i) {
        Collections.sort(this.learnItemsList, this.myComp);
        this.curItemsList.clear();
        for (int i = 0; i < 30; i++) {
            if (this.learnItemsList.get(i).curLevel == this.stageVar) {
                this.curItemsList.add(Integer.valueOf(this.learnItemsList.get(i).ind));
            }
        }
        int lVar = 0;
        while (this.curItemsList.size() < $i && lVar < 30) {
            if (this.learnItemsList.get(lVar).curLevel != this.stageVar) {
                this.curItemsList.add(Integer.valueOf(this.learnItemsList.get(lVar).ind));
            }
            lVar++;
        }
        Collections.sort(this.learnItemsList, this.myCompInd);
    }

    public int getStageVar() {
        return this.stageVar;
    }

    public int getStageCount() {
        return this.stageCount;
    }

    public boolean checkSaveNow() {
        if (!this.saveNowFlag) {
            return false;
        }
        this.saveNowFlag = false;
        return true;
    }

    public ArrayList<MyPair> getSaveNowPairs() {
        ArrayList<MyPair> savePairs = new ArrayList<>();
        Iterator<Integer> it = this.curItemsList.iterator();
        while (it.hasNext()) {
            savePairs.add(new MyPair(it.next().intValue(), 1));
        }
        return savePairs;
    }

    private ArrayList<Integer> randomizeListArrayInit(ArrayList<Integer> $inArr, int $init) {
        ArrayList<Integer> tList = new ArrayList<>();
        Iterator<Integer> it = $inArr.iterator();
        while (it.hasNext()) {
            tList.add(it.next());
        }
        Collections.shuffle(tList);
        List<Integer> tmpList = new ArrayList<>();
        tmpList.add(Integer.valueOf($init));
        int tmp = Collections.indexOfSubList(tList, tmpList);
        if (tmp != 0) {
            Collections.swap(tList, 0, tmp);
        }
        ArrayList<Integer> outArr = new ArrayList<>();
        for (int i = 0; i < tList.size(); i++) {
            outArr.add(tList.get(i));
        }
        return outArr;
    }

    private void randomizeFromPoint(ArrayList<Integer> $inArr, int $int) {
        ArrayList<Integer> tList = new ArrayList<>();
        for (int i = $int; i < $inArr.size(); i++) {
            tList.add($inArr.get(i));
        }
        int tmp = $inArr.get($int).intValue();
        if (tList.size() > 1) {
            while (tmp == tList.get(0).intValue()) {
                Collections.shuffle(tList);
            }
        }
        for (int i2 = $int; i2 < $inArr.size(); i2++) {
            $inArr.set(i2, tList.get(i2 - $int));
        }
    }

    private int[] ranSeq() {
        int[] outArr = new int[30];
        ArrayList<Integer> tArr1 = new ArrayList<>();
        for (int i = 0; i < 30; i++) {
            tArr1.add(Integer.valueOf(i));
        }
        Collections.shuffle(tArr1);
        for (int i2 = 0; i2 < 30; i2++) {
            outArr[i2] = tArr1.get(i2).intValue();
        }
        return outArr;
    }

    public class LearnItem {
        int curLevel;
        int ind;
        int lastShown;

        public LearnItem(int $ind, int $lastShown, int $curLevel) {
            this.ind = $ind;
            this.lastShown = $lastShown;
            this.curLevel = $curLevel;
        }
    }

    public class MyPair {
        private final int key;
        private final int value;

        public MyPair(int $key, int $value) {
            this.key = $key;
            this.value = $value;
        }

        public int returnKey() {
            return this.key;
        }

        public int returnValue() {
            return this.value;
        }
    }
}
