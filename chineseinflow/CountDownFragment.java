package com.gamestolearnenglish.chineseinflow;

import android.os.Bundle;
import android.support.v4.app.Fragment;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ProgressBar;
import java.util.Timer;
import java.util.TimerTask;

public class CountDownFragment extends Fragment {
    private ProgressBar myBar;
    private int myInt;
    private Timer myTimer;
    private double timerInc;
    private Boolean timerRunning;

    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_count_down, container, false);
        this.myInt = 300;
        this.timerInc = 0.8d;
        this.myBar = (ProgressBar) view.findViewById(R.id.activityCountDownProgressBar1);
        this.myBar.setProgress(this.myInt);
        this.timerRunning = false;
        return view;
    }

    public void onResume() {
        super.onResume();
        this.myTimer = new Timer();
        this.myTimer.schedule(new TimerTask() {
            public void run() {
                CountDownFragment.this.updateTime();
            }
        }, 0, 100);
    }

    public void onPause() {
        super.onPause();
        if (this.myTimer != null) {
            this.myTimer.cancel();
            this.myTimer.purge();
            this.myTimer = null;
        }
    }

    public void altTimerInc(boolean $sdMode) {
        if ($sdMode) {
            this.timerInc += 0.2d;
            if (this.timerInc > 3.4d) {
                this.timerInc = 3.4d;
                return;
            }
            return;
        }
        this.timerInc += 0.05d;
        if (this.timerInc > 2.0d) {
            this.timerInc = 2.0d;
        }
    }

    public void startTimer() {
        this.timerRunning = true;
    }

    public void stopTimer() {
        this.timerRunning = false;
    }

    public void addTime(int $add) {
        this.myInt += $add;
        if (this.myInt > 300) {
            this.myInt = 300;
        }
    }

    public void resetTimer() {
        this.myInt = 300;
    }

    /* access modifiers changed from: private */
    public void updateTime() {
        if (this.timerRunning.booleanValue()) {
            this.myInt = (int) (((double) this.myInt) - this.timerInc);
            if (this.myInt <= 0) {
                this.myInt = 0;
                this.timerRunning = false;
                ((PracticeActivity) getActivity()).timeOut();
            }
            this.myBar.setProgress(this.myInt);
        }
    }
}
