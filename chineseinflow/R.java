package com.gamestolearnenglish.chineseinflow;

public final class R {

    public static final class anim {
        public static final int abc_fade_in = 2131034112;
        public static final int abc_fade_out = 2131034113;
        public static final int abc_grow_fade_in_from_bottom = 2131034114;
        public static final int abc_popup_enter = 2131034115;
        public static final int abc_popup_exit = 2131034116;
        public static final int abc_shrink_fade_out_from_bottom = 2131034117;
        public static final int abc_slide_in_bottom = 2131034118;
        public static final int abc_slide_in_top = 2131034119;
        public static final int abc_slide_out_bottom = 2131034120;
        public static final int abc_slide_out_top = 2131034121;
        public static final int design_bottom_sheet_slide_in = 2131034122;
        public static final int design_bottom_sheet_slide_out = 2131034123;
        public static final int design_fab_in = 2131034124;
        public static final int design_fab_out = 2131034125;
        public static final int design_snackbar_in = 2131034126;
        public static final int design_snackbar_out = 2131034127;
        public static final int eight_one_trans = 2131034128;
        public static final int fade_in_anim = 2131034129;
        public static final int fade_out_anim = 2131034130;
        public static final int five_eight_trans = 2131034131;
        public static final int five_eight_trans_top = 2131034132;
        public static final int five_one_anim = 2131034133;
        public static final int one_three_anim = 2131034134;
        public static final int three_five_anim = 2131034135;
        public static final int three_five_trans_top = 2131034136;
        public static final int three_one_anim = 2131034137;
        public static final int three_one_er_anim = 2131034138;
        public static final int three_one_ling_anim = 2131034139;
    }

    public static final class animator {
        public static final int design_appbar_state_list_animator = 2131099648;
    }

    public static final class attr {
        public static final int actionBarDivider = 2130772082;
        public static final int actionBarItemBackground = 2130772083;
        public static final int actionBarPopupTheme = 2130772076;
        public static final int actionBarSize = 2130772081;
        public static final int actionBarSplitStyle = 2130772078;
        public static final int actionBarStyle = 2130772077;
        public static final int actionBarTabBarStyle = 2130772072;
        public static final int actionBarTabStyle = 2130772071;
        public static final int actionBarTabTextStyle = 2130772073;
        public static final int actionBarTheme = 2130772079;
        public static final int actionBarWidgetTheme = 2130772080;
        public static final int actionButtonStyle = 2130772109;
        public static final int actionDropDownStyle = 2130772105;
        public static final int actionLayout = 2130772228;
        public static final int actionMenuTextAppearance = 2130772084;
        public static final int actionMenuTextColor = 2130772085;
        public static final int actionModeBackground = 2130772088;
        public static final int actionModeCloseButtonStyle = 2130772087;
        public static final int actionModeCloseDrawable = 2130772090;
        public static final int actionModeCopyDrawable = 2130772092;
        public static final int actionModeCutDrawable = 2130772091;
        public static final int actionModeFindDrawable = 2130772096;
        public static final int actionModePasteDrawable = 2130772093;
        public static final int actionModePopupWindowStyle = 2130772098;
        public static final int actionModeSelectAllDrawable = **********;
        public static final int actionModeShareDrawable = **********;
        public static final int actionModeSplitBackground = **********;
        public static final int actionModeStyle = **********;
        public static final int actionModeWebSearchDrawable = **********;
        public static final int actionOverflowButtonStyle = **********;
        public static final int actionOverflowMenuStyle = **********;
        public static final int actionProviderClass = **********;
        public static final int actionViewClass = **********;
        public static final int activityChooserViewStyle = **********;
        public static final int alertDialogButtonGroupStyle = **********;
        public static final int alertDialogCenterButtons = **********;
        public static final int alertDialogStyle = **********;
        public static final int alertDialogTheme = **********;
        public static final int allowStacking = **********;
        public static final int alpha = **********;
        public static final int arrowHeadLength = **********;
        public static final int arrowShaftLength = **********;
        public static final int autoCompleteTextViewStyle = **********;
        public static final int background = **********;
        public static final int backgroundSplit = **********;
        public static final int backgroundStacked = **********;
        public static final int backgroundTint = **********;
        public static final int backgroundTintMode = 2130772325;
        public static final int barLength = 2130772215;
        public static final int behavior_autoHide = 2130772222;
        public static final int behavior_hideable = 2130772175;
        public static final int behavior_overlapTop = 2130772248;
        public static final int behavior_peekHeight = 2130772174;
        public static final int behavior_skipCollapsed = 2130772176;
        public static final int borderWidth = 2130772220;
        public static final int borderlessButtonStyle = 2130772114;
        public static final int bottomSheetDialogTheme = 2130772206;
        public static final int bottomSheetStyle = 2130772207;
        public static final int buttonBarButtonStyle = 2130772111;
        public static final int buttonBarNegativeButtonStyle = 2130772158;
        public static final int buttonBarNeutralButtonStyle = 2130772159;
        public static final int buttonBarPositiveButtonStyle = 2130772157;
        public static final int buttonBarStyle = 2130772110;
        public static final int buttonGravity = 2130772313;
        public static final int buttonPanelSideLayout = 2130772045;
        public static final int buttonStyle = 2130772161;
        public static final int buttonStyleSmall = 2130772162;
        public static final int buttonTint = 2130772196;
        public static final int buttonTintMode = 2130772197;
        public static final int checkboxStyle = 2130772163;
        public static final int checkedTextViewStyle = 2130772164;
        public static final int closeIcon = 2130772253;
        public static final int closeItemLayout = 2130772042;
        public static final int collapseContentDescription = 2130772315;
        public static final int collapseIcon = 2130772314;
        public static final int collapsedTitleGravity = 2130772190;
        public static final int collapsedTitleTextAppearance = 2130772184;
        public static final int color = 2130772209;
        public static final int colorAccent = 2130772144;
        public static final int colorBackgroundFloating = 2130772151;
        public static final int colorButtonNormal = 2130772148;
        public static final int colorControlActivated = 2130772146;
        public static final int colorControlHighlight = 2130772147;
        public static final int colorControlNormal = 2130772145;
        public static final int colorPrimary = 2130772142;
        public static final int colorPrimaryDark = 2130772143;
        public static final int colorSwitchThumbNormal = 2130772149;
        public static final int commitIcon = 2130772258;
        public static final int constraintSet = 2130771968;
        public static final int contentInsetEnd = 2130772035;
        public static final int contentInsetEndWithActions = 2130772039;
        public static final int contentInsetLeft = 2130772036;
        public static final int contentInsetRight = 2130772037;
        public static final int contentInsetStart = 2130772034;
        public static final int contentInsetStartWithNavigation = 2130772038;
        public static final int contentScrim = 2130772185;
        public static final int controlBackground = 2130772150;
        public static final int counterEnabled = 2130772294;
        public static final int counterMaxLength = 2130772295;
        public static final int counterOverflowTextAppearance = 2130772297;
        public static final int counterTextAppearance = 2130772296;
        public static final int customNavigationLayout = 2130772027;
        public static final int defaultQueryHint = 2130772252;
        public static final int dialogPreferredPadding = 2130772103;
        public static final int dialogTheme = 2130772102;
        public static final int displayOptions = 2130772017;
        public static final int divider = 2130772023;
        public static final int dividerHorizontal = 2130772116;
        public static final int dividerPadding = 2130772226;
        public static final int dividerVertical = 2130772115;
        public static final int drawableSize = 2130772211;
        public static final int drawerArrowStyle = 2130771969;
        public static final int dropDownListViewStyle = 2130772134;
        public static final int dropdownListPreferredItemHeight = 2130772106;
        public static final int editTextBackground = 2130772123;
        public static final int editTextColor = 2130772122;
        public static final int editTextStyle = 2130772165;
        public static final int elevation = 2130772040;
        public static final int errorEnabled = 2130772292;
        public static final int errorTextAppearance = 2130772293;
        public static final int expandActivityOverflowButtonDrawable = 2130772044;
        public static final int expanded = 2130772051;
        public static final int expandedTitleGravity = 2130772191;
        public static final int expandedTitleMargin = 2130772178;
        public static final int expandedTitleMarginBottom = 2130772182;
        public static final int expandedTitleMarginEnd = 2130772181;
        public static final int expandedTitleMarginStart = 2130772179;
        public static final int expandedTitleMarginTop = 2130772180;
        public static final int expandedTitleTextAppearance = 2130772183;
        public static final int fabSize = 2130772218;
        public static final int foregroundInsidePadding = 2130772223;
        public static final int gapBetweenBars = 2130772212;
        public static final int goIcon = 2130772254;
        public static final int headerLayout = 2130772238;
        public static final int height = 2130771970;
        public static final int hideOnContentScroll = 2130772033;
        public static final int hintAnimationEnabled = 2130772298;
        public static final int hintEnabled = 2130772291;
        public static final int hintTextAppearance = 2130772290;
        public static final int homeAsUpIndicator = 2130772108;
        public static final int homeLayout = 2130772028;
        public static final int icon = 2130772021;
        public static final int iconifiedByDefault = 2130772250;
        public static final int imageButtonStyle = 2130772124;
        public static final int indeterminateProgressStyle = 2130772030;
        public static final int initialActivityCount = 2130772043;
        public static final int insetForeground = 2130772247;
        public static final int isLightTheme = 2130771971;
        public static final int itemBackground = 2130772236;
        public static final int itemIconTint = 2130772234;
        public static final int itemPadding = 2130772032;
        public static final int itemTextAppearance = 2130772237;
        public static final int itemTextColor = 2130772235;
        public static final int keylines = 2130772198;
        public static final int layout = 2130772249;
        public static final int layoutManager = 2130772243;
        public static final int layout_anchor = 2130772201;
        public static final int layout_anchorGravity = 2130772203;
        public static final int layout_behavior = 2130772200;
        public static final int layout_collapseMode = 2130772193;
        public static final int layout_collapseParallaxMultiplier = 2130772194;
        public static final int layout_constraintBaseline_creator = 2130771972;
        public static final int layout_constraintBaseline_toBaselineOf = 2130771973;
        public static final int layout_constraintBottom_creator = 2130771974;
        public static final int layout_constraintBottom_toBottomOf = 2130771975;
        public static final int layout_constraintBottom_toTopOf = 2130771976;
        public static final int layout_constraintDimensionRatio = 2130771977;
        public static final int layout_constraintEnd_toEndOf = 2130771978;
        public static final int layout_constraintEnd_toStartOf = 2130771979;
        public static final int layout_constraintGuide_begin = 2130771980;
        public static final int layout_constraintGuide_end = 2130771981;
        public static final int layout_constraintGuide_percent = 2130771982;
        public static final int layout_constraintHeight_default = 2130771983;
        public static final int layout_constraintHeight_max = 2130771984;
        public static final int layout_constraintHeight_min = 2130771985;
        public static final int layout_constraintHorizontal_bias = 2130771986;
        public static final int layout_constraintHorizontal_chainStyle = 2130771987;
        public static final int layout_constraintHorizontal_weight = 2130771988;
        public static final int layout_constraintLeft_creator = 2130771989;
        public static final int layout_constraintLeft_toLeftOf = 2130771990;
        public static final int layout_constraintLeft_toRightOf = 2130771991;
        public static final int layout_constraintRight_creator = 2130771992;
        public static final int layout_constraintRight_toLeftOf = 2130771993;
        public static final int layout_constraintRight_toRightOf = 2130771994;
        public static final int layout_constraintStart_toEndOf = 2130771995;
        public static final int layout_constraintStart_toStartOf = 2130771996;
        public static final int layout_constraintTop_creator = 2130771997;
        public static final int layout_constraintTop_toBottomOf = 2130771998;
        public static final int layout_constraintTop_toTopOf = 2130771999;
        public static final int layout_constraintVertical_bias = 2130772000;
        public static final int layout_constraintVertical_chainStyle = 2130772001;
        public static final int layout_constraintVertical_weight = 2130772002;
        public static final int layout_constraintWidth_default = 2130772003;
        public static final int layout_constraintWidth_max = 2130772004;
        public static final int layout_constraintWidth_min = 2130772005;
        public static final int layout_dodgeInsetEdges = 2130772205;
        public static final int layout_editor_absoluteX = 2130772006;
        public static final int layout_editor_absoluteY = 2130772007;
        public static final int layout_goneMarginBottom = 2130772008;
        public static final int layout_goneMarginEnd = 2130772009;
        public static final int layout_goneMarginLeft = 2130772010;
        public static final int layout_goneMarginRight = 2130772011;
        public static final int layout_goneMarginStart = 2130772012;
        public static final int layout_goneMarginTop = 2130772013;
        public static final int layout_insetEdge = 2130772204;
        public static final int layout_keyline = 2130772202;
        public static final int layout_optimizationLevel = 2130772014;
        public static final int layout_scrollFlags = 2130772054;
        public static final int layout_scrollInterpolator = 2130772055;
        public static final int listChoiceBackgroundIndicator = 2130772141;
        public static final int listDividerAlertDialog = 2130772104;
        public static final int listItemLayout = 2130772049;
        public static final int listLayout = 2130772046;
        public static final int listMenuViewStyle = 2130772173;
        public static final int listPopupWindowStyle = 2130772135;
        public static final int listPreferredItemHeight = 2130772129;
        public static final int listPreferredItemHeightLarge = 2130772131;
        public static final int listPreferredItemHeightSmall = 2130772130;
        public static final int listPreferredItemPaddingLeft = 2130772132;
        public static final int listPreferredItemPaddingRight = 2130772133;
        public static final int logo = 2130772022;
        public static final int logoDescription = 2130772318;
        public static final int maxActionInlineWidth = 2130772262;
        public static final int maxButtonHeight = 2130772312;
        public static final int measureWithLargestChild = 2130772224;
        public static final int menu = 2130772233;
        public static final int multiChoiceItemLayout = 2130772047;
        public static final int navigationContentDescription = 2130772317;
        public static final int navigationIcon = 2130772316;
        public static final int navigationMode = 2130772016;
        public static final int overlapAnchor = 2130772239;
        public static final int paddingBottomNoButtons = 2130772241;
        public static final int paddingEnd = 2130772322;
        public static final int paddingStart = 2130772321;
        public static final int paddingTopNoTitle = 2130772242;
        public static final int panelBackground = 2130772138;
        public static final int panelMenuListTheme = 2130772140;
        public static final int panelMenuListWidth = 2130772139;
        public static final int passwordToggleContentDescription = 2130772301;
        public static final int passwordToggleDrawable = 2130772300;
        public static final int passwordToggleEnabled = 2130772299;
        public static final int passwordToggleTint = 2130772302;
        public static final int passwordToggleTintMode = 2130772303;
        public static final int popupMenuStyle = 2130772120;
        public static final int popupTheme = 2130772041;
        public static final int popupWindowStyle = 2130772121;
        public static final int preserveIconSpacing = 2130772231;
        public static final int pressedTranslationZ = 2130772219;
        public static final int progressBarPadding = 2130772031;
        public static final int progressBarStyle = 2130772029;
        public static final int queryBackground = 2130772260;
        public static final int queryHint = 2130772251;
        public static final int radioButtonStyle = 2130772166;
        public static final int ratingBarStyle = 2130772167;
        public static final int ratingBarStyleIndicator = 2130772168;
        public static final int ratingBarStyleSmall = 2130772169;
        public static final int reverseLayout = 2130772245;
        public static final int rippleColor = 2130772217;
        public static final int scrimAnimationDuration = 2130772189;
        public static final int scrimVisibleHeightTrigger = 2130772188;
        public static final int searchHintIcon = 2130772256;
        public static final int searchIcon = 2130772255;
        public static final int searchViewStyle = 2130772128;
        public static final int seekBarStyle = 2130772170;
        public static final int selectableItemBackground = 2130772112;
        public static final int selectableItemBackgroundBorderless = 2130772113;
        public static final int showAsAction = 2130772227;
        public static final int showDividers = 2130772225;
        public static final int showText = 2130772273;
        public static final int showTitle = 2130772050;
        public static final int singleChoiceItemLayout = 2130772048;
        public static final int spanCount = 2130772244;
        public static final int spinBars = 2130772210;
        public static final int spinnerDropDownItemStyle = 2130772107;
        public static final int spinnerStyle = 2130772171;
        public static final int splitTrack = 2130772272;
        public static final int srcCompat = 2130772056;
        public static final int stackFromEnd = 2130772246;
        public static final int state_above_anchor = 2130772240;
        public static final int state_collapsed = 2130772052;
        public static final int state_collapsible = 2130772053;
        public static final int statusBarBackground = 2130772199;
        public static final int statusBarScrim = 2130772186;
        public static final int subMenuArrow = 2130772232;
        public static final int submitBackground = 2130772261;
        public static final int subtitle = 2130772018;
        public static final int subtitleTextAppearance = 2130772305;
        public static final int subtitleTextColor = 2130772320;
        public static final int subtitleTextStyle = 2130772020;
        public static final int suggestionRowLayout = 2130772259;
        public static final int switchMinWidth = 2130772270;
        public static final int switchPadding = 2130772271;
        public static final int switchStyle = 2130772172;
        public static final int switchTextAppearance = 2130772269;
        public static final int tabBackground = 2130772277;
        public static final int tabContentStart = 2130772276;
        public static final int tabGravity = 2130772279;
        public static final int tabIndicatorColor = 2130772274;
        public static final int tabIndicatorHeight = 2130772275;
        public static final int tabMaxWidth = 2130772281;
        public static final int tabMinWidth = 2130772280;
        public static final int tabMode = 2130772278;
        public static final int tabPadding = 2130772289;
        public static final int tabPaddingBottom = 2130772288;
        public static final int tabPaddingEnd = 2130772287;
        public static final int tabPaddingStart = 2130772285;
        public static final int tabPaddingTop = 2130772286;
        public static final int tabSelectedTextColor = 2130772284;
        public static final int tabTextAppearance = 2130772282;
        public static final int tabTextColor = 2130772283;
        public static final int textAllCaps = 2130772060;
        public static final int textAppearanceLargePopupMenu = 2130772099;
        public static final int textAppearanceListItem = 2130772136;
        public static final int textAppearanceListItemSmall = 2130772137;
        public static final int textAppearancePopupMenuHeader = 2130772101;
        public static final int textAppearanceSearchResultSubtitle = 2130772126;
        public static final int textAppearanceSearchResultTitle = 2130772125;
        public static final int textAppearanceSmallPopupMenu = 2130772100;
        public static final int textColorAlertDialogListItem = 2130772156;
        public static final int textColorError = 2130772208;
        public static final int textColorSearchUrl = 2130772127;
        public static final int theme = 2130772323;
        public static final int thickness = 2130772216;
        public static final int thumbTextPadding = 2130772268;
        public static final int thumbTint = 2130772263;
        public static final int thumbTintMode = 2130772264;
        public static final int tickMark = 2130772057;
        public static final int tickMarkTint = 2130772058;
        public static final int tickMarkTintMode = 2130772059;
        public static final int title = 2130772015;
        public static final int titleEnabled = 2130772192;
        public static final int titleMargin = 2130772306;
        public static final int titleMarginBottom = 2130772310;
        public static final int titleMarginEnd = 2130772308;
        public static final int titleMarginStart = 2130772307;
        public static final int titleMarginTop = 2130772309;
        public static final int titleMargins = 2130772311;
        public static final int titleTextAppearance = 2130772304;
        public static final int titleTextColor = 2130772319;
        public static final int titleTextStyle = 2130772019;
        public static final int toolbarId = 2130772187;
        public static final int toolbarNavigationButtonStyle = 2130772119;
        public static final int toolbarStyle = 2130772118;
        public static final int track = 2130772265;
        public static final int trackTint = 2130772266;
        public static final int trackTintMode = 2130772267;
        public static final int useCompatPadding = 2130772221;
        public static final int voiceIcon = 2130772257;
        public static final int windowActionBar = 2130772061;
        public static final int windowActionBarOverlay = 2130772063;
        public static final int windowActionModeOverlay = 2130772064;
        public static final int windowFixedHeightMajor = 2130772068;
        public static final int windowFixedHeightMinor = 2130772066;
        public static final int windowFixedWidthMajor = 2130772065;
        public static final int windowFixedWidthMinor = 2130772067;
        public static final int windowMinWidthMajor = 2130772069;
        public static final int windowMinWidthMinor = 2130772070;
        public static final int windowNoTitle = 2130772062;
    }

    public static final class bool {
        public static final int abc_action_bar_embed_tabs = 2131361792;
        public static final int abc_allow_stacked_button_bar = 2131361794;
        public static final int abc_config_actionMenuItemAllCaps = 2131361795;
        public static final int abc_config_closeDialogWhenTouchOutside = 2131361796;
        public static final int abc_config_showMenuShortcutsWhenKeyboardPresent = 2131361797;
        public static final int isTablet = 2131361793;
    }

    public static final class color {
        public static final int abc_background_cache_hint_selector_material_dark = 2131492934;
        public static final int abc_background_cache_hint_selector_material_light = 2131492935;
        public static final int abc_btn_colored_borderless_text_material = 2131492936;
        public static final int abc_btn_colored_text_material = 2131492937;
        public static final int abc_color_highlight_material = 2131492938;
        public static final int abc_hint_foreground_material_dark = 2131492939;
        public static final int abc_hint_foreground_material_light = 2131492940;
        public static final int abc_input_method_navigation_guard = 2131492865;
        public static final int abc_primary_text_disable_only_material_dark = 2131492941;
        public static final int abc_primary_text_disable_only_material_light = 2131492942;
        public static final int abc_primary_text_material_dark = 2131492943;
        public static final int abc_primary_text_material_light = 2131492944;
        public static final int abc_search_url_text = 2131492945;
        public static final int abc_search_url_text_normal = 2131492866;
        public static final int abc_search_url_text_pressed = 2131492867;
        public static final int abc_search_url_text_selected = 2131492868;
        public static final int abc_secondary_text_material_dark = 2131492946;
        public static final int abc_secondary_text_material_light = 2131492947;
        public static final int abc_tint_btn_checkable = 2131492948;
        public static final int abc_tint_default = 2131492949;
        public static final int abc_tint_edittext = 2131492950;
        public static final int abc_tint_seek_thumb = 2131492951;
        public static final int abc_tint_spinner = 2131492952;
        public static final int abc_tint_switch_thumb = 2131492953;
        public static final int abc_tint_switch_track = 2131492954;
        public static final int accent_material_dark = 2131492869;
        public static final int accent_material_light = 2131492870;
        public static final int background_floating_material_dark = 2131492871;
        public static final int background_floating_material_light = 2131492872;
        public static final int background_material_dark = 2131492873;
        public static final int background_material_light = 2131492874;
        public static final int bright_foreground_disabled_material_dark = 2131492875;
        public static final int bright_foreground_disabled_material_light = 2131492876;
        public static final int bright_foreground_inverse_material_dark = 2131492877;
        public static final int bright_foreground_inverse_material_light = 2131492878;
        public static final int bright_foreground_material_dark = 2131492879;
        public static final int bright_foreground_material_light = 2131492880;
        public static final int button_material_dark = 2131492881;
        public static final int button_material_light = 2131492882;
        public static final int design_bottom_navigation_shadow_color = 2131492883;
        public static final int design_error = 2131492955;
        public static final int design_fab_shadow_end_color = 2131492884;
        public static final int design_fab_shadow_mid_color = 2131492885;
        public static final int design_fab_shadow_start_color = 2131492886;
        public static final int design_fab_stroke_end_inner_color = 2131492887;
        public static final int design_fab_stroke_end_outer_color = 2131492888;
        public static final int design_fab_stroke_top_inner_color = 2131492889;
        public static final int design_fab_stroke_top_outer_color = 2131492890;
        public static final int design_snackbar_background_color = 2131492891;
        public static final int design_textinput_error_color_dark = 2131492892;
        public static final int design_textinput_error_color_light = 2131492893;
        public static final int design_tint_password_toggle = 2131492956;
        public static final int dim_foreground_disabled_material_dark = 2131492894;
        public static final int dim_foreground_disabled_material_light = 2131492895;
        public static final int dim_foreground_material_dark = 2131492896;
        public static final int dim_foreground_material_light = 2131492897;
        public static final int foreground_material_dark = 2131492898;
        public static final int foreground_material_light = 2131492899;
        public static final int highlighted_text_material_dark = 2131492900;
        public static final int highlighted_text_material_light = 2131492901;
        public static final int material_blue_grey_800 = 2131492902;
        public static final int material_blue_grey_900 = 2131492903;
        public static final int material_blue_grey_950 = 2131492904;
        public static final int material_deep_teal_200 = 2131492905;
        public static final int material_deep_teal_500 = 2131492906;
        public static final int material_grey_100 = 2131492907;
        public static final int material_grey_300 = 2131492908;
        public static final int material_grey_50 = 2131492909;
        public static final int material_grey_600 = 2131492910;
        public static final int material_grey_800 = 2131492911;
        public static final int material_grey_850 = 2131492912;
        public static final int material_grey_900 = 2131492913;
        public static final int notification_action_color_filter = 2131492864;
        public static final int notification_icon_bg_color = 2131492914;
        public static final int notification_material_background_media_default_color = 2131492915;
        public static final int primary_dark_material_dark = 2131492916;
        public static final int primary_dark_material_light = 2131492917;
        public static final int primary_material_dark = 2131492918;
        public static final int primary_material_light = 2131492919;
        public static final int primary_text_default_material_dark = 2131492920;
        public static final int primary_text_default_material_light = 2131492921;
        public static final int primary_text_disabled_material_dark = 2131492922;
        public static final int primary_text_disabled_material_light = 2131492923;
        public static final int ripple_material_dark = 2131492924;
        public static final int ripple_material_light = 2131492925;
        public static final int secondary_text_default_material_dark = 2131492926;
        public static final int secondary_text_default_material_light = 2131492927;
        public static final int secondary_text_disabled_material_dark = 2131492928;
        public static final int secondary_text_disabled_material_light = 2131492929;
        public static final int switch_thumb_disabled_material_dark = 2131492930;
        public static final int switch_thumb_disabled_material_light = 2131492931;
        public static final int switch_thumb_material_dark = 2131492957;
        public static final int switch_thumb_material_light = 2131492958;
        public static final int switch_thumb_normal_material_dark = 2131492932;
        public static final int switch_thumb_normal_material_light = 2131492933;
    }

    public static final class dimen {
        public static final int abc_action_bar_content_inset_material = 2131230732;
        public static final int abc_action_bar_content_inset_with_nav = 2131230733;
        public static final int abc_action_bar_default_height_material = 2131230721;
        public static final int abc_action_bar_default_padding_end_material = 2131230734;
        public static final int abc_action_bar_default_padding_start_material = 2131230735;
        public static final int abc_action_bar_elevation_material = 2131230750;
        public static final int abc_action_bar_icon_vertical_padding_material = 2131230751;
        public static final int abc_action_bar_overflow_padding_end_material = 2131230752;
        public static final int abc_action_bar_overflow_padding_start_material = 2131230753;
        public static final int abc_action_bar_progress_bar_size = 2131230722;
        public static final int abc_action_bar_stacked_max_height = 2131230754;
        public static final int abc_action_bar_stacked_tab_max_width = 2131230755;
        public static final int abc_action_bar_subtitle_bottom_margin_material = 2131230756;
        public static final int abc_action_bar_subtitle_top_margin_material = 2131230757;
        public static final int abc_action_button_min_height_material = 2131230758;
        public static final int abc_action_button_min_width_material = 2131230759;
        public static final int abc_action_button_min_width_overflow_material = 2131230760;
        public static final int abc_alert_dialog_button_bar_height = 2131230720;
        public static final int abc_button_inset_horizontal_material = 2131230761;
        public static final int abc_button_inset_vertical_material = 2131230762;
        public static final int abc_button_padding_horizontal_material = 2131230763;
        public static final int abc_button_padding_vertical_material = 2131230764;
        public static final int abc_cascading_menus_min_smallest_width = 2131230765;
        public static final int abc_config_prefDialogWidth = 2131230725;
        public static final int abc_control_corner_material = 2131230766;
        public static final int abc_control_inset_material = 2131230767;
        public static final int abc_control_padding_material = 2131230768;
        public static final int abc_dialog_fixed_height_major = 2131230726;
        public static final int abc_dialog_fixed_height_minor = 2131230727;
        public static final int abc_dialog_fixed_width_major = 2131230728;
        public static final int abc_dialog_fixed_width_minor = 2131230729;
        public static final int abc_dialog_list_padding_bottom_no_buttons = 2131230769;
        public static final int abc_dialog_list_padding_top_no_title = 2131230770;
        public static final int abc_dialog_min_width_major = 2131230730;
        public static final int abc_dialog_min_width_minor = 2131230731;
        public static final int abc_dialog_padding_material = 2131230771;
        public static final int abc_dialog_padding_top_material = 2131230772;
        public static final int abc_dialog_title_divider_material = 2131230773;
        public static final int abc_disabled_alpha_material_dark = 2131230774;
        public static final int abc_disabled_alpha_material_light = 2131230775;
        public static final int abc_dropdownitem_icon_width = 2131230776;
        public static final int abc_dropdownitem_text_padding_left = 2131230777;
        public static final int abc_dropdownitem_text_padding_right = 2131230778;
        public static final int abc_edit_text_inset_bottom_material = 2131230779;
        public static final int abc_edit_text_inset_horizontal_material = 2131230780;
        public static final int abc_edit_text_inset_top_material = 2131230781;
        public static final int abc_floating_window_z = 2131230782;
        public static final int abc_list_item_padding_horizontal_material = 2131230783;
        public static final int abc_panel_menu_list_width = 2131230784;
        public static final int abc_progress_bar_height_material = 2131230785;
        public static final int abc_search_view_preferred_height = 2131230786;
        public static final int abc_search_view_preferred_width = 2131230787;
        public static final int abc_seekbar_track_background_height_material = 2131230788;
        public static final int abc_seekbar_track_progress_height_material = 2131230789;
        public static final int abc_select_dialog_padding_start_material = 2131230790;
        public static final int abc_switch_padding = 2131230746;
        public static final int abc_text_size_body_1_material = 2131230791;
        public static final int abc_text_size_body_2_material = 2131230792;
        public static final int abc_text_size_button_material = 2131230793;
        public static final int abc_text_size_caption_material = 2131230794;
        public static final int abc_text_size_display_1_material = 2131230795;
        public static final int abc_text_size_display_2_material = 2131230796;
        public static final int abc_text_size_display_3_material = 2131230797;
        public static final int abc_text_size_display_4_material = 2131230798;
        public static final int abc_text_size_headline_material = 2131230799;
        public static final int abc_text_size_large_material = 2131230800;
        public static final int abc_text_size_medium_material = 2131230801;
        public static final int abc_text_size_menu_header_material = 2131230802;
        public static final int abc_text_size_menu_material = 2131230803;
        public static final int abc_text_size_small_material = 2131230804;
        public static final int abc_text_size_subhead_material = 2131230805;
        public static final int abc_text_size_subtitle_material_toolbar = 2131230723;
        public static final int abc_text_size_title_material = 2131230806;
        public static final int abc_text_size_title_material_toolbar = 2131230724;
        public static final int activity_horizontal_margin = 2131230744;
        public static final int design_appbar_elevation = 2131230807;
        public static final int design_bottom_navigation_active_item_max_width = 2131230808;
        public static final int design_bottom_navigation_active_text_size = 2131230809;
        public static final int design_bottom_navigation_elevation = 2131230810;
        public static final int design_bottom_navigation_height = 2131230811;
        public static final int design_bottom_navigation_item_max_width = 2131230812;
        public static final int design_bottom_navigation_item_min_width = 2131230813;
        public static final int design_bottom_navigation_margin = 2131230814;
        public static final int design_bottom_navigation_shadow_height = 2131230815;
        public static final int design_bottom_navigation_text_size = 2131230816;
        public static final int design_bottom_sheet_modal_elevation = 2131230817;
        public static final int design_bottom_sheet_peek_height_min = 2131230818;
        public static final int design_fab_border_width = 2131230819;
        public static final int design_fab_elevation = 2131230820;
        public static final int design_fab_image_size = 2131230821;
        public static final int design_fab_size_mini = 2131230822;
        public static final int design_fab_size_normal = 2131230823;
        public static final int design_fab_translation_z_pressed = 2131230824;
        public static final int design_navigation_elevation = 2131230825;
        public static final int design_navigation_icon_padding = 2131230826;
        public static final int design_navigation_icon_size = 2131230827;
        public static final int design_navigation_max_width = 2131230736;
        public static final int design_navigation_padding_bottom = 2131230828;
        public static final int design_navigation_separator_vertical_padding = 2131230829;
        public static final int design_snackbar_action_inline_max_width = 2131230737;
        public static final int design_snackbar_background_corner_radius = 2131230738;
        public static final int design_snackbar_elevation = 2131230830;
        public static final int design_snackbar_extra_spacing_horizontal = 2131230739;
        public static final int design_snackbar_max_width = 2131230740;
        public static final int design_snackbar_min_width = 2131230741;
        public static final int design_snackbar_padding_horizontal = 2131230831;
        public static final int design_snackbar_padding_vertical = 2131230832;
        public static final int design_snackbar_padding_vertical_2lines = 2131230742;
        public static final int design_snackbar_text_size = 2131230833;
        public static final int design_tab_max_width = 2131230834;
        public static final int design_tab_scrollable_min_width = 2131230743;
        public static final int design_tab_text_size = 2131230835;
        public static final int design_tab_text_size_2line = 2131230836;
        public static final int disabled_alpha_material_dark = 2131230837;
        public static final int disabled_alpha_material_light = 2131230838;
        public static final int fab_margin = 2131230839;
        public static final int highlight_alpha_material_colored = 2131230840;
        public static final int highlight_alpha_material_dark = 2131230841;
        public static final int highlight_alpha_material_light = 2131230842;
        public static final int hint_alpha_material_dark = 2131230843;
        public static final int hint_alpha_material_light = 2131230844;
        public static final int hint_pressed_alpha_material_dark = 2131230845;
        public static final int hint_pressed_alpha_material_light = 2131230846;
        public static final int item_touch_helper_max_drag_scroll_per_frame = 2131230847;
        public static final int item_touch_helper_swipe_escape_max_velocity = 2131230848;
        public static final int item_touch_helper_swipe_escape_velocity = 2131230849;
        public static final int notification_action_icon_size = 2131230850;
        public static final int notification_action_text_size = 2131230851;
        public static final int notification_big_circle_margin = 2131230852;
        public static final int notification_content_margin_start = 2131230747;
        public static final int notification_large_icon_height = 2131230853;
        public static final int notification_large_icon_width = 2131230854;
        public static final int notification_main_column_padding_top = 2131230748;
        public static final int notification_media_narrow_margin = 2131230749;
        public static final int notification_right_icon_size = 2131230855;
        public static final int notification_right_side_padding_top = 2131230745;
        public static final int notification_small_icon_background_padding = 2131230856;
        public static final int notification_small_icon_size_as_large = 2131230857;
        public static final int notification_subtext_size = 2131230858;
        public static final int notification_top_pad = 2131230859;
        public static final int notification_top_pad_large_text = 2131230860;
    }

    public static final class drawable {
        public static final int abc_ab_share_pack_mtrl_alpha = 2130837504;
        public static final int abc_action_bar_item_background_material = 2130837505;
        public static final int abc_btn_borderless_material = 2130837506;
        public static final int abc_btn_check_material = 2130837507;
        public static final int abc_btn_check_to_on_mtrl_000 = 2130837508;
        public static final int abc_btn_check_to_on_mtrl_015 = 2130837509;
        public static final int abc_btn_colored_material = 2130837510;
        public static final int abc_btn_default_mtrl_shape = 2130837511;
        public static final int abc_btn_radio_material = 2130837512;
        public static final int abc_btn_radio_to_on_mtrl_000 = 2130837513;
        public static final int abc_btn_radio_to_on_mtrl_015 = 2130837514;
        public static final int abc_btn_switch_to_on_mtrl_00001 = 2130837515;
        public static final int abc_btn_switch_to_on_mtrl_00012 = 2130837516;
        public static final int abc_cab_background_internal_bg = 2130837517;
        public static final int abc_cab_background_top_material = 2130837518;
        public static final int abc_cab_background_top_mtrl_alpha = 2130837519;
        public static final int abc_control_background_material = 2130837520;
        public static final int abc_dialog_material_background = 2130837521;
        public static final int abc_edit_text_material = 2130837522;
        public static final int abc_ic_ab_back_material = 2130837523;
        public static final int abc_ic_arrow_drop_right_black_24dp = 2130837524;
        public static final int abc_ic_clear_material = 2130837525;
        public static final int abc_ic_commit_search_api_mtrl_alpha = 2130837526;
        public static final int abc_ic_go_search_api_material = 2130837527;
        public static final int abc_ic_menu_copy_mtrl_am_alpha = 2130837528;
        public static final int abc_ic_menu_cut_mtrl_alpha = 2130837529;
        public static final int abc_ic_menu_overflow_material = 2130837530;
        public static final int abc_ic_menu_paste_mtrl_am_alpha = 2130837531;
        public static final int abc_ic_menu_selectall_mtrl_alpha = 2130837532;
        public static final int abc_ic_menu_share_mtrl_alpha = 2130837533;
        public static final int abc_ic_search_api_material = 2130837534;
        public static final int abc_ic_star_black_16dp = 2130837535;
        public static final int abc_ic_star_black_36dp = 2130837536;
        public static final int abc_ic_star_black_48dp = 2130837537;
        public static final int abc_ic_star_half_black_16dp = 2130837538;
        public static final int abc_ic_star_half_black_36dp = 2130837539;
        public static final int abc_ic_star_half_black_48dp = 2130837540;
        public static final int abc_ic_voice_search_api_material = 2130837541;
        public static final int abc_item_background_holo_dark = 2130837542;
        public static final int abc_item_background_holo_light = 2130837543;
        public static final int abc_list_divider_mtrl_alpha = 2130837544;
        public static final int abc_list_focused_holo = 2130837545;
        public static final int abc_list_longpressed_holo = 2130837546;
        public static final int abc_list_pressed_holo_dark = 2130837547;
        public static final int abc_list_pressed_holo_light = 2130837548;
        public static final int abc_list_selector_background_transition_holo_dark = 2130837549;
        public static final int abc_list_selector_background_transition_holo_light = 2130837550;
        public static final int abc_list_selector_disabled_holo_dark = 2130837551;
        public static final int abc_list_selector_disabled_holo_light = 2130837552;
        public static final int abc_list_selector_holo_dark = 2130837553;
        public static final int abc_list_selector_holo_light = 2130837554;
        public static final int abc_menu_hardkey_panel_mtrl_mult = 2130837555;
        public static final int abc_popup_background_mtrl_mult = 2130837556;
        public static final int abc_ratingbar_indicator_material = 2130837557;
        public static final int abc_ratingbar_material = 2130837558;
        public static final int abc_ratingbar_small_material = 2130837559;
        public static final int abc_scrubber_control_off_mtrl_alpha = 2130837560;
        public static final int abc_scrubber_control_to_pressed_mtrl_000 = 2130837561;
        public static final int abc_scrubber_control_to_pressed_mtrl_005 = 2130837562;
        public static final int abc_scrubber_primary_mtrl_alpha = 2130837563;
        public static final int abc_scrubber_track_mtrl_alpha = 2130837564;
        public static final int abc_seekbar_thumb_material = 2130837565;
        public static final int abc_seekbar_tick_mark_material = 2130837566;
        public static final int abc_seekbar_track_material = 2130837567;
        public static final int abc_spinner_mtrl_am_alpha = 2130837568;
        public static final int abc_spinner_textfield_background_material = 2130837569;
        public static final int abc_switch_thumb_material = 2130837570;
        public static final int abc_switch_track_mtrl_alpha = 2130837571;
        public static final int abc_tab_indicator_material = 2130837572;
        public static final int abc_tab_indicator_mtrl_alpha = 2130837573;
        public static final int abc_text_cursor_material = 2130837574;
        public static final int abc_text_select_handle_left_mtrl_dark = 2130837575;
        public static final int abc_text_select_handle_left_mtrl_light = 2130837576;
        public static final int abc_text_select_handle_middle_mtrl_dark = 2130837577;
        public static final int abc_text_select_handle_middle_mtrl_light = 2130837578;
        public static final int abc_text_select_handle_right_mtrl_dark = 2130837579;
        public static final int abc_text_select_handle_right_mtrl_light = 2130837580;
        public static final int abc_textfield_activated_mtrl_alpha = 2130837581;
        public static final int abc_textfield_default_mtrl_alpha = 2130837582;
        public static final int abc_textfield_search_activated_mtrl_alpha = 2130837583;
        public static final int abc_textfield_search_default_mtrl_alpha = 2130837584;
        public static final int abc_textfield_search_material = 2130837585;
        public static final int abc_vector_test = 2130837586;
        public static final int aud_but = 2130837587;
        public static final int aud_but_large = 2130837588;
        public static final int avd_hide_password = 2130837589;
        public static final int avd_hide_password_1 = 2130837649;
        public static final int avd_hide_password_2 = 2130837650;
        public static final int avd_hide_password_3 = 2130837651;
        public static final int avd_show_password = 2130837590;
        public static final int avd_show_password_1 = 2130837652;
        public static final int avd_show_password_2 = 2130837653;
        public static final int avd_show_password_3 = 2130837654;
        public static final int blank_button_bronze_selector = 2130837591;
        public static final int blank_button_gold_selector = 2130837592;
        public static final int blank_button_selector = 2130837593;
        public static final int blank_button_silver_selector = 2130837594;
        public static final int but_blank = 2130837595;
        public static final int but_blank2 = 2130837596;
        public static final int but_blank_bronze = 2130837597;
        public static final int but_blank_gold = 2130837598;
        public static final int but_blank_silver = 2130837599;
        public static final int butblankbronze = 2130837600;
        public static final int butblankgold = 2130837601;
        public static final int butblanksilver = 2130837602;
        public static final int countdown_border = 2130837603;
        public static final int countdown_border_large = 2130837604;
        public static final int countdown_border_large_src = 2130837605;
        public static final int countdown_border_small = 2130837606;
        public static final int countdown_border_small_src = 2130837607;
        public static final int countdown_border_src = 2130837608;
        public static final int design_bottom_navigation_item_background = 2130837609;
        public static final int design_fab_background = 2130837610;
        public static final int design_ic_visibility = 2130837611;
        public static final int design_ic_visibility_off = 2130837612;
        public static final int design_password_eye = 2130837613;
        public static final int design_snackbar_background = 2130837614;
        public static final int disp_back = 2130837615;
        public static final int hint = 2130837616;
        public static final int main_background = 2130837617;
        public static final int main_background2 = 2130837618;
        public static final int navigation_empty_icon = 2130837619;
        public static final int new_logo2 = 2130837620;
        public static final int notification_action_background = 2130837621;
        public static final int notification_bg = 2130837622;
        public static final int notification_bg_low = 2130837623;
        public static final int notification_bg_low_normal = 2130837624;
        public static final int notification_bg_low_pressed = 2130837625;
        public static final int notification_bg_normal = 2130837626;
        public static final int notification_bg_normal_pressed = 2130837627;
        public static final int notification_icon_background = 2130837628;
        public static final int notification_template_icon_bg = 2130837647;
        public static final int notification_template_icon_low_bg = 2130837648;
        public static final int notification_tile_bg = 2130837629;
        public static final int notify_panel_notification_icon_bg = 2130837630;
        public static final int progress_style = 2130837631;
        public static final int review_arrow_left = 2130837632;
        public static final int review_arrow_left_large = 2130837633;
        public static final int review_arrow_right = 2130837634;
        public static final int review_arrow_right_large = 2130837635;
        public static final int settings_but = 2130837636;
        public static final int settings_but_large = 2130837637;
        public static final int settings_pane = 2130837638;
        public static final int settings_pane_large_src = 2130837639;
        public static final int settings_pane_src = 2130837640;
        public static final int tar_back = 2130837641;
        public static final int tar_back_gold = 2130837642;
        public static final int tar_back_gold2 = 2130837643;
        public static final int tar_back_selector = 2130837644;
        public static final int title_shadow = 2130837645;
        public static final int title_shadow_large = 2130837646;
    }

    public static final class id {
        public static final int action0 = 2131558631;
        public static final int action_bar = 2131558507;
        public static final int action_bar_activity_content = 2131558400;
        public static final int action_bar_container = 2131558506;
        public static final int action_bar_root = 2131558502;
        public static final int action_bar_spinner = 2131558401;
        public static final int action_bar_subtitle = 2131558473;
        public static final int action_bar_title = 2131558472;
        public static final int action_container = 2131558628;
        public static final int action_context_bar = 2131558508;
        public static final int action_divider = 2131558635;
        public static final int action_image = 2131558629;
        public static final int action_menu_divider = 2131558402;
        public static final int action_menu_presenter = 2131558403;
        public static final int action_mode_bar = 2131558504;
        public static final int action_mode_bar_stub = 2131558503;
        public static final int action_mode_close_button = 2131558474;
        public static final int action_text = 2131558630;
        public static final int actions = 2131558644;
        public static final int activityCountDownProgressBar1 = 2131558627;
        public static final int activityEndAgainButton = 2131558527;
        public static final int activityEndBackButton = 2131558526;
        public static final int activityEndFeedbackButton = 2131558529;
        public static final int activityEndInfoText = 2131558523;
        public static final int activityEndRateLayout = 2131558528;
        public static final int activityEndSummaryText0 = 2131558524;
        public static final int activityEndSummaryText1 = 2131558525;
        public static final int activityEndTitleText = 2131558522;
        public static final int activityLearnHint = 2131558537;
        public static final int activityLearnHintDummy = 2131558533;
        public static final int activityLearnMainText = 2131558530;
        public static final int activityMainButton0 = 2131558552;
        public static final int activityMainButton1 = 2131558553;
        public static final int activityMainButton2 = 2131558554;
        public static final int activityMainButton3 = 2131558555;
        public static final int activityMainButton4 = 2131558556;
        public static final int activityMainButton5 = 2131558557;
        public static final int activityMenuLearnButton = 2131558565;
        public static final int activityMenuPracticeButton = 2131558566;
        public static final int activityMenuReviewButton = 2131558567;
        public static final int activityMenuStat00 = 2131558559;
        public static final int activityMenuStat01 = 2131558560;
        public static final int activityMenuStat10 = 2131558561;
        public static final int activityMenuStat11 = 2131558562;
        public static final int activityMenuStat20 = 2131558563;
        public static final int activityMenuStat21 = 2131558564;
        public static final int activityMenuTitleText = 2131558558;
        public static final int activityPracticeButton0 = 2131558569;
        public static final int activityPracticeButton1 = 2131558570;
        public static final int activityPracticeButton2 = 2131558571;
        public static final int activityPracticeButton3 = 2131558572;
        public static final int activityPracticeButton4 = 2131558573;
        public static final int activityPracticeButton5 = 2131558574;
        public static final int activityPracticeButton6 = 2131558575;
        public static final int activityPracticeFragment = 2131558576;
        public static final int activityPracticeText = 2131558568;
        public static final int activityReviewButAgain = 2131558583;
        public static final int activityReviewButGood = 2131558582;
        public static final int activityReviewMiddleLayout = 2131558578;
        public static final int activityReviewRevealedLine0 = 2131558579;
        public static final int activityReviewRevealedLine1 = 2131558580;
        public static final int activityReviewRoundText = 2131558581;
        public static final int activityReviewTopText = 2131558577;
        public static final int activity_chooser_view_content = 2131558475;
        public static final int activtiyMainSubTitleText = 2131558550;
        public static final int activtiyMainTitleImage = 2131558548;
        public static final int add = 2131558438;
        public static final int alertTitle = 2131558495;
        public static final int all = 2131558420;
        public static final int always = 2131558465;
        public static final int audBut = 2131558547;
        public static final int audButBig = 2131558531;
        public static final int auto = 2131558445;
        public static final int backPart = 2131558584;
        public static final int basic = 2131558421;
        public static final int beginning = 2131558463;
        public static final int bottom = 2131558446;
        public static final int bottomText = 2131558546;
        public static final int buttonPanel = 2131558482;
        public static final int cancel_action = 2131558632;
        public static final int center = 2131558447;
        public static final int center_horizontal = 2131558448;
        public static final int center_vertical = 2131558449;
        public static final int chains = 2131558422;
        public static final int checkbox = 2131558498;
        public static final int chronometer = 2131558640;
        public static final int clip_horizontal = 2131558458;
        public static final int clip_vertical = 2131558459;
        public static final int collapseActionView = 2131558466;
        public static final int contentPanel = 2131558485;
        public static final int custom = 2131558492;
        public static final int customPanel = 2131558491;
        public static final int decor_content_parent = 2131558505;
        public static final int default_activity_button = 2131558478;
        public static final int delayTextBoxCheck = 2131558604;
        public static final int design_bottom_sheet = 2131558618;
        public static final int design_menu_item_action_area = 2131558625;
        public static final int design_menu_item_action_area_stub = 2131558624;
        public static final int design_menu_item_text = 2131558623;
        public static final int design_navigation_view = 2131558622;
        public static final int disableHome = 2131558427;
        public static final int edit_query = 2131558509;
        public static final int end = 2131558450;
        public static final int end_padder = 2131558650;
        public static final int enterAlways = 2131558433;
        public static final int enterAlwaysCollapsed = 2131558434;
        public static final int exitUntilCollapsed = 2131558435;
        public static final int expand_activities_button = 2131558476;
        public static final int expanded_menu = 2131558497;
        public static final int fill = 2131558460;
        public static final int fill_horizontal = 2131558461;
        public static final int fill_vertical = 2131558451;
        public static final int fixed = 2131558470;
        public static final int frontPart = 2131558585;
        public static final int home = 2131558404;
        public static final int homeAsUp = 2131558428;
        public static final int icon = 2131558480;
        public static final int icon_group = 2131558645;
        public static final int ifRoom = 2131558467;
        public static final int image = 2131558477;
        public static final int info = 2131558641;
        public static final int item_touch_helper_previous_elevation = 2131558405;
        public static final int lArrow = 2131558549;
        public static final int largeLabel = 2131558616;
        public static final int learnButton0 = 2131558534;
        public static final int learnButton1 = 2131558535;
        public static final int learnButton2 = 2131558536;
        public static final int learnButton3 = 2131558539;
        public static final int learnButton4 = 2131558540;
        public static final int learnButton5 = 2131558542;
        public static final int learnButton6 = 2131558543;
        public static final int learnButton7 = 2131558544;
        public static final int learnLay0 = 2131558532;
        public static final int learnLay1 = 2131558538;
        public static final int learnLay2 = 2131558541;
        public static final int learnSettingsCheck0 = 2131558594;
        public static final int learnSettingsCheck1 = 2131558591;
        public static final int learnSettingsCheck2 = 2131558588;
        public static final int learnSettingsCheck3 = 2131558597;
        public static final int learnSettingsCheck4 = 2131558600;
        public static final int learnSettingsCheck5 = 2131558603;
        public static final int learnSettingsText0 = 2131558586;
        public static final int learnSettingsText1 = 2131558593;
        public static final int learnSettingsText2 = 2131558590;
        public static final int learnSettingsText3 = 2131558587;
        public static final int learnSettingsText4 = 2131558596;
        public static final int learnSettingsText5 = 2131558599;
        public static final int learnSettingsText6 = 2131558602;
        public static final int left = 2131558452;
        public static final int line1 = 2131558646;
        public static final int line3 = 2131558648;
        public static final int listMode = 2131558424;
        public static final int list_item = 2131558479;
        public static final int masked = 2131558652;
        public static final int media_actions = 2131558634;
        public static final int middle = 2131558464;
        public static final int mini = 2131558462;
        public static final int multiply = 2131558439;
        public static final int navigation_header_container = 2131558621;
        public static final int never = 2131558468;
        public static final int none = 2131558423;
        public static final int normal = 2131558425;
        public static final int notification_background = 2131558643;
        public static final int notification_main_column = 2131558637;
        public static final int notification_main_column_container = 2131558636;
        public static final int packed = 2131558418;
        public static final int parallax = 2131558456;
        public static final int parent = 2131558415;
        public static final int parentPanel = 2131558484;
        public static final int pin = 2131558457;
        public static final int playAudBoxCheck = 2131558589;
        public static final int practiceSettingsCheck0 = 2131558608;
        public static final int practiceSettingsCheck1 = 2131558610;
        public static final int practiceSettingsCheck2 = 2131558612;
        public static final int practiceSettingsCheck3 = 2131558614;
        public static final int practiceSettingsText0 = 2131558606;
        public static final int practiceSettingsText1 = 2131558607;
        public static final int practiceSettingsText2 = 2131558609;
        public static final int practiceSettingsText3 = 2131558611;
        public static final int practiceSettingsText4 = 2131558613;
        public static final int progress_circular = 2131558406;
        public static final int progress_horizontal = 2131558407;
        public static final int rArrow = 2131558551;
        public static final int radio = 2131558500;
        public static final int right = 2131558453;
        public static final int right_icon = 2131558642;
        public static final int right_side = 2131558638;
        public static final int screen = 2131558440;
        public static final int scroll = 2131558436;
        public static final int scrollIndicatorDown = 2131558490;
        public static final int scrollIndicatorUp = 2131558486;
        public static final int scrollView = 2131558487;
        public static final int scrollable = 2131558471;
        public static final int search_badge = 2131558511;
        public static final int search_bar = 2131558510;
        public static final int search_button = 2131558512;
        public static final int search_close_btn = 2131558517;
        public static final int search_edit_frame = 2131558513;
        public static final int search_go_btn = 2131558519;
        public static final int search_mag_icon = 2131558514;
        public static final int search_plate = 2131558515;
        public static final int search_src_text = 2131558516;
        public static final int search_voice_btn = 2131558520;
        public static final int select_dialog_listview = 2131558521;
        public static final int settingsBut = 2131558545;
        public static final int settingsOkBut = 2131558605;
        public static final int shortcut = 2131558499;
        public static final int showCustom = 2131558429;
        public static final int showEnglishBoxCheck = 2131558592;
        public static final int showHome = 2131558430;
        public static final int showPinyinBoxCheck = 2131558595;
        public static final int showPinyinButtonBoxCheck = 2131558598;
        public static final int showTextBoxCheck = 2131558601;
        public static final int showTitle = 2131558431;
        public static final int smallLabel = 2131558615;
        public static final int snackbar_action = 2131558620;
        public static final int snackbar_text = 2131558619;
        public static final int snap = 2131558437;
        public static final int spacer = 2131558483;
        public static final int split_action_bar = 2131558408;
        public static final int spread = 2131558416;
        public static final int spread_inside = 2131558419;
        public static final int src_atop = 2131558441;
        public static final int src_in = 2131558442;
        public static final int src_over = 2131558443;
        public static final int start = 2131558454;
        public static final int status_bar_latest_event_content = 2131558633;
        public static final int submenuarrow = 2131558501;
        public static final int submit_area = 2131558518;
        public static final int tabMode = 2131558426;
        public static final int text = 2131558649;
        public static final int text2 = 2131558647;
        public static final int textSpacerNoButtons = 2131558489;
        public static final int textSpacerNoTitle = 2131558488;
        public static final int text_input_password_toggle = 2131558626;
        public static final int textinput_counter = 2131558409;
        public static final int textinput_error = 2131558410;
        public static final int time = 2131558639;
        public static final int title = 2131558481;
        public static final int titleDividerNoCustom = 2131558496;
        public static final int title_template = 2131558494;
        public static final int top = 2131558455;
        public static final int topPanel = 2131558493;
        public static final int touch_outside = 2131558617;
        public static final int transition_current_scene = 2131558411;
        public static final int transition_scene_layoutid_cache = 2131558412;
        public static final int up = 2131558413;
        public static final int useLogo = 2131558432;
        public static final int view_offset_helper = 2131558414;
        public static final int visible = 2131558651;
        public static final int withText = 2131558469;
        public static final int wrap = 2131558417;
        public static final int wrap_content = 2131558444;
    }

    public static final class integer {
        public static final int abc_config_activityDefaultDur = 2131427329;
        public static final int abc_config_activityShortDur = 2131427330;
        public static final int app_bar_elevation_anim_duration = 2131427331;
        public static final int bottom_sheet_slide_duration = 2131427332;
        public static final int cancel_button_image_alpha = 2131427333;
        public static final int databaseVersion = 2131427334;
        public static final int design_snackbar_text_max_lines = 2131427328;
        public static final int hide_password_duration = 2131427335;
        public static final int show_password_duration = 2131427336;
        public static final int status_bar_notification_info_maxnum = 2131427337;
    }

    public static final class layout {
        public static final int abc_action_bar_title_item = 2130968576;
        public static final int abc_action_bar_up_container = 2130968577;
        public static final int abc_action_bar_view_list_nav_layout = 2130968578;
        public static final int abc_action_menu_item_layout = 2130968579;
        public static final int abc_action_menu_layout = 2130968580;
        public static final int abc_action_mode_bar = 2130968581;
        public static final int abc_action_mode_close_item_material = 2130968582;
        public static final int abc_activity_chooser_view = 2130968583;
        public static final int abc_activity_chooser_view_list_item = 2130968584;
        public static final int abc_alert_dialog_button_bar_material = 2130968585;
        public static final int abc_alert_dialog_material = 2130968586;
        public static final int abc_alert_dialog_title_material = 2130968587;
        public static final int abc_dialog_title_material = 2130968588;
        public static final int abc_expanded_menu_layout = 2130968589;
        public static final int abc_list_menu_item_checkbox = 2130968590;
        public static final int abc_list_menu_item_icon = 2130968591;
        public static final int abc_list_menu_item_layout = 2130968592;
        public static final int abc_list_menu_item_radio = 2130968593;
        public static final int abc_popup_menu_header_item_layout = 2130968594;
        public static final int abc_popup_menu_item_layout = 2130968595;
        public static final int abc_screen_content_include = 2130968596;
        public static final int abc_screen_simple = 2130968597;
        public static final int abc_screen_simple_overlay_action_mode = 2130968598;
        public static final int abc_screen_toolbar = 2130968599;
        public static final int abc_search_dropdown_item_icons_2line = 2130968600;
        public static final int abc_search_view = 2130968601;
        public static final int abc_select_dialog_material = 2130968602;
        public static final int activity_end = 2130968603;
        public static final int activity_learn = 2130968604;
        public static final int activity_main = 2130968605;
        public static final int activity_menu = 2130968606;
        public static final int activity_practice = 2130968607;
        public static final int activity_review = 2130968608;
        public static final int activity_settings_learn = 2130968609;
        public static final int activity_settings_practice = 2130968610;
        public static final int activity_settings_review = 2130968611;
        public static final int design_bottom_navigation_item = 2130968612;
        public static final int design_bottom_sheet_dialog = 2130968613;
        public static final int design_layout_snackbar = 2130968614;
        public static final int design_layout_snackbar_include = 2130968615;
        public static final int design_layout_tab_icon = 2130968616;
        public static final int design_layout_tab_text = 2130968617;
        public static final int design_menu_item_action_area = 2130968618;
        public static final int design_navigation_item = 2130968619;
        public static final int design_navigation_item_header = 2130968620;
        public static final int design_navigation_item_separator = 2130968621;
        public static final int design_navigation_item_subheader = 2130968622;
        public static final int design_navigation_menu = 2130968623;
        public static final int design_navigation_menu_item = 2130968624;
        public static final int design_text_input_password_icon = 2130968625;
        public static final int fragment_count_down = 2130968626;
        public static final int notification_action = 2130968627;
        public static final int notification_action_tombstone = 2130968628;
        public static final int notification_media_action = 2130968629;
        public static final int notification_media_cancel_action = 2130968630;
        public static final int notification_template_big_media = 2130968631;
        public static final int notification_template_big_media_custom = 2130968632;
        public static final int notification_template_big_media_narrow = 2130968633;
        public static final int notification_template_big_media_narrow_custom = 2130968634;
        public static final int notification_template_custom_big = 2130968635;
        public static final int notification_template_icon_group = 2130968636;
        public static final int notification_template_lines_media = 2130968637;
        public static final int notification_template_media = 2130968638;
        public static final int notification_template_media_custom = 2130968639;
        public static final int notification_template_part_chronometer = 2130968640;
        public static final int notification_template_part_time = 2130968641;
        public static final int select_dialog_item_material = 2130968642;
        public static final int select_dialog_multichoice_material = 2130968643;
        public static final int select_dialog_singlechoice_material = 2130968644;
        public static final int support_simple_spinner_dropdown_item = 2130968645;
    }

    public static final class mipmap {
        public static final int ic_launcher = 2130903040;
        public static final int ic_launcher_round = 2130903041;
    }

    public static final class string {
        public static final int abc_action_bar_home_description = 2131165184;
        public static final int abc_action_bar_home_description_format = 2131165185;
        public static final int abc_action_bar_home_subtitle_description_format = 2131165186;
        public static final int abc_action_bar_up_description = 2131165187;
        public static final int abc_action_menu_overflow_description = 2131165188;
        public static final int abc_action_mode_done = 2131165189;
        public static final int abc_activity_chooser_view_see_all = 2131165190;
        public static final int abc_activitychooserview_choose_application = 2131165191;
        public static final int abc_capital_off = 2131165192;
        public static final int abc_capital_on = 2131165193;
        public static final int abc_font_family_body_1_material = 2131165205;
        public static final int abc_font_family_body_2_material = 2131165206;
        public static final int abc_font_family_button_material = 2131165207;
        public static final int abc_font_family_caption_material = 2131165208;
        public static final int abc_font_family_display_1_material = 2131165209;
        public static final int abc_font_family_display_2_material = 2131165210;
        public static final int abc_font_family_display_3_material = 2131165211;
        public static final int abc_font_family_display_4_material = 2131165212;
        public static final int abc_font_family_headline_material = 2131165213;
        public static final int abc_font_family_menu_material = 2131165214;
        public static final int abc_font_family_subhead_material = 2131165215;
        public static final int abc_font_family_title_material = 2131165216;
        public static final int abc_search_hint = **********;
        public static final int abc_searchview_description_clear = **********;
        public static final int abc_searchview_description_query = **********;
        public static final int abc_searchview_description_search = **********;
        public static final int abc_searchview_description_submit = **********;
        public static final int abc_searchview_description_voice = **********;
        public static final int abc_shareactionprovider_share_with = **********;
        public static final int abc_shareactionprovider_share_with_application = **********;
        public static final int abc_toolbar_collapse_description = **********;
        public static final int app_name = **********;
        public static final int appbar_scrolling_view_behavior = **********;
        public static final int bottom_sheet_behavior = **********;
        public static final int character_counter_pattern = **********;
        public static final int end_activity_info_learn = **********;
        public static final int end_activity_info_practice_complete = **********;
        public static final int end_activity_info_practice_sdmiss = **********;
        public static final int end_activity_info_practice_sdtimeout = **********;
        public static final int end_activity_info_practice_timeout = **********;
        public static final int end_activity_info_review = **********;
        public static final int password_toggle_content_description = **********;
        public static final int path_password_eye = 2131165228;
        public static final int path_password_eye_mask_strike_through = 2131165229;
        public static final int path_password_eye_mask_visible = 2131165230;
        public static final int path_password_strike_through = 2131165231;
        public static final int search_menu_title = 2131165203;
        public static final int status_bar_notification_info_overflow = 2131165204;
        public static final int title_activity_end = 2131165232;
        public static final int title_activity_learn = 2131165233;
        public static final int title_activity_learn_settings = 2131165234;
        public static final int title_activity_menu = 2131165235;
        public static final int title_activity_practice = 2131165236;
        public static final int title_activity_review = 2131165237;
        public static final int title_activity_settings_learn = 2131165238;
        public static final int title_activity_settings_practice = 2131165239;
        public static final int title_activity_settings_review = 2131165240;
    }

    public static final class style {
        public static final int AlertDialog_AppCompat = 2131296417;
        public static final int AlertDialog_AppCompat_Light = 2131296418;
        public static final int Animation_AppCompat_Dialog = 2131296419;
        public static final int Animation_AppCompat_DropDownUp = 2131296420;
        public static final int Animation_Design_BottomSheetDialog = 2131296421;
        public static final int AppTheme = 2131296422;
        public static final int Base_AlertDialog_AppCompat = 2131296423;
        public static final int Base_AlertDialog_AppCompat_Light = 2131296424;
        public static final int Base_Animation_AppCompat_Dialog = 2131296425;
        public static final int Base_Animation_AppCompat_DropDownUp = 2131296426;
        public static final int Base_DialogWindowTitleBackground_AppCompat = 2131296428;
        public static final int Base_DialogWindowTitle_AppCompat = 2131296427;
        public static final int Base_TextAppearance_AppCompat = 2131296320;
        public static final int Base_TextAppearance_AppCompat_Body1 = 2131296321;
        public static final int Base_TextAppearance_AppCompat_Body2 = 2131296322;
        public static final int Base_TextAppearance_AppCompat_Button = 2131296296;
        public static final int Base_TextAppearance_AppCompat_Caption = 2131296323;
        public static final int Base_TextAppearance_AppCompat_Display1 = 2131296324;
        public static final int Base_TextAppearance_AppCompat_Display2 = 2131296325;
        public static final int Base_TextAppearance_AppCompat_Display3 = 2131296326;
        public static final int Base_TextAppearance_AppCompat_Display4 = 2131296327;
        public static final int Base_TextAppearance_AppCompat_Headline = 2131296328;
        public static final int Base_TextAppearance_AppCompat_Inverse = 2131296268;
        public static final int Base_TextAppearance_AppCompat_Large = 2131296329;
        public static final int Base_TextAppearance_AppCompat_Large_Inverse = 2131296269;
        public static final int Base_TextAppearance_AppCompat_Light_Widget_PopupMenu_Large = 2131296330;
        public static final int Base_TextAppearance_AppCompat_Light_Widget_PopupMenu_Small = 2131296331;
        public static final int Base_TextAppearance_AppCompat_Medium = 2131296332;
        public static final int Base_TextAppearance_AppCompat_Medium_Inverse = 2131296270;
        public static final int Base_TextAppearance_AppCompat_Menu = 2131296333;
        public static final int Base_TextAppearance_AppCompat_SearchResult = 2131296429;
        public static final int Base_TextAppearance_AppCompat_SearchResult_Subtitle = 2131296334;
        public static final int Base_TextAppearance_AppCompat_SearchResult_Title = 2131296335;
        public static final int Base_TextAppearance_AppCompat_Small = 2131296336;
        public static final int Base_TextAppearance_AppCompat_Small_Inverse = 2131296271;
        public static final int Base_TextAppearance_AppCompat_Subhead = 2131296337;
        public static final int Base_TextAppearance_AppCompat_Subhead_Inverse = 2131296272;
        public static final int Base_TextAppearance_AppCompat_Title = 2131296338;
        public static final int Base_TextAppearance_AppCompat_Title_Inverse = 2131296273;
        public static final int Base_TextAppearance_AppCompat_Widget_ActionBar_Menu = 2131296406;
        public static final int Base_TextAppearance_AppCompat_Widget_ActionBar_Subtitle = 2131296339;
        public static final int Base_TextAppearance_AppCompat_Widget_ActionBar_Subtitle_Inverse = 2131296340;
        public static final int Base_TextAppearance_AppCompat_Widget_ActionBar_Title = 2131296341;
        public static final int Base_TextAppearance_AppCompat_Widget_ActionBar_Title_Inverse = 2131296342;
        public static final int Base_TextAppearance_AppCompat_Widget_ActionMode_Subtitle = 2131296343;
        public static final int Base_TextAppearance_AppCompat_Widget_ActionMode_Title = 2131296344;
        public static final int Base_TextAppearance_AppCompat_Widget_Button = 2131296345;
        public static final int Base_TextAppearance_AppCompat_Widget_Button_Borderless_Colored = 2131296413;
        public static final int Base_TextAppearance_AppCompat_Widget_Button_Colored = 2131296414;
        public static final int Base_TextAppearance_AppCompat_Widget_Button_Inverse = 2131296407;
        public static final int Base_TextAppearance_AppCompat_Widget_DropDownItem = 2131296430;
        public static final int Base_TextAppearance_AppCompat_Widget_PopupMenu_Header = 2131296346;
        public static final int Base_TextAppearance_AppCompat_Widget_PopupMenu_Large = 2131296347;
        public static final int Base_TextAppearance_AppCompat_Widget_PopupMenu_Small = 2131296348;
        public static final int Base_TextAppearance_AppCompat_Widget_Switch = 2131296349;
        public static final int Base_TextAppearance_AppCompat_Widget_TextView_SpinnerItem = 2131296350;
        public static final int Base_TextAppearance_Widget_AppCompat_ExpandedMenu_Item = 2131296431;
        public static final int Base_TextAppearance_Widget_AppCompat_Toolbar_Subtitle = 2131296351;
        public static final int Base_TextAppearance_Widget_AppCompat_Toolbar_Title = 2131296352;
        public static final int Base_ThemeOverlay_AppCompat = 2131296436;
        public static final int Base_ThemeOverlay_AppCompat_ActionBar = 2131296437;
        public static final int Base_ThemeOverlay_AppCompat_Dark = 2131296438;
        public static final int Base_ThemeOverlay_AppCompat_Dark_ActionBar = 2131296439;
        public static final int Base_ThemeOverlay_AppCompat_Dialog = 2131296280;
        public static final int Base_ThemeOverlay_AppCompat_Dialog_Alert = 2131296281;
        public static final int Base_ThemeOverlay_AppCompat_Light = 2131296440;
        public static final int Base_Theme_AppCompat = 2131296353;
        public static final int Base_Theme_AppCompat_CompactMenu = 2131296432;
        public static final int Base_Theme_AppCompat_Dialog = 2131296274;
        public static final int Base_Theme_AppCompat_DialogWhenLarge = 2131296258;
        public static final int Base_Theme_AppCompat_Dialog_Alert = 2131296275;
        public static final int Base_Theme_AppCompat_Dialog_FixedSize = 2131296433;
        public static final int Base_Theme_AppCompat_Dialog_MinWidth = 2131296276;
        public static final int Base_Theme_AppCompat_Light = 2131296354;
        public static final int Base_Theme_AppCompat_Light_DarkActionBar = 2131296434;
        public static final int Base_Theme_AppCompat_Light_Dialog = 2131296277;
        public static final int Base_Theme_AppCompat_Light_DialogWhenLarge = 2131296259;
        public static final int Base_Theme_AppCompat_Light_Dialog_Alert = 2131296278;
        public static final int Base_Theme_AppCompat_Light_Dialog_FixedSize = 2131296435;
        public static final int Base_Theme_AppCompat_Light_Dialog_MinWidth = 2131296279;
        public static final int Base_V11_ThemeOverlay_AppCompat_Dialog = 2131296284;
        public static final int Base_V11_Theme_AppCompat_Dialog = 2131296282;
        public static final int Base_V11_Theme_AppCompat_Light_Dialog = 2131296283;
        public static final int Base_V12_Widget_AppCompat_AutoCompleteTextView = 2131296292;
        public static final int Base_V12_Widget_AppCompat_EditText = 2131296293;
        public static final int Base_V21_ThemeOverlay_AppCompat_Dialog = 2131296359;
        public static final int Base_V21_Theme_AppCompat = 2131296355;
        public static final int Base_V21_Theme_AppCompat_Dialog = 2131296356;
        public static final int Base_V21_Theme_AppCompat_Light = 2131296357;
        public static final int Base_V21_Theme_AppCompat_Light_Dialog = 2131296358;
        public static final int Base_V22_Theme_AppCompat = 2131296404;
        public static final int Base_V22_Theme_AppCompat_Light = 2131296405;
        public static final int Base_V23_Theme_AppCompat = 2131296408;
        public static final int Base_V23_Theme_AppCompat_Light = 2131296409;
        public static final int Base_V7_ThemeOverlay_AppCompat_Dialog = 2131296445;
        public static final int Base_V7_Theme_AppCompat = 2131296441;
        public static final int Base_V7_Theme_AppCompat_Dialog = 2131296442;
        public static final int Base_V7_Theme_AppCompat_Light = 2131296443;
        public static final int Base_V7_Theme_AppCompat_Light_Dialog = 2131296444;
        public static final int Base_V7_Widget_AppCompat_AutoCompleteTextView = 2131296446;
        public static final int Base_V7_Widget_AppCompat_EditText = 2131296447;
        public static final int Base_Widget_AppCompat_ActionBar = 2131296448;
        public static final int Base_Widget_AppCompat_ActionBar_Solid = 2131296449;
        public static final int Base_Widget_AppCompat_ActionBar_TabBar = 2131296450;
        public static final int Base_Widget_AppCompat_ActionBar_TabText = 2131296360;
        public static final int Base_Widget_AppCompat_ActionBar_TabView = 2131296361;
        public static final int Base_Widget_AppCompat_ActionButton = 2131296362;
        public static final int Base_Widget_AppCompat_ActionButton_CloseMode = 2131296363;
        public static final int Base_Widget_AppCompat_ActionButton_Overflow = 2131296364;
        public static final int Base_Widget_AppCompat_ActionMode = 2131296451;
        public static final int Base_Widget_AppCompat_ActivityChooserView = 2131296452;
        public static final int Base_Widget_AppCompat_AutoCompleteTextView = 2131296294;
        public static final int Base_Widget_AppCompat_Button = 2131296365;
        public static final int Base_Widget_AppCompat_ButtonBar = 2131296369;
        public static final int Base_Widget_AppCompat_ButtonBar_AlertDialog = 2131296454;
        public static final int Base_Widget_AppCompat_Button_Borderless = 2131296366;
        public static final int Base_Widget_AppCompat_Button_Borderless_Colored = 2131296367;
        public static final int Base_Widget_AppCompat_Button_ButtonBar_AlertDialog = 2131296453;
        public static final int Base_Widget_AppCompat_Button_Colored = 2131296410;
        public static final int Base_Widget_AppCompat_Button_Small = 2131296368;
        public static final int Base_Widget_AppCompat_CompoundButton_CheckBox = 2131296370;
        public static final int Base_Widget_AppCompat_CompoundButton_RadioButton = 2131296371;
        public static final int Base_Widget_AppCompat_CompoundButton_Switch = 2131296455;
        public static final int Base_Widget_AppCompat_DrawerArrowToggle = 2131296256;
        public static final int Base_Widget_AppCompat_DrawerArrowToggle_Common = 2131296456;
        public static final int Base_Widget_AppCompat_DropDownItem_Spinner = 2131296372;
        public static final int Base_Widget_AppCompat_EditText = 2131296295;
        public static final int Base_Widget_AppCompat_ImageButton = 2131296373;
        public static final int Base_Widget_AppCompat_Light_ActionBar = 2131296457;
        public static final int Base_Widget_AppCompat_Light_ActionBar_Solid = 2131296458;
        public static final int Base_Widget_AppCompat_Light_ActionBar_TabBar = 2131296459;
        public static final int Base_Widget_AppCompat_Light_ActionBar_TabText = 2131296374;
        public static final int Base_Widget_AppCompat_Light_ActionBar_TabText_Inverse = 2131296375;
        public static final int Base_Widget_AppCompat_Light_ActionBar_TabView = 2131296376;
        public static final int Base_Widget_AppCompat_Light_PopupMenu = 2131296377;
        public static final int Base_Widget_AppCompat_Light_PopupMenu_Overflow = 2131296378;
        public static final int Base_Widget_AppCompat_ListMenuView = 2131296460;
        public static final int Base_Widget_AppCompat_ListPopupWindow = 2131296379;
        public static final int Base_Widget_AppCompat_ListView = 2131296380;
        public static final int Base_Widget_AppCompat_ListView_DropDown = 2131296381;
        public static final int Base_Widget_AppCompat_ListView_Menu = 2131296382;
        public static final int Base_Widget_AppCompat_PopupMenu = 2131296383;
        public static final int Base_Widget_AppCompat_PopupMenu_Overflow = 2131296384;
        public static final int Base_Widget_AppCompat_PopupWindow = 2131296461;
        public static final int Base_Widget_AppCompat_ProgressBar = 2131296285;
        public static final int Base_Widget_AppCompat_ProgressBar_Horizontal = 2131296286;
        public static final int Base_Widget_AppCompat_RatingBar = 2131296385;
        public static final int Base_Widget_AppCompat_RatingBar_Indicator = 2131296411;
        public static final int Base_Widget_AppCompat_RatingBar_Small = 2131296412;
        public static final int Base_Widget_AppCompat_SearchView = 2131296462;
        public static final int Base_Widget_AppCompat_SearchView_ActionBar = 2131296463;
        public static final int Base_Widget_AppCompat_SeekBar = 2131296386;
        public static final int Base_Widget_AppCompat_SeekBar_Discrete = 2131296464;
        public static final int Base_Widget_AppCompat_Spinner = 2131296387;
        public static final int Base_Widget_AppCompat_Spinner_Underlined = 2131296260;
        public static final int Base_Widget_AppCompat_TextView_SpinnerItem = 2131296388;
        public static final int Base_Widget_AppCompat_Toolbar = 2131296465;
        public static final int Base_Widget_AppCompat_Toolbar_Button_Navigation = 2131296389;
        public static final int Base_Widget_Design_AppBarLayout = 2131296466;
        public static final int Base_Widget_Design_TabLayout = 2131296467;
        public static final int Platform_AppCompat = 2131296287;
        public static final int Platform_AppCompat_Light = 2131296288;
        public static final int Platform_ThemeOverlay_AppCompat = 2131296390;
        public static final int Platform_ThemeOverlay_AppCompat_Dark = 2131296391;
        public static final int Platform_ThemeOverlay_AppCompat_Light = 2131296392;
        public static final int Platform_V11_AppCompat = 2131296289;
        public static final int Platform_V11_AppCompat_Light = 2131296290;
        public static final int Platform_V14_AppCompat = 2131296297;
        public static final int Platform_V14_AppCompat_Light = 2131296298;
        public static final int Platform_V21_AppCompat = 2131296393;
        public static final int Platform_V21_AppCompat_Light = 2131296394;
        public static final int Platform_V25_AppCompat = 2131296415;
        public static final int Platform_V25_AppCompat_Light = 2131296416;
        public static final int Platform_Widget_AppCompat_Spinner = 2131296291;
        public static final int RtlOverlay_DialogWindowTitle_AppCompat = 2131296306;
        public static final int RtlOverlay_Widget_AppCompat_ActionBar_TitleItem = 2131296307;
        public static final int RtlOverlay_Widget_AppCompat_DialogTitle_Icon = 2131296308;
        public static final int RtlOverlay_Widget_AppCompat_PopupMenuItem = 2131296309;
        public static final int RtlOverlay_Widget_AppCompat_PopupMenuItem_InternalGroup = 2131296310;
        public static final int RtlOverlay_Widget_AppCompat_PopupMenuItem_Text = 2131296311;
        public static final int RtlOverlay_Widget_AppCompat_SearchView_MagIcon = 2131296317;
        public static final int RtlOverlay_Widget_AppCompat_Search_DropDown = 2131296312;
        public static final int RtlOverlay_Widget_AppCompat_Search_DropDown_Icon1 = 2131296313;
        public static final int RtlOverlay_Widget_AppCompat_Search_DropDown_Icon2 = 2131296314;
        public static final int RtlOverlay_Widget_AppCompat_Search_DropDown_Query = 2131296315;
        public static final int RtlOverlay_Widget_AppCompat_Search_DropDown_Text = 2131296316;
        public static final int RtlUnderlay_Widget_AppCompat_ActionButton = 2131296318;
        public static final int RtlUnderlay_Widget_AppCompat_ActionButton_Overflow = 2131296319;
        public static final int TextAppearance_AppCompat = 2131296468;
        public static final int TextAppearance_AppCompat_Body1 = 2131296469;
        public static final int TextAppearance_AppCompat_Body2 = 2131296470;
        public static final int TextAppearance_AppCompat_Button = 2131296471;
        public static final int TextAppearance_AppCompat_Caption = 2131296472;
        public static final int TextAppearance_AppCompat_Display1 = 2131296473;
        public static final int TextAppearance_AppCompat_Display2 = 2131296474;
        public static final int TextAppearance_AppCompat_Display3 = 2131296475;
        public static final int TextAppearance_AppCompat_Display4 = 2131296476;
        public static final int TextAppearance_AppCompat_Headline = 2131296477;
        public static final int TextAppearance_AppCompat_Inverse = 2131296478;
        public static final int TextAppearance_AppCompat_Large = 2131296479;
        public static final int TextAppearance_AppCompat_Large_Inverse = 2131296480;
        public static final int TextAppearance_AppCompat_Light_SearchResult_Subtitle = 2131296481;
        public static final int TextAppearance_AppCompat_Light_SearchResult_Title = 2131296482;
        public static final int TextAppearance_AppCompat_Light_Widget_PopupMenu_Large = 2131296483;
        public static final int TextAppearance_AppCompat_Light_Widget_PopupMenu_Small = 2131296484;
        public static final int TextAppearance_AppCompat_Medium = 2131296485;
        public static final int TextAppearance_AppCompat_Medium_Inverse = 2131296486;
        public static final int TextAppearance_AppCompat_Menu = 2131296487;
        public static final int TextAppearance_AppCompat_Notification = 2131296299;
        public static final int TextAppearance_AppCompat_Notification_Info = 2131296395;
        public static final int TextAppearance_AppCompat_Notification_Info_Media = 2131296396;
        public static final int TextAppearance_AppCompat_Notification_Line2 = 2131296488;
        public static final int TextAppearance_AppCompat_Notification_Line2_Media = 2131296489;
        public static final int TextAppearance_AppCompat_Notification_Media = 2131296397;
        public static final int TextAppearance_AppCompat_Notification_Time = 2131296398;
        public static final int TextAppearance_AppCompat_Notification_Time_Media = 2131296399;
        public static final int TextAppearance_AppCompat_Notification_Title = 2131296300;
        public static final int TextAppearance_AppCompat_Notification_Title_Media = 2131296400;
        public static final int TextAppearance_AppCompat_SearchResult_Subtitle = 2131296490;
        public static final int TextAppearance_AppCompat_SearchResult_Title = 2131296491;
        public static final int TextAppearance_AppCompat_Small = 2131296492;
        public static final int TextAppearance_AppCompat_Small_Inverse = 2131296493;
        public static final int TextAppearance_AppCompat_Subhead = 2131296494;
        public static final int TextAppearance_AppCompat_Subhead_Inverse = 2131296495;
        public static final int TextAppearance_AppCompat_Title = 2131296496;
        public static final int TextAppearance_AppCompat_Title_Inverse = 2131296497;
        public static final int TextAppearance_AppCompat_Widget_ActionBar_Menu = 2131296498;
        public static final int TextAppearance_AppCompat_Widget_ActionBar_Subtitle = 2131296499;
        public static final int TextAppearance_AppCompat_Widget_ActionBar_Subtitle_Inverse = 2131296500;
        public static final int TextAppearance_AppCompat_Widget_ActionBar_Title = 2131296501;
        public static final int TextAppearance_AppCompat_Widget_ActionBar_Title_Inverse = 2131296502;
        public static final int TextAppearance_AppCompat_Widget_ActionMode_Subtitle = 2131296503;
        public static final int TextAppearance_AppCompat_Widget_ActionMode_Subtitle_Inverse = 2131296504;
        public static final int TextAppearance_AppCompat_Widget_ActionMode_Title = 2131296505;
        public static final int TextAppearance_AppCompat_Widget_ActionMode_Title_Inverse = 2131296506;
        public static final int TextAppearance_AppCompat_Widget_Button = 2131296507;
        public static final int TextAppearance_AppCompat_Widget_Button_Borderless_Colored = 2131296508;
        public static final int TextAppearance_AppCompat_Widget_Button_Colored = 2131296509;
        public static final int TextAppearance_AppCompat_Widget_Button_Inverse = 2131296510;
        public static final int TextAppearance_AppCompat_Widget_DropDownItem = 2131296511;
        public static final int TextAppearance_AppCompat_Widget_PopupMenu_Header = 2131296512;
        public static final int TextAppearance_AppCompat_Widget_PopupMenu_Large = 2131296513;
        public static final int TextAppearance_AppCompat_Widget_PopupMenu_Small = 2131296514;
        public static final int TextAppearance_AppCompat_Widget_Switch = 2131296515;
        public static final int TextAppearance_AppCompat_Widget_TextView_SpinnerItem = 2131296516;
        public static final int TextAppearance_Design_CollapsingToolbar_Expanded = 2131296517;
        public static final int TextAppearance_Design_Counter = 2131296518;
        public static final int TextAppearance_Design_Counter_Overflow = 2131296519;
        public static final int TextAppearance_Design_Error = 2131296520;
        public static final int TextAppearance_Design_Hint = 2131296521;
        public static final int TextAppearance_Design_Snackbar_Message = 2131296522;
        public static final int TextAppearance_Design_Tab = 2131296523;
        public static final int TextAppearance_StatusBar_EventContent = 2131296301;
        public static final int TextAppearance_StatusBar_EventContent_Info = 2131296302;
        public static final int TextAppearance_StatusBar_EventContent_Line2 = 2131296303;
        public static final int TextAppearance_StatusBar_EventContent_Time = 2131296304;
        public static final int TextAppearance_StatusBar_EventContent_Title = 2131296305;
        public static final int TextAppearance_Widget_AppCompat_ExpandedMenu_Item = 2131296524;
        public static final int TextAppearance_Widget_AppCompat_Toolbar_Subtitle = 2131296525;
        public static final int TextAppearance_Widget_AppCompat_Toolbar_Title = 2131296526;
        public static final int ThemeOverlay_AppCompat = 2131296548;
        public static final int ThemeOverlay_AppCompat_ActionBar = 2131296549;
        public static final int ThemeOverlay_AppCompat_Dark = 2131296550;
        public static final int ThemeOverlay_AppCompat_Dark_ActionBar = 2131296551;
        public static final int ThemeOverlay_AppCompat_Dialog = 2131296552;
        public static final int ThemeOverlay_AppCompat_Dialog_Alert = 2131296553;
        public static final int ThemeOverlay_AppCompat_Light = 2131296554;
        public static final int Theme_AppCompat = 2131296527;
        public static final int Theme_AppCompat_CompactMenu = 2131296528;
        public static final int Theme_AppCompat_DayNight = 2131296261;
        public static final int Theme_AppCompat_DayNight_DarkActionBar = 2131296262;
        public static final int Theme_AppCompat_DayNight_Dialog = 2131296263;
        public static final int Theme_AppCompat_DayNight_DialogWhenLarge = 2131296266;
        public static final int Theme_AppCompat_DayNight_Dialog_Alert = 2131296264;
        public static final int Theme_AppCompat_DayNight_Dialog_MinWidth = 2131296265;
        public static final int Theme_AppCompat_DayNight_NoActionBar = 2131296267;
        public static final int Theme_AppCompat_Dialog = 2131296529;
        public static final int Theme_AppCompat_DialogWhenLarge = 2131296532;
        public static final int Theme_AppCompat_Dialog_Alert = 2131296530;
        public static final int Theme_AppCompat_Dialog_MinWidth = 2131296531;
        public static final int Theme_AppCompat_Light = 2131296533;
        public static final int Theme_AppCompat_Light_DarkActionBar = 2131296534;
        public static final int Theme_AppCompat_Light_Dialog = 2131296535;
        public static final int Theme_AppCompat_Light_DialogWhenLarge = 2131296538;
        public static final int Theme_AppCompat_Light_Dialog_Alert = 2131296536;
        public static final int Theme_AppCompat_Light_Dialog_MinWidth = 2131296537;
        public static final int Theme_AppCompat_Light_NoActionBar = 2131296539;
        public static final int Theme_AppCompat_NoActionBar = 2131296540;
        public static final int Theme_Design = 2131296541;
        public static final int Theme_Design_BottomSheetDialog = 2131296542;
        public static final int Theme_Design_Light = 2131296543;
        public static final int Theme_Design_Light_BottomSheetDialog = 2131296544;
        public static final int Theme_Design_Light_NoActionBar = 2131296545;
        public static final int Theme_Design_NoActionBar = 2131296546;
        public static final int Theme_Transparent = 2131296547;
        public static final int Widget_AppCompat_ActionBar = 2131296555;
        public static final int Widget_AppCompat_ActionBar_Solid = 2131296556;
        public static final int Widget_AppCompat_ActionBar_TabBar = 2131296557;
        public static final int Widget_AppCompat_ActionBar_TabText = 2131296558;
        public static final int Widget_AppCompat_ActionBar_TabView = 2131296559;
        public static final int Widget_AppCompat_ActionButton = 2131296560;
        public static final int Widget_AppCompat_ActionButton_CloseMode = 2131296561;
        public static final int Widget_AppCompat_ActionButton_Overflow = 2131296562;
        public static final int Widget_AppCompat_ActionMode = 2131296563;
        public static final int Widget_AppCompat_ActivityChooserView = 2131296564;
        public static final int Widget_AppCompat_AutoCompleteTextView = 2131296565;
        public static final int Widget_AppCompat_Button = 2131296566;
        public static final int Widget_AppCompat_ButtonBar = 2131296572;
        public static final int Widget_AppCompat_ButtonBar_AlertDialog = 2131296573;
        public static final int Widget_AppCompat_Button_Borderless = 2131296567;
        public static final int Widget_AppCompat_Button_Borderless_Colored = 2131296568;
        public static final int Widget_AppCompat_Button_ButtonBar_AlertDialog = 2131296569;
        public static final int Widget_AppCompat_Button_Colored = 2131296570;
        public static final int Widget_AppCompat_Button_Small = 2131296571;
        public static final int Widget_AppCompat_CompoundButton_CheckBox = 2131296574;
        public static final int Widget_AppCompat_CompoundButton_RadioButton = 2131296575;
        public static final int Widget_AppCompat_CompoundButton_Switch = 2131296576;
        public static final int Widget_AppCompat_DrawerArrowToggle = 2131296577;
        public static final int Widget_AppCompat_DropDownItem_Spinner = 2131296578;
        public static final int Widget_AppCompat_EditText = 2131296579;
        public static final int Widget_AppCompat_ImageButton = 2131296580;
        public static final int Widget_AppCompat_Light_ActionBar = 2131296581;
        public static final int Widget_AppCompat_Light_ActionBar_Solid = 2131296582;
        public static final int Widget_AppCompat_Light_ActionBar_Solid_Inverse = 2131296583;
        public static final int Widget_AppCompat_Light_ActionBar_TabBar = 2131296584;
        public static final int Widget_AppCompat_Light_ActionBar_TabBar_Inverse = 2131296585;
        public static final int Widget_AppCompat_Light_ActionBar_TabText = 2131296586;
        public static final int Widget_AppCompat_Light_ActionBar_TabText_Inverse = 2131296587;
        public static final int Widget_AppCompat_Light_ActionBar_TabView = 2131296588;
        public static final int Widget_AppCompat_Light_ActionBar_TabView_Inverse = 2131296589;
        public static final int Widget_AppCompat_Light_ActionButton = 2131296590;
        public static final int Widget_AppCompat_Light_ActionButton_CloseMode = 2131296591;
        public static final int Widget_AppCompat_Light_ActionButton_Overflow = 2131296592;
        public static final int Widget_AppCompat_Light_ActionMode_Inverse = 2131296593;
        public static final int Widget_AppCompat_Light_ActivityChooserView = 2131296594;
        public static final int Widget_AppCompat_Light_AutoCompleteTextView = 2131296595;
        public static final int Widget_AppCompat_Light_DropDownItem_Spinner = 2131296596;
        public static final int Widget_AppCompat_Light_ListPopupWindow = 2131296597;
        public static final int Widget_AppCompat_Light_ListView_DropDown = 2131296598;
        public static final int Widget_AppCompat_Light_PopupMenu = 2131296599;
        public static final int Widget_AppCompat_Light_PopupMenu_Overflow = 2131296600;
        public static final int Widget_AppCompat_Light_SearchView = 2131296601;
        public static final int Widget_AppCompat_Light_Spinner_DropDown_ActionBar = 2131296602;
        public static final int Widget_AppCompat_ListMenuView = 2131296603;
        public static final int Widget_AppCompat_ListPopupWindow = 2131296604;
        public static final int Widget_AppCompat_ListView = 2131296605;
        public static final int Widget_AppCompat_ListView_DropDown = 2131296606;
        public static final int Widget_AppCompat_ListView_Menu = 2131296607;
        public static final int Widget_AppCompat_NotificationActionContainer = 2131296401;
        public static final int Widget_AppCompat_NotificationActionText = 2131296402;
        public static final int Widget_AppCompat_PopupMenu = 2131296608;
        public static final int Widget_AppCompat_PopupMenu_Overflow = 2131296609;
        public static final int Widget_AppCompat_PopupWindow = 2131296610;
        public static final int Widget_AppCompat_ProgressBar = 2131296611;
        public static final int Widget_AppCompat_ProgressBar_Horizontal = 2131296612;
        public static final int Widget_AppCompat_RatingBar = 2131296613;
        public static final int Widget_AppCompat_RatingBar_Indicator = 2131296614;
        public static final int Widget_AppCompat_RatingBar_Small = 2131296615;
        public static final int Widget_AppCompat_SearchView = 2131296616;
        public static final int Widget_AppCompat_SearchView_ActionBar = 2131296617;
        public static final int Widget_AppCompat_SeekBar = 2131296618;
        public static final int Widget_AppCompat_SeekBar_Discrete = 2131296619;
        public static final int Widget_AppCompat_Spinner = 2131296620;
        public static final int Widget_AppCompat_Spinner_DropDown = 2131296621;
        public static final int Widget_AppCompat_Spinner_DropDown_ActionBar = 2131296622;
        public static final int Widget_AppCompat_Spinner_Underlined = 2131296623;
        public static final int Widget_AppCompat_TextView_SpinnerItem = 2131296624;
        public static final int Widget_AppCompat_Toolbar = 2131296625;
        public static final int Widget_AppCompat_Toolbar_Button_Navigation = 2131296626;
        public static final int Widget_Design_AppBarLayout = 2131296403;
        public static final int Widget_Design_BottomNavigationView = 2131296627;
        public static final int Widget_Design_BottomSheet_Modal = 2131296628;
        public static final int Widget_Design_CollapsingToolbar = 2131296629;
        public static final int Widget_Design_CoordinatorLayout = 2131296630;
        public static final int Widget_Design_FloatingActionButton = 2131296631;
        public static final int Widget_Design_NavigationView = 2131296632;
        public static final int Widget_Design_ScrimInsetsFrameLayout = 2131296633;
        public static final int Widget_Design_Snackbar = 2131296634;
        public static final int Widget_Design_TabLayout = 2131296257;
        public static final int Widget_Design_TextInputLayout = 2131296635;
    }

    public static final class styleable {
        public static final int[] ActionBar = {R.attr.height, R.attr.title, R.attr.navigationMode, R.attr.displayOptions, R.attr.subtitle, R.attr.titleTextStyle, R.attr.subtitleTextStyle, R.attr.icon, R.attr.logo, R.attr.divider, R.attr.background, R.attr.backgroundStacked, R.attr.backgroundSplit, R.attr.customNavigationLayout, R.attr.homeLayout, R.attr.progressBarStyle, R.attr.indeterminateProgressStyle, R.attr.progressBarPadding, R.attr.itemPadding, R.attr.hideOnContentScroll, R.attr.contentInsetStart, R.attr.contentInsetEnd, R.attr.contentInsetLeft, R.attr.contentInsetRight, R.attr.contentInsetStartWithNavigation, R.attr.contentInsetEndWithActions, R.attr.elevation, R.attr.popupTheme, R.attr.homeAsUpIndicator};
        public static final int[] ActionBarLayout = {16842931};
        public static final int ActionBarLayout_android_layout_gravity = 0;
        public static final int ActionBar_background = 10;
        public static final int ActionBar_backgroundSplit = 12;
        public static final int ActionBar_backgroundStacked = 11;
        public static final int ActionBar_contentInsetEnd = 21;
        public static final int ActionBar_contentInsetEndWithActions = 25;
        public static final int ActionBar_contentInsetLeft = 22;
        public static final int ActionBar_contentInsetRight = 23;
        public static final int ActionBar_contentInsetStart = 20;
        public static final int ActionBar_contentInsetStartWithNavigation = 24;
        public static final int ActionBar_customNavigationLayout = 13;
        public static final int ActionBar_displayOptions = 3;
        public static final int ActionBar_divider = 9;
        public static final int ActionBar_elevation = 26;
        public static final int ActionBar_height = 0;
        public static final int ActionBar_hideOnContentScroll = 19;
        public static final int ActionBar_homeAsUpIndicator = 28;
        public static final int ActionBar_homeLayout = 14;
        public static final int ActionBar_icon = 7;
        public static final int ActionBar_indeterminateProgressStyle = 16;
        public static final int ActionBar_itemPadding = 18;
        public static final int ActionBar_logo = 8;
        public static final int ActionBar_navigationMode = 2;
        public static final int ActionBar_popupTheme = 27;
        public static final int ActionBar_progressBarPadding = 17;
        public static final int ActionBar_progressBarStyle = 15;
        public static final int ActionBar_subtitle = 4;
        public static final int ActionBar_subtitleTextStyle = 6;
        public static final int ActionBar_title = 1;
        public static final int ActionBar_titleTextStyle = 5;
        public static final int[] ActionMenuItemView = {16843071};
        public static final int ActionMenuItemView_android_minWidth = 0;
        public static final int[] ActionMenuView = new int[0];
        public static final int[] ActionMode = {R.attr.height, R.attr.titleTextStyle, R.attr.subtitleTextStyle, R.attr.background, R.attr.backgroundSplit, R.attr.closeItemLayout};
        public static final int ActionMode_background = 3;
        public static final int ActionMode_backgroundSplit = 4;
        public static final int ActionMode_closeItemLayout = 5;
        public static final int ActionMode_height = 0;
        public static final int ActionMode_subtitleTextStyle = 2;
        public static final int ActionMode_titleTextStyle = 1;
        public static final int[] ActivityChooserView = {R.attr.initialActivityCount, R.attr.expandActivityOverflowButtonDrawable};
        public static final int ActivityChooserView_expandActivityOverflowButtonDrawable = 1;
        public static final int ActivityChooserView_initialActivityCount = 0;
        public static final int[] AlertDialog = {16842994, R.attr.buttonPanelSideLayout, R.attr.listLayout, R.attr.multiChoiceItemLayout, R.attr.singleChoiceItemLayout, R.attr.listItemLayout, R.attr.showTitle};
        public static final int AlertDialog_android_layout = 0;
        public static final int AlertDialog_buttonPanelSideLayout = 1;
        public static final int AlertDialog_listItemLayout = 5;
        public static final int AlertDialog_listLayout = 2;
        public static final int AlertDialog_multiChoiceItemLayout = 3;
        public static final int AlertDialog_showTitle = 6;
        public static final int AlertDialog_singleChoiceItemLayout = 4;
        public static final int[] AppBarLayout = {16842964, R.attr.elevation, R.attr.expanded};
        public static final int[] AppBarLayoutStates = {R.attr.state_collapsed, R.attr.state_collapsible};
        public static final int AppBarLayoutStates_state_collapsed = 0;
        public static final int AppBarLayoutStates_state_collapsible = 1;
        public static final int[] AppBarLayout_Layout = {R.attr.layout_scrollFlags, R.attr.layout_scrollInterpolator};
        public static final int AppBarLayout_Layout_layout_scrollFlags = 0;
        public static final int AppBarLayout_Layout_layout_scrollInterpolator = 1;
        public static final int AppBarLayout_android_background = 0;
        public static final int AppBarLayout_elevation = 1;
        public static final int AppBarLayout_expanded = 2;
        public static final int[] AppCompatImageView = {16843033, R.attr.srcCompat};
        public static final int AppCompatImageView_android_src = 0;
        public static final int AppCompatImageView_srcCompat = 1;
        public static final int[] AppCompatSeekBar = {16843074, R.attr.tickMark, R.attr.tickMarkTint, R.attr.tickMarkTintMode};
        public static final int AppCompatSeekBar_android_thumb = 0;
        public static final int AppCompatSeekBar_tickMark = 1;
        public static final int AppCompatSeekBar_tickMarkTint = 2;
        public static final int AppCompatSeekBar_tickMarkTintMode = 3;
        public static final int[] AppCompatTextHelper = {16842804, 16843117, 16843118, 16843119, 16843120, 16843666, 16843667};
        public static final int AppCompatTextHelper_android_drawableBottom = 2;
        public static final int AppCompatTextHelper_android_drawableEnd = 6;
        public static final int AppCompatTextHelper_android_drawableLeft = 3;
        public static final int AppCompatTextHelper_android_drawableRight = 4;
        public static final int AppCompatTextHelper_android_drawableStart = 5;
        public static final int AppCompatTextHelper_android_drawableTop = 1;
        public static final int AppCompatTextHelper_android_textAppearance = 0;
        public static final int[] AppCompatTextView = {16842804, R.attr.textAllCaps};
        public static final int AppCompatTextView_android_textAppearance = 0;
        public static final int AppCompatTextView_textAllCaps = 1;
        public static final int[] AppCompatTheme = {16842839, 16842926, R.attr.windowActionBar, R.attr.windowNoTitle, R.attr.windowActionBarOverlay, R.attr.windowActionModeOverlay, R.attr.windowFixedWidthMajor, R.attr.windowFixedHeightMinor, R.attr.windowFixedWidthMinor, R.attr.windowFixedHeightMajor, R.attr.windowMinWidthMajor, R.attr.windowMinWidthMinor, R.attr.actionBarTabStyle, R.attr.actionBarTabBarStyle, R.attr.actionBarTabTextStyle, R.attr.actionOverflowButtonStyle, R.attr.actionOverflowMenuStyle, R.attr.actionBarPopupTheme, R.attr.actionBarStyle, R.attr.actionBarSplitStyle, R.attr.actionBarTheme, R.attr.actionBarWidgetTheme, R.attr.actionBarSize, R.attr.actionBarDivider, R.attr.actionBarItemBackground, R.attr.actionMenuTextAppearance, R.attr.actionMenuTextColor, R.attr.actionModeStyle, R.attr.actionModeCloseButtonStyle, R.attr.actionModeBackground, R.attr.actionModeSplitBackground, R.attr.actionModeCloseDrawable, R.attr.actionModeCutDrawable, R.attr.actionModeCopyDrawable, R.attr.actionModePasteDrawable, R.attr.actionModeSelectAllDrawable, R.attr.actionModeShareDrawable, R.attr.actionModeFindDrawable, R.attr.actionModeWebSearchDrawable, R.attr.actionModePopupWindowStyle, R.attr.textAppearanceLargePopupMenu, R.attr.textAppearanceSmallPopupMenu, R.attr.textAppearancePopupMenuHeader, R.attr.dialogTheme, R.attr.dialogPreferredPadding, R.attr.listDividerAlertDialog, R.attr.actionDropDownStyle, R.attr.dropdownListPreferredItemHeight, R.attr.spinnerDropDownItemStyle, R.attr.homeAsUpIndicator, R.attr.actionButtonStyle, R.attr.buttonBarStyle, R.attr.buttonBarButtonStyle, R.attr.selectableItemBackground, R.attr.selectableItemBackgroundBorderless, R.attr.borderlessButtonStyle, R.attr.dividerVertical, R.attr.dividerHorizontal, R.attr.activityChooserViewStyle, R.attr.toolbarStyle, R.attr.toolbarNavigationButtonStyle, R.attr.popupMenuStyle, R.attr.popupWindowStyle, R.attr.editTextColor, R.attr.editTextBackground, R.attr.imageButtonStyle, R.attr.textAppearanceSearchResultTitle, R.attr.textAppearanceSearchResultSubtitle, R.attr.textColorSearchUrl, R.attr.searchViewStyle, R.attr.listPreferredItemHeight, R.attr.listPreferredItemHeightSmall, R.attr.listPreferredItemHeightLarge, R.attr.listPreferredItemPaddingLeft, R.attr.listPreferredItemPaddingRight, R.attr.dropDownListViewStyle, R.attr.listPopupWindowStyle, R.attr.textAppearanceListItem, R.attr.textAppearanceListItemSmall, R.attr.panelBackground, R.attr.panelMenuListWidth, R.attr.panelMenuListTheme, R.attr.listChoiceBackgroundIndicator, R.attr.colorPrimary, R.attr.colorPrimaryDark, R.attr.colorAccent, R.attr.colorControlNormal, R.attr.colorControlActivated, R.attr.colorControlHighlight, R.attr.colorButtonNormal, R.attr.colorSwitchThumbNormal, R.attr.controlBackground, R.attr.colorBackgroundFloating, R.attr.alertDialogStyle, R.attr.alertDialogButtonGroupStyle, R.attr.alertDialogCenterButtons, R.attr.alertDialogTheme, R.attr.textColorAlertDialogListItem, R.attr.buttonBarPositiveButtonStyle, R.attr.buttonBarNegativeButtonStyle, R.attr.buttonBarNeutralButtonStyle, R.attr.autoCompleteTextViewStyle, R.attr.buttonStyle, R.attr.buttonStyleSmall, R.attr.checkboxStyle, R.attr.checkedTextViewStyle, R.attr.editTextStyle, R.attr.radioButtonStyle, R.attr.ratingBarStyle, R.attr.ratingBarStyleIndicator, R.attr.ratingBarStyleSmall, R.attr.seekBarStyle, R.attr.spinnerStyle, R.attr.switchStyle, R.attr.listMenuViewStyle};
        public static final int AppCompatTheme_actionBarDivider = 23;
        public static final int AppCompatTheme_actionBarItemBackground = 24;
        public static final int AppCompatTheme_actionBarPopupTheme = 17;
        public static final int AppCompatTheme_actionBarSize = 22;
        public static final int AppCompatTheme_actionBarSplitStyle = 19;
        public static final int AppCompatTheme_actionBarStyle = 18;
        public static final int AppCompatTheme_actionBarTabBarStyle = 13;
        public static final int AppCompatTheme_actionBarTabStyle = 12;
        public static final int AppCompatTheme_actionBarTabTextStyle = 14;
        public static final int AppCompatTheme_actionBarTheme = 20;
        public static final int AppCompatTheme_actionBarWidgetTheme = 21;
        public static final int AppCompatTheme_actionButtonStyle = 50;
        public static final int AppCompatTheme_actionDropDownStyle = 46;
        public static final int AppCompatTheme_actionMenuTextAppearance = 25;
        public static final int AppCompatTheme_actionMenuTextColor = 26;
        public static final int AppCompatTheme_actionModeBackground = 29;
        public static final int AppCompatTheme_actionModeCloseButtonStyle = 28;
        public static final int AppCompatTheme_actionModeCloseDrawable = 31;
        public static final int AppCompatTheme_actionModeCopyDrawable = 33;
        public static final int AppCompatTheme_actionModeCutDrawable = 32;
        public static final int AppCompatTheme_actionModeFindDrawable = 37;
        public static final int AppCompatTheme_actionModePasteDrawable = 34;
        public static final int AppCompatTheme_actionModePopupWindowStyle = 39;
        public static final int AppCompatTheme_actionModeSelectAllDrawable = 35;
        public static final int AppCompatTheme_actionModeShareDrawable = 36;
        public static final int AppCompatTheme_actionModeSplitBackground = 30;
        public static final int AppCompatTheme_actionModeStyle = 27;
        public static final int AppCompatTheme_actionModeWebSearchDrawable = 38;
        public static final int AppCompatTheme_actionOverflowButtonStyle = 15;
        public static final int AppCompatTheme_actionOverflowMenuStyle = 16;
        public static final int AppCompatTheme_activityChooserViewStyle = 58;
        public static final int AppCompatTheme_alertDialogButtonGroupStyle = 94;
        public static final int AppCompatTheme_alertDialogCenterButtons = 95;
        public static final int AppCompatTheme_alertDialogStyle = 93;
        public static final int AppCompatTheme_alertDialogTheme = 96;
        public static final int AppCompatTheme_android_windowAnimationStyle = 1;
        public static final int AppCompatTheme_android_windowIsFloating = 0;
        public static final int AppCompatTheme_autoCompleteTextViewStyle = 101;
        public static final int AppCompatTheme_borderlessButtonStyle = 55;
        public static final int AppCompatTheme_buttonBarButtonStyle = 52;
        public static final int AppCompatTheme_buttonBarNegativeButtonStyle = 99;
        public static final int AppCompatTheme_buttonBarNeutralButtonStyle = 100;
        public static final int AppCompatTheme_buttonBarPositiveButtonStyle = 98;
        public static final int AppCompatTheme_buttonBarStyle = 51;
        public static final int AppCompatTheme_buttonStyle = 102;
        public static final int AppCompatTheme_buttonStyleSmall = 103;
        public static final int AppCompatTheme_checkboxStyle = 104;
        public static final int AppCompatTheme_checkedTextViewStyle = 105;
        public static final int AppCompatTheme_colorAccent = 85;
        public static final int AppCompatTheme_colorBackgroundFloating = 92;
        public static final int AppCompatTheme_colorButtonNormal = 89;
        public static final int AppCompatTheme_colorControlActivated = 87;
        public static final int AppCompatTheme_colorControlHighlight = 88;
        public static final int AppCompatTheme_colorControlNormal = 86;
        public static final int AppCompatTheme_colorPrimary = 83;
        public static final int AppCompatTheme_colorPrimaryDark = 84;
        public static final int AppCompatTheme_colorSwitchThumbNormal = 90;
        public static final int AppCompatTheme_controlBackground = 91;
        public static final int AppCompatTheme_dialogPreferredPadding = 44;
        public static final int AppCompatTheme_dialogTheme = 43;
        public static final int AppCompatTheme_dividerHorizontal = 57;
        public static final int AppCompatTheme_dividerVertical = 56;
        public static final int AppCompatTheme_dropDownListViewStyle = 75;
        public static final int AppCompatTheme_dropdownListPreferredItemHeight = 47;
        public static final int AppCompatTheme_editTextBackground = 64;
        public static final int AppCompatTheme_editTextColor = 63;
        public static final int AppCompatTheme_editTextStyle = 106;
        public static final int AppCompatTheme_homeAsUpIndicator = 49;
        public static final int AppCompatTheme_imageButtonStyle = 65;
        public static final int AppCompatTheme_listChoiceBackgroundIndicator = 82;
        public static final int AppCompatTheme_listDividerAlertDialog = 45;
        public static final int AppCompatTheme_listMenuViewStyle = 114;
        public static final int AppCompatTheme_listPopupWindowStyle = 76;
        public static final int AppCompatTheme_listPreferredItemHeight = 70;
        public static final int AppCompatTheme_listPreferredItemHeightLarge = 72;
        public static final int AppCompatTheme_listPreferredItemHeightSmall = 71;
        public static final int AppCompatTheme_listPreferredItemPaddingLeft = 73;
        public static final int AppCompatTheme_listPreferredItemPaddingRight = 74;
        public static final int AppCompatTheme_panelBackground = 79;
        public static final int AppCompatTheme_panelMenuListTheme = 81;
        public static final int AppCompatTheme_panelMenuListWidth = 80;
        public static final int AppCompatTheme_popupMenuStyle = 61;
        public static final int AppCompatTheme_popupWindowStyle = 62;
        public static final int AppCompatTheme_radioButtonStyle = 107;
        public static final int AppCompatTheme_ratingBarStyle = 108;
        public static final int AppCompatTheme_ratingBarStyleIndicator = 109;
        public static final int AppCompatTheme_ratingBarStyleSmall = 110;
        public static final int AppCompatTheme_searchViewStyle = 69;
        public static final int AppCompatTheme_seekBarStyle = 111;
        public static final int AppCompatTheme_selectableItemBackground = 53;
        public static final int AppCompatTheme_selectableItemBackgroundBorderless = 54;
        public static final int AppCompatTheme_spinnerDropDownItemStyle = 48;
        public static final int AppCompatTheme_spinnerStyle = 112;
        public static final int AppCompatTheme_switchStyle = 113;
        public static final int AppCompatTheme_textAppearanceLargePopupMenu = 40;
        public static final int AppCompatTheme_textAppearanceListItem = 77;
        public static final int AppCompatTheme_textAppearanceListItemSmall = 78;
        public static final int AppCompatTheme_textAppearancePopupMenuHeader = 42;
        public static final int AppCompatTheme_textAppearanceSearchResultSubtitle = 67;
        public static final int AppCompatTheme_textAppearanceSearchResultTitle = 66;
        public static final int AppCompatTheme_textAppearanceSmallPopupMenu = 41;
        public static final int AppCompatTheme_textColorAlertDialogListItem = 97;
        public static final int AppCompatTheme_textColorSearchUrl = 68;
        public static final int AppCompatTheme_toolbarNavigationButtonStyle = 60;
        public static final int AppCompatTheme_toolbarStyle = 59;
        public static final int AppCompatTheme_windowActionBar = 2;
        public static final int AppCompatTheme_windowActionBarOverlay = 4;
        public static final int AppCompatTheme_windowActionModeOverlay = 5;
        public static final int AppCompatTheme_windowFixedHeightMajor = 9;
        public static final int AppCompatTheme_windowFixedHeightMinor = 7;
        public static final int AppCompatTheme_windowFixedWidthMajor = 6;
        public static final int AppCompatTheme_windowFixedWidthMinor = 8;
        public static final int AppCompatTheme_windowMinWidthMajor = 10;
        public static final int AppCompatTheme_windowMinWidthMinor = 11;
        public static final int AppCompatTheme_windowNoTitle = 3;
        public static final int[] BottomNavigationView = {R.attr.elevation, R.attr.menu, R.attr.itemIconTint, R.attr.itemTextColor, R.attr.itemBackground};
        public static final int BottomNavigationView_elevation = 0;
        public static final int BottomNavigationView_itemBackground = 4;
        public static final int BottomNavigationView_itemIconTint = 2;
        public static final int BottomNavigationView_itemTextColor = 3;
        public static final int BottomNavigationView_menu = 1;
        public static final int[] BottomSheetBehavior_Layout = {R.attr.behavior_peekHeight, R.attr.behavior_hideable, R.attr.behavior_skipCollapsed};
        public static final int BottomSheetBehavior_Layout_behavior_hideable = 1;
        public static final int BottomSheetBehavior_Layout_behavior_peekHeight = 0;
        public static final int BottomSheetBehavior_Layout_behavior_skipCollapsed = 2;
        public static final int[] ButtonBarLayout = {R.attr.allowStacking};
        public static final int ButtonBarLayout_allowStacking = 0;
        public static final int[] CollapsingToolbarLayout = {R.attr.title, R.attr.expandedTitleMargin, R.attr.expandedTitleMarginStart, R.attr.expandedTitleMarginTop, R.attr.expandedTitleMarginEnd, R.attr.expandedTitleMarginBottom, R.attr.expandedTitleTextAppearance, R.attr.collapsedTitleTextAppearance, R.attr.contentScrim, R.attr.statusBarScrim, R.attr.toolbarId, R.attr.scrimVisibleHeightTrigger, R.attr.scrimAnimationDuration, R.attr.collapsedTitleGravity, R.attr.expandedTitleGravity, R.attr.titleEnabled};
        public static final int[] CollapsingToolbarLayout_Layout = {R.attr.layout_collapseMode, R.attr.layout_collapseParallaxMultiplier};
        public static final int CollapsingToolbarLayout_Layout_layout_collapseMode = 0;
        public static final int CollapsingToolbarLayout_Layout_layout_collapseParallaxMultiplier = 1;
        public static final int CollapsingToolbarLayout_collapsedTitleGravity = 13;
        public static final int CollapsingToolbarLayout_collapsedTitleTextAppearance = 7;
        public static final int CollapsingToolbarLayout_contentScrim = 8;
        public static final int CollapsingToolbarLayout_expandedTitleGravity = 14;
        public static final int CollapsingToolbarLayout_expandedTitleMargin = 1;
        public static final int CollapsingToolbarLayout_expandedTitleMarginBottom = 5;
        public static final int CollapsingToolbarLayout_expandedTitleMarginEnd = 4;
        public static final int CollapsingToolbarLayout_expandedTitleMarginStart = 2;
        public static final int CollapsingToolbarLayout_expandedTitleMarginTop = 3;
        public static final int CollapsingToolbarLayout_expandedTitleTextAppearance = 6;
        public static final int CollapsingToolbarLayout_scrimAnimationDuration = 12;
        public static final int CollapsingToolbarLayout_scrimVisibleHeightTrigger = 11;
        public static final int CollapsingToolbarLayout_statusBarScrim = 9;
        public static final int CollapsingToolbarLayout_title = 0;
        public static final int CollapsingToolbarLayout_titleEnabled = 15;
        public static final int CollapsingToolbarLayout_toolbarId = 10;
        public static final int[] ColorStateListItem = {16843173, 16843551, R.attr.alpha};
        public static final int ColorStateListItem_alpha = 2;
        public static final int ColorStateListItem_android_alpha = 1;
        public static final int ColorStateListItem_android_color = 0;
        public static final int[] CompoundButton = {16843015, R.attr.buttonTint, R.attr.buttonTintMode};
        public static final int CompoundButton_android_button = 0;
        public static final int CompoundButton_buttonTint = 1;
        public static final int CompoundButton_buttonTintMode = 2;
        public static final int[] ConstraintLayout_Layout = {16842948, 16843039, 16843040, 16843071, 16843072, R.attr.constraintSet, R.attr.layout_constraintBaseline_creator, R.attr.layout_constraintBaseline_toBaselineOf, R.attr.layout_constraintBottom_creator, R.attr.layout_constraintBottom_toBottomOf, R.attr.layout_constraintBottom_toTopOf, R.attr.layout_constraintDimensionRatio, R.attr.layout_constraintEnd_toEndOf, R.attr.layout_constraintEnd_toStartOf, R.attr.layout_constraintGuide_begin, R.attr.layout_constraintGuide_end, R.attr.layout_constraintGuide_percent, R.attr.layout_constraintHeight_default, R.attr.layout_constraintHeight_max, R.attr.layout_constraintHeight_min, R.attr.layout_constraintHorizontal_bias, R.attr.layout_constraintHorizontal_chainStyle, R.attr.layout_constraintHorizontal_weight, R.attr.layout_constraintLeft_creator, R.attr.layout_constraintLeft_toLeftOf, R.attr.layout_constraintLeft_toRightOf, R.attr.layout_constraintRight_creator, R.attr.layout_constraintRight_toLeftOf, R.attr.layout_constraintRight_toRightOf, R.attr.layout_constraintStart_toEndOf, R.attr.layout_constraintStart_toStartOf, R.attr.layout_constraintTop_creator, R.attr.layout_constraintTop_toBottomOf, R.attr.layout_constraintTop_toTopOf, R.attr.layout_constraintVertical_bias, R.attr.layout_constraintVertical_chainStyle, R.attr.layout_constraintVertical_weight, R.attr.layout_constraintWidth_default, R.attr.layout_constraintWidth_max, R.attr.layout_constraintWidth_min, R.attr.layout_editor_absoluteX, R.attr.layout_editor_absoluteY, R.attr.layout_goneMarginBottom, R.attr.layout_goneMarginEnd, R.attr.layout_goneMarginLeft, R.attr.layout_goneMarginRight, R.attr.layout_goneMarginStart, R.attr.layout_goneMarginTop, R.attr.layout_optimizationLevel};
        public static final int ConstraintLayout_Layout_android_maxHeight = 2;
        public static final int ConstraintLayout_Layout_android_maxWidth = 1;
        public static final int ConstraintLayout_Layout_android_minHeight = 4;
        public static final int ConstraintLayout_Layout_android_minWidth = 3;
        public static final int ConstraintLayout_Layout_android_orientation = 0;
        public static final int ConstraintLayout_Layout_constraintSet = 5;
        public static final int ConstraintLayout_Layout_layout_constraintBaseline_creator = 6;
        public static final int ConstraintLayout_Layout_layout_constraintBaseline_toBaselineOf = 7;
        public static final int ConstraintLayout_Layout_layout_constraintBottom_creator = 8;
        public static final int ConstraintLayout_Layout_layout_constraintBottom_toBottomOf = 9;
        public static final int ConstraintLayout_Layout_layout_constraintBottom_toTopOf = 10;
        public static final int ConstraintLayout_Layout_layout_constraintDimensionRatio = 11;
        public static final int ConstraintLayout_Layout_layout_constraintEnd_toEndOf = 12;
        public static final int ConstraintLayout_Layout_layout_constraintEnd_toStartOf = 13;
        public static final int ConstraintLayout_Layout_layout_constraintGuide_begin = 14;
        public static final int ConstraintLayout_Layout_layout_constraintGuide_end = 15;
        public static final int ConstraintLayout_Layout_layout_constraintGuide_percent = 16;
        public static final int ConstraintLayout_Layout_layout_constraintHeight_default = 17;
        public static final int ConstraintLayout_Layout_layout_constraintHeight_max = 18;
        public static final int ConstraintLayout_Layout_layout_constraintHeight_min = 19;
        public static final int ConstraintLayout_Layout_layout_constraintHorizontal_bias = 20;
        public static final int ConstraintLayout_Layout_layout_constraintHorizontal_chainStyle = 21;
        public static final int ConstraintLayout_Layout_layout_constraintHorizontal_weight = 22;
        public static final int ConstraintLayout_Layout_layout_constraintLeft_creator = 23;
        public static final int ConstraintLayout_Layout_layout_constraintLeft_toLeftOf = 24;
        public static final int ConstraintLayout_Layout_layout_constraintLeft_toRightOf = 25;
        public static final int ConstraintLayout_Layout_layout_constraintRight_creator = 26;
        public static final int ConstraintLayout_Layout_layout_constraintRight_toLeftOf = 27;
        public static final int ConstraintLayout_Layout_layout_constraintRight_toRightOf = 28;
        public static final int ConstraintLayout_Layout_layout_constraintStart_toEndOf = 29;
        public static final int ConstraintLayout_Layout_layout_constraintStart_toStartOf = 30;
        public static final int ConstraintLayout_Layout_layout_constraintTop_creator = 31;
        public static final int ConstraintLayout_Layout_layout_constraintTop_toBottomOf = 32;
        public static final int ConstraintLayout_Layout_layout_constraintTop_toTopOf = 33;
        public static final int ConstraintLayout_Layout_layout_constraintVertical_bias = 34;
        public static final int ConstraintLayout_Layout_layout_constraintVertical_chainStyle = 35;
        public static final int ConstraintLayout_Layout_layout_constraintVertical_weight = 36;
        public static final int ConstraintLayout_Layout_layout_constraintWidth_default = 37;
        public static final int ConstraintLayout_Layout_layout_constraintWidth_max = 38;
        public static final int ConstraintLayout_Layout_layout_constraintWidth_min = 39;
        public static final int ConstraintLayout_Layout_layout_editor_absoluteX = 40;
        public static final int ConstraintLayout_Layout_layout_editor_absoluteY = 41;
        public static final int ConstraintLayout_Layout_layout_goneMarginBottom = 42;
        public static final int ConstraintLayout_Layout_layout_goneMarginEnd = 43;
        public static final int ConstraintLayout_Layout_layout_goneMarginLeft = 44;
        public static final int ConstraintLayout_Layout_layout_goneMarginRight = 45;
        public static final int ConstraintLayout_Layout_layout_goneMarginStart = 46;
        public static final int ConstraintLayout_Layout_layout_goneMarginTop = 47;
        public static final int ConstraintLayout_Layout_layout_optimizationLevel = 48;
        public static final int[] ConstraintSet = {16842948, 16842960, 16842972, 16842996, 16842997, 16842999, 16843000, 16843001, 16843002, 16843551, 16843552, 16843553, 16843554, 16843555, 16843556, 16843557, 16843559, 16843560, 16843701, 16843702, 16843770, 16843840, R.attr.layout_constraintBaseline_creator, R.attr.layout_constraintBaseline_toBaselineOf, R.attr.layout_constraintBottom_creator, R.attr.layout_constraintBottom_toBottomOf, R.attr.layout_constraintBottom_toTopOf, R.attr.layout_constraintDimensionRatio, R.attr.layout_constraintEnd_toEndOf, R.attr.layout_constraintEnd_toStartOf, R.attr.layout_constraintGuide_begin, R.attr.layout_constraintGuide_end, R.attr.layout_constraintGuide_percent, R.attr.layout_constraintHeight_default, R.attr.layout_constraintHeight_max, R.attr.layout_constraintHeight_min, R.attr.layout_constraintHorizontal_bias, R.attr.layout_constraintHorizontal_chainStyle, R.attr.layout_constraintHorizontal_weight, R.attr.layout_constraintLeft_creator, R.attr.layout_constraintLeft_toLeftOf, R.attr.layout_constraintLeft_toRightOf, R.attr.layout_constraintRight_creator, R.attr.layout_constraintRight_toLeftOf, R.attr.layout_constraintRight_toRightOf, R.attr.layout_constraintStart_toEndOf, R.attr.layout_constraintStart_toStartOf, R.attr.layout_constraintTop_creator, R.attr.layout_constraintTop_toBottomOf, R.attr.layout_constraintTop_toTopOf, R.attr.layout_constraintVertical_bias, R.attr.layout_constraintVertical_chainStyle, R.attr.layout_constraintVertical_weight, R.attr.layout_constraintWidth_default, R.attr.layout_constraintWidth_max, R.attr.layout_constraintWidth_min, R.attr.layout_editor_absoluteX, R.attr.layout_editor_absoluteY, R.attr.layout_goneMarginBottom, R.attr.layout_goneMarginEnd, R.attr.layout_goneMarginLeft, R.attr.layout_goneMarginRight, R.attr.layout_goneMarginStart, R.attr.layout_goneMarginTop};
        public static final int ConstraintSet_android_alpha = 9;
        public static final int ConstraintSet_android_elevation = 21;
        public static final int ConstraintSet_android_id = 1;
        public static final int ConstraintSet_android_layout_height = 4;
        public static final int ConstraintSet_android_layout_marginBottom = 8;
        public static final int ConstraintSet_android_layout_marginEnd = 19;
        public static final int ConstraintSet_android_layout_marginLeft = 5;
        public static final int ConstraintSet_android_layout_marginRight = 7;
        public static final int ConstraintSet_android_layout_marginStart = 18;
        public static final int ConstraintSet_android_layout_marginTop = 6;
        public static final int ConstraintSet_android_layout_width = 3;
        public static final int ConstraintSet_android_orientation = 0;
        public static final int ConstraintSet_android_rotationX = 16;
        public static final int ConstraintSet_android_rotationY = 17;
        public static final int ConstraintSet_android_scaleX = 14;
        public static final int ConstraintSet_android_scaleY = 15;
        public static final int ConstraintSet_android_transformPivotX = 10;
        public static final int ConstraintSet_android_transformPivotY = 11;
        public static final int ConstraintSet_android_translationX = 12;
        public static final int ConstraintSet_android_translationY = 13;
        public static final int ConstraintSet_android_translationZ = 20;
        public static final int ConstraintSet_android_visibility = 2;
        public static final int ConstraintSet_layout_constraintBaseline_creator = 22;
        public static final int ConstraintSet_layout_constraintBaseline_toBaselineOf = 23;
        public static final int ConstraintSet_layout_constraintBottom_creator = 24;
        public static final int ConstraintSet_layout_constraintBottom_toBottomOf = 25;
        public static final int ConstraintSet_layout_constraintBottom_toTopOf = 26;
        public static final int ConstraintSet_layout_constraintDimensionRatio = 27;
        public static final int ConstraintSet_layout_constraintEnd_toEndOf = 28;
        public static final int ConstraintSet_layout_constraintEnd_toStartOf = 29;
        public static final int ConstraintSet_layout_constraintGuide_begin = 30;
        public static final int ConstraintSet_layout_constraintGuide_end = 31;
        public static final int ConstraintSet_layout_constraintGuide_percent = 32;
        public static final int ConstraintSet_layout_constraintHeight_default = 33;
        public static final int ConstraintSet_layout_constraintHeight_max = 34;
        public static final int ConstraintSet_layout_constraintHeight_min = 35;
        public static final int ConstraintSet_layout_constraintHorizontal_bias = 36;
        public static final int ConstraintSet_layout_constraintHorizontal_chainStyle = 37;
        public static final int ConstraintSet_layout_constraintHorizontal_weight = 38;
        public static final int ConstraintSet_layout_constraintLeft_creator = 39;
        public static final int ConstraintSet_layout_constraintLeft_toLeftOf = 40;
        public static final int ConstraintSet_layout_constraintLeft_toRightOf = 41;
        public static final int ConstraintSet_layout_constraintRight_creator = 42;
        public static final int ConstraintSet_layout_constraintRight_toLeftOf = 43;
        public static final int ConstraintSet_layout_constraintRight_toRightOf = 44;
        public static final int ConstraintSet_layout_constraintStart_toEndOf = 45;
        public static final int ConstraintSet_layout_constraintStart_toStartOf = 46;
        public static final int ConstraintSet_layout_constraintTop_creator = 47;
        public static final int ConstraintSet_layout_constraintTop_toBottomOf = 48;
        public static final int ConstraintSet_layout_constraintTop_toTopOf = 49;
        public static final int ConstraintSet_layout_constraintVertical_bias = 50;
        public static final int ConstraintSet_layout_constraintVertical_chainStyle = 51;
        public static final int ConstraintSet_layout_constraintVertical_weight = 52;
        public static final int ConstraintSet_layout_constraintWidth_default = 53;
        public static final int ConstraintSet_layout_constraintWidth_max = 54;
        public static final int ConstraintSet_layout_constraintWidth_min = 55;
        public static final int ConstraintSet_layout_editor_absoluteX = 56;
        public static final int ConstraintSet_layout_editor_absoluteY = 57;
        public static final int ConstraintSet_layout_goneMarginBottom = 58;
        public static final int ConstraintSet_layout_goneMarginEnd = 59;
        public static final int ConstraintSet_layout_goneMarginLeft = 60;
        public static final int ConstraintSet_layout_goneMarginRight = 61;
        public static final int ConstraintSet_layout_goneMarginStart = 62;
        public static final int ConstraintSet_layout_goneMarginTop = 63;
        public static final int[] CoordinatorLayout = {R.attr.keylines, R.attr.statusBarBackground};
        public static final int[] CoordinatorLayout_Layout = {16842931, R.attr.layout_behavior, R.attr.layout_anchor, R.attr.layout_keyline, R.attr.layout_anchorGravity, R.attr.layout_insetEdge, R.attr.layout_dodgeInsetEdges};
        public static final int CoordinatorLayout_Layout_android_layout_gravity = 0;
        public static final int CoordinatorLayout_Layout_layout_anchor = 2;
        public static final int CoordinatorLayout_Layout_layout_anchorGravity = 4;
        public static final int CoordinatorLayout_Layout_layout_behavior = 1;
        public static final int CoordinatorLayout_Layout_layout_dodgeInsetEdges = 6;
        public static final int CoordinatorLayout_Layout_layout_insetEdge = 5;
        public static final int CoordinatorLayout_Layout_layout_keyline = 3;
        public static final int CoordinatorLayout_keylines = 0;
        public static final int CoordinatorLayout_statusBarBackground = 1;
        public static final int[] DesignTheme = {R.attr.bottomSheetDialogTheme, R.attr.bottomSheetStyle, R.attr.textColorError};
        public static final int DesignTheme_bottomSheetDialogTheme = 0;
        public static final int DesignTheme_bottomSheetStyle = 1;
        public static final int DesignTheme_textColorError = 2;
        public static final int[] DrawerArrowToggle = {R.attr.color, R.attr.spinBars, R.attr.drawableSize, R.attr.gapBetweenBars, R.attr.arrowHeadLength, R.attr.arrowShaftLength, R.attr.barLength, R.attr.thickness};
        public static final int DrawerArrowToggle_arrowHeadLength = 4;
        public static final int DrawerArrowToggle_arrowShaftLength = 5;
        public static final int DrawerArrowToggle_barLength = 6;
        public static final int DrawerArrowToggle_color = 0;
        public static final int DrawerArrowToggle_drawableSize = 2;
        public static final int DrawerArrowToggle_gapBetweenBars = 3;
        public static final int DrawerArrowToggle_spinBars = 1;
        public static final int DrawerArrowToggle_thickness = 7;
        public static final int[] FloatingActionButton = {R.attr.elevation, R.attr.rippleColor, R.attr.fabSize, R.attr.pressedTranslationZ, R.attr.borderWidth, R.attr.useCompatPadding, R.attr.backgroundTint, R.attr.backgroundTintMode};
        public static final int[] FloatingActionButton_Behavior_Layout = {R.attr.behavior_autoHide};
        public static final int FloatingActionButton_Behavior_Layout_behavior_autoHide = 0;
        public static final int FloatingActionButton_backgroundTint = 6;
        public static final int FloatingActionButton_backgroundTintMode = 7;
        public static final int FloatingActionButton_borderWidth = 4;
        public static final int FloatingActionButton_elevation = 0;
        public static final int FloatingActionButton_fabSize = 2;
        public static final int FloatingActionButton_pressedTranslationZ = 3;
        public static final int FloatingActionButton_rippleColor = 1;
        public static final int FloatingActionButton_useCompatPadding = 5;
        public static final int[] ForegroundLinearLayout = {16843017, 16843264, R.attr.foregroundInsidePadding};
        public static final int ForegroundLinearLayout_android_foreground = 0;
        public static final int ForegroundLinearLayout_android_foregroundGravity = 1;
        public static final int ForegroundLinearLayout_foregroundInsidePadding = 2;
        public static final int[] LinearConstraintLayout = {16842948};
        public static final int LinearConstraintLayout_android_orientation = 0;
        public static final int[] LinearLayoutCompat = {16842927, 16842948, 16843046, 16843047, 16843048, R.attr.divider, R.attr.measureWithLargestChild, R.attr.showDividers, R.attr.dividerPadding};
        public static final int[] LinearLayoutCompat_Layout = {16842931, 16842996, 16842997, 16843137};
        public static final int LinearLayoutCompat_Layout_android_layout_gravity = 0;
        public static final int LinearLayoutCompat_Layout_android_layout_height = 2;
        public static final int LinearLayoutCompat_Layout_android_layout_weight = 3;
        public static final int LinearLayoutCompat_Layout_android_layout_width = 1;
        public static final int LinearLayoutCompat_android_baselineAligned = 2;
        public static final int LinearLayoutCompat_android_baselineAlignedChildIndex = 3;
        public static final int LinearLayoutCompat_android_gravity = 0;
        public static final int LinearLayoutCompat_android_orientation = 1;
        public static final int LinearLayoutCompat_android_weightSum = 4;
        public static final int LinearLayoutCompat_divider = 5;
        public static final int LinearLayoutCompat_dividerPadding = 8;
        public static final int LinearLayoutCompat_measureWithLargestChild = 6;
        public static final int LinearLayoutCompat_showDividers = 7;
        public static final int[] ListPopupWindow = {16843436, 16843437};
        public static final int ListPopupWindow_android_dropDownHorizontalOffset = 0;
        public static final int ListPopupWindow_android_dropDownVerticalOffset = 1;
        public static final int[] MenuGroup = {16842766, 16842960, 16843156, 16843230, 16843231, 16843232};
        public static final int MenuGroup_android_checkableBehavior = 5;
        public static final int MenuGroup_android_enabled = 0;
        public static final int MenuGroup_android_id = 1;
        public static final int MenuGroup_android_menuCategory = 3;
        public static final int MenuGroup_android_orderInCategory = 4;
        public static final int MenuGroup_android_visible = 2;
        public static final int[] MenuItem = {16842754, 16842766, 16842960, 16843014, 16843156, 16843230, 16843231, 16843233, 16843234, 16843235, 16843236, 16843237, 16843375, R.attr.showAsAction, R.attr.actionLayout, R.attr.actionViewClass, R.attr.actionProviderClass};
        public static final int MenuItem_actionLayout = 14;
        public static final int MenuItem_actionProviderClass = 16;
        public static final int MenuItem_actionViewClass = 15;
        public static final int MenuItem_android_alphabeticShortcut = 9;
        public static final int MenuItem_android_checkable = 11;
        public static final int MenuItem_android_checked = 3;
        public static final int MenuItem_android_enabled = 1;
        public static final int MenuItem_android_icon = 0;
        public static final int MenuItem_android_id = 2;
        public static final int MenuItem_android_menuCategory = 5;
        public static final int MenuItem_android_numericShortcut = 10;
        public static final int MenuItem_android_onClick = 12;
        public static final int MenuItem_android_orderInCategory = 6;
        public static final int MenuItem_android_title = 7;
        public static final int MenuItem_android_titleCondensed = 8;
        public static final int MenuItem_android_visible = 4;
        public static final int MenuItem_showAsAction = 13;
        public static final int[] MenuView = {16842926, 16843052, 16843053, 16843054, 16843055, 16843056, 16843057, R.attr.preserveIconSpacing, R.attr.subMenuArrow};
        public static final int MenuView_android_headerBackground = 4;
        public static final int MenuView_android_horizontalDivider = 2;
        public static final int MenuView_android_itemBackground = 5;
        public static final int MenuView_android_itemIconDisabledAlpha = 6;
        public static final int MenuView_android_itemTextAppearance = 1;
        public static final int MenuView_android_verticalDivider = 3;
        public static final int MenuView_android_windowAnimationStyle = 0;
        public static final int MenuView_preserveIconSpacing = 7;
        public static final int MenuView_subMenuArrow = 8;
        public static final int[] NavigationView = {16842964, 16842973, 16843039, R.attr.elevation, R.attr.menu, R.attr.itemIconTint, R.attr.itemTextColor, R.attr.itemBackground, R.attr.itemTextAppearance, R.attr.headerLayout};
        public static final int NavigationView_android_background = 0;
        public static final int NavigationView_android_fitsSystemWindows = 1;
        public static final int NavigationView_android_maxWidth = 2;
        public static final int NavigationView_elevation = 3;
        public static final int NavigationView_headerLayout = 9;
        public static final int NavigationView_itemBackground = 7;
        public static final int NavigationView_itemIconTint = 5;
        public static final int NavigationView_itemTextAppearance = 8;
        public static final int NavigationView_itemTextColor = 6;
        public static final int NavigationView_menu = 4;
        public static final int[] PopupWindow = {16843126, 16843465, R.attr.overlapAnchor};
        public static final int[] PopupWindowBackgroundState = {R.attr.state_above_anchor};
        public static final int PopupWindowBackgroundState_state_above_anchor = 0;
        public static final int PopupWindow_android_popupAnimationStyle = 1;
        public static final int PopupWindow_android_popupBackground = 0;
        public static final int PopupWindow_overlapAnchor = 2;
        public static final int[] RecycleListView = {R.attr.paddingBottomNoButtons, R.attr.paddingTopNoTitle};
        public static final int RecycleListView_paddingBottomNoButtons = 0;
        public static final int RecycleListView_paddingTopNoTitle = 1;
        public static final int[] RecyclerView = {16842948, 16842993, R.attr.layoutManager, R.attr.spanCount, R.attr.reverseLayout, R.attr.stackFromEnd};
        public static final int RecyclerView_android_descendantFocusability = 1;
        public static final int RecyclerView_android_orientation = 0;
        public static final int RecyclerView_layoutManager = 2;
        public static final int RecyclerView_reverseLayout = 4;
        public static final int RecyclerView_spanCount = 3;
        public static final int RecyclerView_stackFromEnd = 5;
        public static final int[] ScrimInsetsFrameLayout = {R.attr.insetForeground};
        public static final int ScrimInsetsFrameLayout_insetForeground = 0;
        public static final int[] ScrollingViewBehavior_Layout = {R.attr.behavior_overlapTop};
        public static final int ScrollingViewBehavior_Layout_behavior_overlapTop = 0;
        public static final int[] SearchView = {16842970, 16843039, 16843296, 16843364, R.attr.layout, R.attr.iconifiedByDefault, R.attr.queryHint, R.attr.defaultQueryHint, R.attr.closeIcon, R.attr.goIcon, R.attr.searchIcon, R.attr.searchHintIcon, R.attr.voiceIcon, R.attr.commitIcon, R.attr.suggestionRowLayout, R.attr.queryBackground, R.attr.submitBackground};
        public static final int SearchView_android_focusable = 0;
        public static final int SearchView_android_imeOptions = 3;
        public static final int SearchView_android_inputType = 2;
        public static final int SearchView_android_maxWidth = 1;
        public static final int SearchView_closeIcon = 8;
        public static final int SearchView_commitIcon = 13;
        public static final int SearchView_defaultQueryHint = 7;
        public static final int SearchView_goIcon = 9;
        public static final int SearchView_iconifiedByDefault = 5;
        public static final int SearchView_layout = 4;
        public static final int SearchView_queryBackground = 15;
        public static final int SearchView_queryHint = 6;
        public static final int SearchView_searchHintIcon = 11;
        public static final int SearchView_searchIcon = 10;
        public static final int SearchView_submitBackground = 16;
        public static final int SearchView_suggestionRowLayout = 14;
        public static final int SearchView_voiceIcon = 12;
        public static final int[] SnackbarLayout = {16843039, R.attr.elevation, R.attr.maxActionInlineWidth};
        public static final int SnackbarLayout_android_maxWidth = 0;
        public static final int SnackbarLayout_elevation = 1;
        public static final int SnackbarLayout_maxActionInlineWidth = 2;
        public static final int[] Spinner = {16842930, 16843126, 16843131, 16843362, R.attr.popupTheme};
        public static final int Spinner_android_dropDownWidth = 3;
        public static final int Spinner_android_entries = 0;
        public static final int Spinner_android_popupBackground = 1;
        public static final int Spinner_android_prompt = 2;
        public static final int Spinner_popupTheme = 4;
        public static final int[] SwitchCompat = {16843044, 16843045, 16843074, R.attr.thumbTint, R.attr.thumbTintMode, R.attr.track, R.attr.trackTint, R.attr.trackTintMode, R.attr.thumbTextPadding, R.attr.switchTextAppearance, R.attr.switchMinWidth, R.attr.switchPadding, R.attr.splitTrack, R.attr.showText};
        public static final int SwitchCompat_android_textOff = 1;
        public static final int SwitchCompat_android_textOn = 0;
        public static final int SwitchCompat_android_thumb = 2;
        public static final int SwitchCompat_showText = 13;
        public static final int SwitchCompat_splitTrack = 12;
        public static final int SwitchCompat_switchMinWidth = 10;
        public static final int SwitchCompat_switchPadding = 11;
        public static final int SwitchCompat_switchTextAppearance = 9;
        public static final int SwitchCompat_thumbTextPadding = 8;
        public static final int SwitchCompat_thumbTint = 3;
        public static final int SwitchCompat_thumbTintMode = 4;
        public static final int SwitchCompat_track = 5;
        public static final int SwitchCompat_trackTint = 6;
        public static final int SwitchCompat_trackTintMode = 7;
        public static final int[] TabItem = {16842754, 16842994, 16843087};
        public static final int TabItem_android_icon = 0;
        public static final int TabItem_android_layout = 1;
        public static final int TabItem_android_text = 2;
        public static final int[] TabLayout = {R.attr.tabIndicatorColor, R.attr.tabIndicatorHeight, R.attr.tabContentStart, R.attr.tabBackground, R.attr.tabMode, R.attr.tabGravity, R.attr.tabMinWidth, R.attr.tabMaxWidth, R.attr.tabTextAppearance, R.attr.tabTextColor, R.attr.tabSelectedTextColor, R.attr.tabPaddingStart, R.attr.tabPaddingTop, R.attr.tabPaddingEnd, R.attr.tabPaddingBottom, R.attr.tabPadding};
        public static final int TabLayout_tabBackground = 3;
        public static final int TabLayout_tabContentStart = 2;
        public static final int TabLayout_tabGravity = 5;
        public static final int TabLayout_tabIndicatorColor = 0;
        public static final int TabLayout_tabIndicatorHeight = 1;
        public static final int TabLayout_tabMaxWidth = 7;
        public static final int TabLayout_tabMinWidth = 6;
        public static final int TabLayout_tabMode = 4;
        public static final int TabLayout_tabPadding = 15;
        public static final int TabLayout_tabPaddingBottom = 14;
        public static final int TabLayout_tabPaddingEnd = 13;
        public static final int TabLayout_tabPaddingStart = 11;
        public static final int TabLayout_tabPaddingTop = 12;
        public static final int TabLayout_tabSelectedTextColor = 10;
        public static final int TabLayout_tabTextAppearance = 8;
        public static final int TabLayout_tabTextColor = 9;
        public static final int[] TextAppearance = {16842901, 16842902, 16842903, 16842904, 16842906, 16843105, 16843106, 16843107, 16843108, R.attr.textAllCaps};
        public static final int TextAppearance_android_shadowColor = 5;
        public static final int TextAppearance_android_shadowDx = 6;
        public static final int TextAppearance_android_shadowDy = 7;
        public static final int TextAppearance_android_shadowRadius = 8;
        public static final int TextAppearance_android_textColor = 3;
        public static final int TextAppearance_android_textColorHint = 4;
        public static final int TextAppearance_android_textSize = 0;
        public static final int TextAppearance_android_textStyle = 2;
        public static final int TextAppearance_android_typeface = 1;
        public static final int TextAppearance_textAllCaps = 9;
        public static final int[] TextInputLayout = {16842906, 16843088, R.attr.hintTextAppearance, R.attr.hintEnabled, R.attr.errorEnabled, R.attr.errorTextAppearance, R.attr.counterEnabled, R.attr.counterMaxLength, R.attr.counterTextAppearance, R.attr.counterOverflowTextAppearance, R.attr.hintAnimationEnabled, R.attr.passwordToggleEnabled, R.attr.passwordToggleDrawable, R.attr.passwordToggleContentDescription, R.attr.passwordToggleTint, R.attr.passwordToggleTintMode};
        public static final int TextInputLayout_android_hint = 1;
        public static final int TextInputLayout_android_textColorHint = 0;
        public static final int TextInputLayout_counterEnabled = 6;
        public static final int TextInputLayout_counterMaxLength = 7;
        public static final int TextInputLayout_counterOverflowTextAppearance = 9;
        public static final int TextInputLayout_counterTextAppearance = 8;
        public static final int TextInputLayout_errorEnabled = 4;
        public static final int TextInputLayout_errorTextAppearance = 5;
        public static final int TextInputLayout_hintAnimationEnabled = 10;
        public static final int TextInputLayout_hintEnabled = 3;
        public static final int TextInputLayout_hintTextAppearance = 2;
        public static final int TextInputLayout_passwordToggleContentDescription = 13;
        public static final int TextInputLayout_passwordToggleDrawable = 12;
        public static final int TextInputLayout_passwordToggleEnabled = 11;
        public static final int TextInputLayout_passwordToggleTint = 14;
        public static final int TextInputLayout_passwordToggleTintMode = 15;
        public static final int[] Toolbar = {16842927, 16843072, R.attr.title, R.attr.subtitle, R.attr.logo, R.attr.contentInsetStart, R.attr.contentInsetEnd, R.attr.contentInsetLeft, R.attr.contentInsetRight, R.attr.contentInsetStartWithNavigation, R.attr.contentInsetEndWithActions, R.attr.popupTheme, R.attr.titleTextAppearance, R.attr.subtitleTextAppearance, R.attr.titleMargin, R.attr.titleMarginStart, R.attr.titleMarginEnd, R.attr.titleMarginTop, R.attr.titleMarginBottom, R.attr.titleMargins, R.attr.maxButtonHeight, R.attr.buttonGravity, R.attr.collapseIcon, R.attr.collapseContentDescription, R.attr.navigationIcon, R.attr.navigationContentDescription, R.attr.logoDescription, R.attr.titleTextColor, R.attr.subtitleTextColor};
        public static final int Toolbar_android_gravity = 0;
        public static final int Toolbar_android_minHeight = 1;
        public static final int Toolbar_buttonGravity = 21;
        public static final int Toolbar_collapseContentDescription = 23;
        public static final int Toolbar_collapseIcon = 22;
        public static final int Toolbar_contentInsetEnd = 6;
        public static final int Toolbar_contentInsetEndWithActions = 10;
        public static final int Toolbar_contentInsetLeft = 7;
        public static final int Toolbar_contentInsetRight = 8;
        public static final int Toolbar_contentInsetStart = 5;
        public static final int Toolbar_contentInsetStartWithNavigation = 9;
        public static final int Toolbar_logo = 4;
        public static final int Toolbar_logoDescription = 26;
        public static final int Toolbar_maxButtonHeight = 20;
        public static final int Toolbar_navigationContentDescription = 25;
        public static final int Toolbar_navigationIcon = 24;
        public static final int Toolbar_popupTheme = 11;
        public static final int Toolbar_subtitle = 3;
        public static final int Toolbar_subtitleTextAppearance = 13;
        public static final int Toolbar_subtitleTextColor = 28;
        public static final int Toolbar_title = 2;
        public static final int Toolbar_titleMargin = 14;
        public static final int Toolbar_titleMarginBottom = 18;
        public static final int Toolbar_titleMarginEnd = 16;
        public static final int Toolbar_titleMarginStart = 15;
        public static final int Toolbar_titleMarginTop = 17;
        public static final int Toolbar_titleMargins = 19;
        public static final int Toolbar_titleTextAppearance = 12;
        public static final int Toolbar_titleTextColor = 27;
        public static final int[] View = {16842752, 16842970, R.attr.paddingStart, R.attr.paddingEnd, R.attr.theme};
        public static final int[] ViewBackgroundHelper = {16842964, R.attr.backgroundTint, R.attr.backgroundTintMode};
        public static final int ViewBackgroundHelper_android_background = 0;
        public static final int ViewBackgroundHelper_backgroundTint = 1;
        public static final int ViewBackgroundHelper_backgroundTintMode = 2;
        public static final int[] ViewStubCompat = {16842960, 16842994, 16842995};
        public static final int ViewStubCompat_android_id = 0;
        public static final int ViewStubCompat_android_inflatedId = 2;
        public static final int ViewStubCompat_android_layout = 1;
        public static final int View_android_focusable = 1;
        public static final int View_android_theme = 0;
        public static final int View_paddingEnd = 3;
        public static final int View_paddingStart = 2;
        public static final int View_theme = 4;
    }
}
