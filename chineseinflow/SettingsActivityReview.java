package com.gamestolearnenglish.chineseinflow;

import android.content.Intent;
import android.graphics.Typeface;
import android.os.Bundle;
import android.support.v7.app.AppCompatActivity;
import android.view.View;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.LinearLayout;
import android.widget.TextView;

public class SettingsActivityReview extends AppCompatActivity {
    private View.OnClickListener backListener = new View.OnClickListener() {
        public void onClick(View arg0) {
            SettingsActivityReview.this.finish();
        }
    };
    private boolean delayTextBool;
    private CheckBox delayTextBox;
    /* access modifiers changed from: private */
    public Intent myData = new Intent();
    private View.OnClickListener okListener = new View.OnClickListener() {
        public void onClick(View arg0) {
            SettingsActivityReview.this.updateBooleans();
            SettingsActivityReview.this.myData.putExtra("altBool", true);
            SettingsActivityReview.this.myData.putExtra("playAudBool", SettingsActivityReview.this.playAudBool);
            SettingsActivityReview.this.myData.putExtra("showPinyinBool", SettingsActivityReview.this.showPinyinBool);
            SettingsActivityReview.this.myData.putExtra("showEnglishBool", SettingsActivityReview.this.showEnglishBool);
            SettingsActivityReview.this.setResult(0, SettingsActivityReview.this.myData);
            SettingsActivityReview.this.finish();
        }
    };
    /* access modifiers changed from: private */
    public boolean playAudBool;
    private CheckBox playAudBox;
    /* access modifiers changed from: private */
    public boolean showEnglishBool;
    private CheckBox showEnglishBox;
    /* access modifiers changed from: private */
    public boolean showPinyinBool;
    private CheckBox showPinyinBox;

    /* access modifiers changed from: protected */
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView((int) R.layout.activity_settings_review);
        setVolumeControlStream(3);
        Intent gotInt = getIntent();
        this.playAudBool = gotInt.getBooleanExtra("playAudBool", false);
        this.showPinyinBool = gotInt.getBooleanExtra("showPinyinBool", false);
        this.showEnglishBool = gotInt.getBooleanExtra("showEnglishBool", false);
        this.playAudBox = (CheckBox) findViewById(R.id.playAudBoxCheck);
        this.showPinyinBox = (CheckBox) findViewById(R.id.showPinyinBoxCheck);
        this.showEnglishBox = (CheckBox) findViewById(R.id.showEnglishBoxCheck);
        setBoxesDefault();
        ((LinearLayout) findViewById(R.id.frontPart)).setOnClickListener((View.OnClickListener) null);
        ((Button) findViewById(R.id.settingsOkBut)).setOnClickListener(this.okListener);
        TextView[] tViews = new TextView[7];
        for (int i = 0; i < 4; i++) {
            tViews[i] = (TextView) findViewById(getResources().getIdentifier("practiceSettingsText" + i, "id", getPackageName()));
            tViews[i].setTypeface(Typeface.createFromAsset(getAssets(), "AmaBold.ttf"));
        }
        this.myData.putExtra("altBool", false);
        setResult(0, this.myData);
    }

    public void checkClick(View v) {
    }

    private void setBoxesDefault() {
        if (this.playAudBool) {
            this.playAudBox.setChecked(true);
        }
        if (this.showPinyinBool) {
            this.showPinyinBox.setChecked(true);
        }
        if (this.showEnglishBool) {
            this.showEnglishBox.setChecked(true);
        }
    }

    /* access modifiers changed from: private */
    public void updateBooleans() {
        boolean z;
        boolean z2;
        boolean z3 = true;
        if (this.playAudBox.isChecked()) {
            z = true;
        } else {
            z = false;
        }
        this.playAudBool = z;
        if (this.showPinyinBox.isChecked()) {
            z2 = true;
        } else {
            z2 = false;
        }
        this.showPinyinBool = z2;
        if (!this.showEnglishBox.isChecked()) {
            z3 = false;
        }
        this.showEnglishBool = z3;
    }
}
