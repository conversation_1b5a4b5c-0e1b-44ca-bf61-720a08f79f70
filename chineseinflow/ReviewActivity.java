package com.gamestolearnenglish.chineseinflow;

import android.content.Intent;
import android.content.res.AssetFileDescriptor;
import android.graphics.Typeface;
import android.media.MediaPlayer;
import android.os.Bundle;
import android.os.Handler;
import android.support.v7.app.AppCompatActivity;
import android.view.View;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.TextView;
import database.DatabaseWrapper;
import java.util.ArrayList;
import java.util.Collections;

public class ReviewActivity extends AppCompatActivity {
    private Button butAgain;
    private Button butGood;
    /* access modifiers changed from: private */
    public int countInt;
    private int grpBase;
    private int[] itemOrderArr = new int[30];
    private ArrayList<Item> myItems;
    private MediaPlayer myMp = new MediaPlayer();
    private DatabaseWrapper myWrap;
    private boolean playAudBool = false;
    private int repeatsCount;
    private TextView revealedLine0;
    private TextView revealedLine1;
    private Runnable round = new Runnable() {
        public void run() {
            ReviewActivity.access$008(ReviewActivity.this);
            if (ReviewActivity.this.countInt < 30) {
                ReviewActivity.this.newRound();
            } else {
                ReviewActivity.this.endGame();
            }
        }
    };
    private Handler roundHandler;
    private int roundInt;
    private TextView roundText;
    private boolean showBool;
    private boolean showEnglishBool = true;
    private boolean showPinyinBool = true;
    private long startTime;
    private int tarBase;
    private TextView topText;
    private int viewsCount;

    static /* synthetic */ int access$008(ReviewActivity x0) {
        int i = x0.countInt;
        x0.countInt = i + 1;
        return i;
    }

    /* access modifiers changed from: protected */
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView((int) R.layout.activity_review);
        setVolumeControlStream(3);
        initValues();
        ImageView sBut = (ImageView) findViewById(R.id.audBut);
        if (this.grpBase <= 3) {
            sBut.setVisibility(0);
        } else {
            sBut.setVisibility(4);
        }
        this.roundHandler.post(this.round);
    }

    /* access modifiers changed from: protected */
    public void onResume() {
        super.onResume();
        this.startTime = System.currentTimeMillis();
    }

    private void initValues() {
        Intent myInt = getIntent();
        this.tarBase = myInt.getIntExtra("contentSelected", 0);
        this.grpBase = myInt.getIntExtra("groupSelected", 0);
        this.myWrap = new DatabaseWrapper(this, this.grpBase, this.tarBase);
        this.myItems = this.myWrap.getItems();
        this.butGood = (Button) findViewById(R.id.activityReviewButGood);
        this.butAgain = (Button) findViewById(R.id.activityReviewButAgain);
        this.revealedLine0 = (TextView) findViewById(R.id.activityReviewRevealedLine0);
        this.revealedLine1 = (TextView) findViewById(R.id.activityReviewRevealedLine1);
        this.topText = (TextView) findViewById(R.id.activityReviewTopText);
        this.roundText = (TextView) findViewById(R.id.activityReviewRoundText);
        this.butGood.setTypeface(Typeface.createFromAsset(getAssets(), "AmaBold.ttf"));
        this.butAgain.setTypeface(Typeface.createFromAsset(getAssets(), "AmaBold.ttf"));
        this.roundText.setTypeface(Typeface.createFromAsset(getAssets(), "AmaBold.ttf"));
        this.roundHandler = new Handler();
        this.countInt = -1;
        this.repeatsCount = 0;
        this.itemOrderArr = ranSeq();
        this.viewsCount = 0;
    }

    /* access modifiers changed from: private */
    public void newRound() {
        this.roundInt = this.itemOrderArr[this.countInt];
        String hStr = this.myItems.get(this.roundInt).getHanzi();
        String pStr = this.myItems.get(this.roundInt).getPinzi();
        String eStr = this.myItems.get(this.roundInt).getEngzi();
        this.topText.setText(hStr);
        this.revealedLine0.setText(pStr);
        this.revealedLine1.setText(eStr);
        hideAnswer();
        this.roundText.setText((this.countInt + 1) + "/30");
        if (this.playAudBool) {
            audClick((View) null);
        }
    }

    /* access modifiers changed from: private */
    public void endGame() {
        this.myWrap.incReviewProgress();
        long passedTime = (System.currentTimeMillis() - this.startTime) / 1000;
        Intent myInt = new Intent(getApplicationContext(), EndActivity.class);
        myInt.putExtra("contentSelected", this.tarBase);
        myInt.putExtra("groupSelected", this.grpBase);
        myInt.putExtra("duration", passedTime);
        myInt.putExtra("repeats", this.repeatsCount);
        myInt.putExtra("activity", 2);
        myInt.setFlags(65536);
        startActivity(myInt);
        overridePendingTransition(0, 0);
        finish();
    }

    private void hideAnswer() {
        this.showBool = false;
        this.butGood.setText("Show");
        this.butAgain.setVisibility(4);
        this.revealedLine0.setVisibility(4);
        this.revealedLine1.setVisibility(4);
    }

    private void showAnswer() {
        this.viewsCount++;
        this.showBool = true;
        this.butGood.setText("Good");
        this.butAgain.setVisibility(0);
        this.revealedLine0.setVisibility(0);
        this.revealedLine1.setVisibility(0);
    }

    public void againClick(View v) {
        proder();
        this.repeatsCount++;
        this.roundHandler.post(this.round);
    }

    public void goodClick(View v) {
        if (this.showBool) {
            this.roundHandler.post(this.round);
        } else {
            showAnswer();
        }
    }

    private void proder() {
        int tmp = 5;
        if (this.countInt == 29) {
            this.countInt = 27;
            return;
        }
        if (this.countInt > 24) {
            tmp = 29 - this.countInt;
        }
        for (int i = this.countInt; i < this.countInt + tmp; i++) {
            this.itemOrderArr[i] = this.itemOrderArr[i + 1];
        }
        this.itemOrderArr[this.countInt + tmp] = this.roundInt;
        this.countInt--;
    }

    public void audClick(View v) {
        if (this.grpBase < 4) {
            try {
                AssetFileDescriptor afd = getAssets().openFd("audio/" + ("group_" + this.grpBase) + "/" + ("tar_set_" + this.tarBase) + "/s_" + this.roundInt + ".mp3");
                this.myMp.reset();
                this.myMp.setDataSource(afd.getFileDescriptor(), afd.getStartOffset(), afd.getLength());
                afd.close();
                this.myMp.prepare();
                this.myMp.start();
            } catch (Exception e) {
            }
        }
    }

    public void settingsClick(View v) {
        Intent myInt = new Intent(getApplicationContext(), SettingsActivityReview.class);
        myInt.putExtra("playAudBool", this.playAudBool);
        myInt.putExtra("showPinyinBool", this.showPinyinBool);
        myInt.putExtra("showEnglishBool", this.showEnglishBool);
        startActivityForResult(myInt, 0);
    }

    /* access modifiers changed from: protected */
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        boolean altBool = data.getBooleanExtra("altBool", false);
        if (resultCode == 0 && altBool) {
            Boolean oldPlayAudBool = Boolean.valueOf(this.playAudBool);
            this.playAudBool = data.getBooleanExtra("playAudBool", false);
            this.showPinyinBool = data.getBooleanExtra("showPinyinBool", false);
            this.showEnglishBool = data.getBooleanExtra("showEnglishBool", false);
            if (this.playAudBool && !oldPlayAudBool.booleanValue()) {
                audClick((View) null);
            }
            if (this.showPinyinBool) {
                this.revealedLine0.setVisibility(0);
            } else {
                this.revealedLine0.setVisibility(8);
            }
            if (this.showEnglishBool) {
                this.revealedLine1.setVisibility(0);
            } else {
                this.revealedLine1.setVisibility(8);
            }
        }
    }

    private int[] ranSeq() {
        int[] outArr = new int[30];
        ArrayList<Integer> tArr = new ArrayList<>();
        for (int i = 0; i < 30; i++) {
            tArr.add(Integer.valueOf(i));
        }
        Collections.shuffle(tArr);
        for (int i2 = 0; i2 < 30; i2++) {
            outArr[i2] = tArr.get(i2).intValue();
        }
        return outArr;
    }

    public void onStop() {
        super.onStop();
        this.myWrap.updateStats((long) this.viewsCount, (System.currentTimeMillis() - this.startTime) / 1000, -1, -1);
    }

    public void onDestroy() {
        super.onDestroy();
        this.myWrap.closeDatabase();
        this.roundHandler.removeCallbacks(this.round);
    }
}
