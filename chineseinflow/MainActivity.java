package com.gamestolearnenglish.chineseinflow;

import android.content.Intent;
import android.graphics.Typeface;
import android.os.Bundle;
import android.support.v7.app.AppCompatActivity;
import android.view.ContextMenu;
import android.view.MenuItem;
import android.view.View;
import android.widget.Button;
import android.widget.TextView;
import database.DatabaseWrapper;

public class MainActivity extends AppCompatActivity {
    private Button[] buttons;
    private int curGrp;
    private int menuContent;
    private TextView subTitleText;
    private View tmpView;

    /* access modifiers changed from: protected */
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView((int) R.layout.activity_main);
        setVolumeControlStream(3);
        initButs();
        this.curGrp = 0;
        setGroup();
    }

    public void onResume() {
        super.onResume();
        showStats();
    }

    private void initButs() {
        this.buttons = new Button[6];
        for (int i = 0; i < 6; i++) {
            this.buttons[i] = (Button) findViewById(getResources().getIdentifier("activityMainButton" + i, "id", getPackageName()));
            this.buttons[i].setTypeface(Typeface.createFromAsset(getAssets(), "AmaBoldItalic.ttf"));
            if (i != 5) {
                registerForContextMenu(this.buttons[i]);
            }
        }
        this.subTitleText = (TextView) findViewById(R.id.activtiyMainSubTitleText);
        this.subTitleText.setTypeface(Typeface.createFromAsset(getAssets(), "AmaBold.ttf"));
    }

    private void setGroup() {
        if (this.curGrp == 0) {
            this.subTitleText.setText("HSK 1");
        }
        if (this.curGrp == 1) {
            this.subTitleText.setText("HSK 2");
        }
        if (this.curGrp == 2) {
            this.subTitleText.setText("HSK 3a");
        }
        if (this.curGrp == 3) {
            this.subTitleText.setText("HSK 3b");
        }
        if (this.curGrp == 4) {
            this.subTitleText.setText("HSK 4a");
        }
        if (this.curGrp == 5) {
            this.subTitleText.setText("HSK 4b");
        }
        if (this.curGrp == 6) {
            this.subTitleText.setText("HSK 4c");
        }
        if (this.curGrp == 7) {
            this.subTitleText.setText("HSK 4d");
        }
        if (this.curGrp == 8) {
            this.subTitleText.setText("HSK 5a");
        }
        if (this.curGrp == 9) {
            this.subTitleText.setText("HSK 5b");
        }
        if (this.curGrp == 10) {
            this.subTitleText.setText("HSK 5c");
        }
        if (this.curGrp == 11) {
            this.subTitleText.setText("HSK 5d");
        }
        if (this.curGrp == 12) {
            this.subTitleText.setText("HSK 5e");
        }
        if (this.curGrp == 13) {
            this.subTitleText.setText("HSK 5f");
        }
        if (this.curGrp == 14) {
            this.subTitleText.setText("HSK 5g");
        }
        if (this.curGrp == 15) {
            this.subTitleText.setText("HSK 5h");
        }
        if (this.curGrp == 16) {
            this.subTitleText.setText("HSK 5i");
        }
        for (int i = 0; i < 5; i++) {
            this.buttons[i].setText(" " + ((i * 30) + (this.curGrp * 150) + 1) + " - " + (((i + 1) * 30) + (this.curGrp * 150)) + " ");
        }
    }

    private void showStats() {
        DatabaseWrapper myWrap = new DatabaseWrapper(this, this.curGrp, 0);
        int[] myStats = myWrap.getAllStats();
        myWrap.closeDatabase();
        float[] cumStats = new float[5];
        for (int i = 0; i < 5; i++) {
            this.buttons[i].setBackgroundResource(R.drawable.blank_button_selector);
            cumStats[i] = (float) (myStats[i * 3] * 4);
            cumStats[i] = cumStats[i] + ((float) (myStats[(i * 3) + 1] * 6));
            cumStats[i] = cumStats[i] + ((float) (myStats[(i * 3) + 2] * 6));
            if (cumStats[i] >= 20.0f) {
                this.buttons[i].setBackgroundResource(R.drawable.blank_button_bronze_selector);
            }
            if (cumStats[i] >= 50.0f) {
                this.buttons[i].setBackgroundResource(R.drawable.blank_button_silver_selector);
            }
            if (cumStats[i] == 100.0f) {
                this.buttons[i].setBackgroundResource(R.drawable.blank_button_gold_selector);
            }
        }
    }

    public void onCreateContextMenu(ContextMenu m, View v, ContextMenu.ContextMenuInfo menuInfo) {
        super.onCreateContextMenu(m, v, menuInfo);
        this.tmpView = v;
        String titleText = String.valueOf(this.subTitleText.getText()) + " - Set ";
        switch (v.getId()) {
            case R.id.activityMainButton0 /*2131558552*/:
                titleText = titleText + "1";
                this.menuContent = 0;
                break;
            case R.id.activityMainButton1 /*2131558553*/:
                titleText = titleText + "2";
                this.menuContent = 30;
                break;
            case R.id.activityMainButton2 /*2131558554*/:
                titleText = titleText + "3";
                this.menuContent = 60;
                break;
            case R.id.activityMainButton3 /*2131558555*/:
                titleText = titleText + "4";
                this.menuContent = 90;
                break;
            case R.id.activityMainButton4 /*2131558556*/:
                titleText = titleText + "5";
                this.menuContent = 120;
                break;
        }
        m.setHeaderTitle(titleText);
        m.add(0, 0, 0, "Use these characters");
        m.add(0, 1, 0, "Reset data for this set");
    }

    public boolean onContextItemSelected(MenuItem item) {
        switch (item.getItemId()) {
            case 0:
                buttonClick(this.tmpView);
                break;
            case 1:
                sendReset();
                break;
        }
        return true;
    }

    private void sendReset() {
        DatabaseWrapper myWrap = new DatabaseWrapper(this, this.curGrp, this.menuContent);
        myWrap.resetData(true, true, true, true);
        myWrap.closeDatabase();
        showStats();
    }

    public void buttonClick(View v) {
        Intent myIntent = new Intent(getApplicationContext(), MenuActivity.class);
        switch (v.getId()) {
            case R.id.activityMainButton0 /*2131558552*/:
                myIntent.putExtra("contentSelected", 0);
                break;
            case R.id.activityMainButton1 /*2131558553*/:
                myIntent.putExtra("contentSelected", 30);
                break;
            case R.id.activityMainButton2 /*2131558554*/:
                myIntent.putExtra("contentSelected", 60);
                break;
            case R.id.activityMainButton3 /*2131558555*/:
                myIntent.putExtra("contentSelected", 90);
                break;
            case R.id.activityMainButton4 /*2131558556*/:
                myIntent.putExtra("contentSelected", 120);
                break;
        }
        myIntent.putExtra("groupSelected", this.curGrp);
        myIntent.setFlags(65536);
        startActivity(myIntent);
        overridePendingTransition(0, 0);
    }

    public void leftTap(View v) {
        this.curGrp--;
        if (this.curGrp == -1) {
            this.curGrp = 16;
        }
        setGroup();
        showStats();
    }

    public void rightTap(View v) {
        this.curGrp++;
        if (this.curGrp == 17) {
            this.curGrp = 0;
        }
        setGroup();
        showStats();
    }

    public void moreClick(View v) {
        this.curGrp++;
        if (this.curGrp == 17) {
            this.curGrp = 0;
        }
        setGroup();
        showStats();
    }

    public void onStop() {
        super.onStop();
    }
}
