package com.gamestolearnenglish.chineseinflow;

import android.content.Intent;
import android.content.res.AssetFileDescriptor;
import android.media.MediaPlayer;
import android.os.AsyncTask;
import android.os.Bundle;
import android.os.Handler;
import android.support.v7.app.AppCompatActivity;
import android.text.Html;
import android.view.View;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import com.gamestolearnenglish.chineseinflow.MetricsLearn;
import database.DatabaseWrapper;
import java.util.ArrayList;
import java.util.Collections;

public class LearnActivity extends AppCompatActivity {
    private LinearLayout buttonLayout0;
    private LinearLayout buttonLayout1;
    private LinearLayout buttonLayout2;
    private Button[] buttons;
    /* access modifiers changed from: private */
    public boolean buttonsActive;
    private int countVar;
    private int curDisplayVal;
    /* access modifiers changed from: private */
    public ArrayList<Integer> curShuffle;
    private float defaultTextSize;
    /* access modifiers changed from: private */
    public float defaultTopSize;
    private Handler delayExitHandler;
    private Runnable delayHint = new Runnable() {
        public void run() {
            ((ImageView) LearnActivity.this.findViewById(R.id.activityLearnHint)).setVisibility(0);
            ((ImageView) LearnActivity.this.findViewById(R.id.activityLearnHintDummy)).setVisibility(0);
        }
    };
    private boolean delayTextBool = false;
    private Runnable endGameExit = new Runnable() {
        public void run() {
            long passedTime = (System.currentTimeMillis() - LearnActivity.this.startTime) / 1000;
            Intent myInt = new Intent(LearnActivity.this.getApplicationContext(), EndActivity.class);
            myInt.putExtra("duration", passedTime);
            myInt.putExtra("misses", LearnActivity.this.missesCount);
            myInt.putExtra("activity", 0);
            myInt.putExtra("contentSelected", LearnActivity.this.tarBase);
            myInt.putExtra("groupSelected", LearnActivity.this.grpBase);
            myInt.setFlags(65536);
            LearnActivity.this.startActivity(myInt);
            LearnActivity.this.overridePendingTransition(0, 0);
            LearnActivity.this.finish();
        }
    };
    /* access modifiers changed from: private */
    public int grpBase;
    private Handler hintDelayHandler;
    private boolean hintShowing = false;
    private float largerTextSize;
    private int lastDisplayPos;
    /* access modifiers changed from: private */
    public TextView mainText;
    /* access modifiers changed from: private */
    public int missesCount;
    /* access modifiers changed from: private */
    public AsyncSave myAsyncSave;
    /* access modifiers changed from: private */
    public MetricsLearn myBox;
    private Handler myHandler;
    /* access modifiers changed from: private */
    public ArrayList<Item> myItems;
    private MediaPlayer myMp = new MediaPlayer();
    /* access modifiers changed from: private */
    public DatabaseWrapper myWrap;
    /* access modifiers changed from: private */
    public boolean playAudBool = false;
    private ArrayList<Integer> positionShuffle;
    private long promptDelay;
    private Handler promptHandler;
    private Runnable round = new Runnable() {
        public void run() {
            LearnActivity.access$008(LearnActivity.this);
            ArrayList unused = LearnActivity.this.curShuffle = LearnActivity.this.myBox.newRound();
            int unused2 = LearnActivity.this.roundVar = ((Integer) LearnActivity.this.curShuffle.get(0)).intValue();
            LearnActivity.this.setPositionShuffle();
            LearnActivity.this.setButtons();
            LearnActivity.this.showPrompt();
            LearnActivity.this.setAppearance();
            LearnActivity.this.saveNowCheck();
            boolean unused3 = LearnActivity.this.buttonsActive = true;
            if (LearnActivity.this.playAudBool) {
                LearnActivity.this.audClick((View) null);
            }
        }
    };
    /* access modifiers changed from: private */
    public int roundVar;
    private Runnable setTextFields = new Runnable() {
        public void run() {
            if (LearnActivity.this.myBox.itemDisplayType == 0) {
                LearnActivity.this.mainText.setText(((Item) LearnActivity.this.myItems.get(LearnActivity.this.roundVar)).getHanzi());
                LearnActivity.this.mainText.setTextSize(0, LearnActivity.this.defaultTopSize);
            } else {
                String myPinzi = ((Item) LearnActivity.this.myItems.get(LearnActivity.this.roundVar)).getPinzi();
                String myEngzi = ((Item) LearnActivity.this.myItems.get(LearnActivity.this.roundVar)).getEngzi();
                if (LearnActivity.this.showEnglishBool) {
                    if (LearnActivity.this.showPinyinBool) {
                        LearnActivity.this.mainText.setText(Html.fromHtml(myPinzi + "<br><b>" + myEngzi + "</b>"));
                    } else {
                        LearnActivity.this.mainText.setText(Html.fromHtml("<b>" + myEngzi + "</b>"));
                    }
                } else if (LearnActivity.this.showPinyinBool) {
                    LearnActivity.this.mainText.setText(Html.fromHtml(myPinzi));
                } else {
                    LearnActivity.this.mainText.setText("");
                }
                LearnActivity.this.mainText.setTextSize(0, LearnActivity.this.smallerTopSize);
            }
            ImageView bBut = (ImageView) LearnActivity.this.findViewById(R.id.audButBig);
            ImageView sBut = (ImageView) LearnActivity.this.findViewById(R.id.audBut);
            if (LearnActivity.this.showTextBool) {
                LearnActivity.this.mainText.setVisibility(0);
            } else {
                LearnActivity.this.mainText.setVisibility(8);
            }
            if (LearnActivity.this.grpBase > 3) {
                bBut.setVisibility(8);
                sBut.setVisibility(4);
            } else if (LearnActivity.this.showTextBool) {
                bBut.setVisibility(8);
                sBut.setVisibility(0);
            } else {
                bBut.setVisibility(0);
                sBut.setVisibility(4);
            }
        }
    };
    /* access modifiers changed from: private */
    public boolean showEnglishBool = true;
    /* access modifiers changed from: private */
    public boolean showPinyinBool = true;
    private boolean showPinyinButtonsBool = true;
    /* access modifiers changed from: private */
    public boolean showTextBool = true;
    /* access modifiers changed from: private */
    public float smallerTopSize;
    /* access modifiers changed from: private */
    public long startTime;
    /* access modifiers changed from: private */
    public int tarBase;

    static /* synthetic */ int access$008(LearnActivity x0) {
        int i = x0.countVar;
        x0.countVar = i + 1;
        return i;
    }

    /* access modifiers changed from: protected */
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView((int) R.layout.activity_learn);
        setVolumeControlStream(3);
        initValues();
        restoreProgress();
        ImageView bBut = (ImageView) findViewById(R.id.audButBig);
        ImageView sBut = (ImageView) findViewById(R.id.audBut);
        if (this.grpBase > 3) {
            bBut.setVisibility(8);
            sBut.setVisibility(4);
        } else if (this.showTextBool) {
            bBut.setVisibility(8);
            sBut.setVisibility(0);
        } else {
            bBut.setVisibility(0);
            sBut.setVisibility(4);
        }
        this.myHandler.post(this.round);
    }

    private void restoreProgress() {
        int[] progInts = this.myWrap.getProgress();
        if (progInts[0] == 0) {
            this.myBox.init();
            this.hintShowing = true;
            this.hintDelayHandler.postDelayed(this.delayHint, 4000);
            return;
        }
        this.myBox.reinit(progInts[0], this.myWrap.getLevels());
        if (progInts[0] < 9) {
            this.myBox.setRestartStageCount(progInts[1]);
        }
    }

    private void removeHint() {
        this.hintShowing = false;
        ((ImageView) findViewById(R.id.activityLearnHint)).setVisibility(8);
        ((ImageView) findViewById(R.id.activityLearnHintDummy)).setVisibility(8);
        this.hintDelayHandler.removeCallbacksAndMessages((Object) null);
    }

    /* access modifiers changed from: protected */
    public void onResume() {
        super.onResume();
        this.startTime = System.currentTimeMillis();
    }

    public void onStop() {
        super.onStop();
        long passedTime = (System.currentTimeMillis() - this.startTime) / 1000;
        int stageCount = this.myBox.getStageCount();
        this.myWrap.updateStats((long) this.countVar, passedTime, this.myBox.getStageVar(), stageCount);
        this.hintDelayHandler.removeCallbacksAndMessages((Object) null);
        this.myHandler.removeCallbacksAndMessages((Object) null);
        this.delayExitHandler.removeCallbacksAndMessages((Object) null);
        this.promptHandler.removeCallbacksAndMessages((Object) null);
    }

    public void onDestroy() {
        super.onDestroy();
        if (this.myAsyncSave != null) {
            this.myAsyncSave.cancel(true);
        }
        this.myMp.release();
        this.myWrap.closeDatabase();
    }

    private void initValues() {
        this.myBox = new MetricsLearn();
        Intent myInt = getIntent();
        this.tarBase = myInt.getIntExtra("contentSelected", 0);
        this.grpBase = myInt.getIntExtra("groupSelected", 0);
        this.myWrap = new DatabaseWrapper(this, this.grpBase, this.tarBase);
        this.myItems = this.myWrap.getItems();
        this.myHandler = new Handler();
        this.hintDelayHandler = new Handler();
        this.delayExitHandler = new Handler();
        this.promptHandler = new Handler();
        this.mainText = (TextView) findViewById(R.id.activityLearnMainText);
        this.buttons = new Button[8];
        this.positionShuffle = new ArrayList<>();
        this.buttonLayout0 = (LinearLayout) findViewById(R.id.learnLay0);
        this.buttonLayout1 = (LinearLayout) findViewById(R.id.learnLay1);
        this.buttonLayout2 = (LinearLayout) findViewById(R.id.learnLay2);
        for (int i = 0; i < 8; i++) {
            this.buttons[i] = (Button) findViewById(getResources().getIdentifier("learnButton" + i, "id", getPackageName()));
        }
        this.defaultTextSize = this.buttons[0].getTextSize();
        this.largerTextSize = (float) (((double) this.defaultTextSize) * 1.5d);
        this.defaultTopSize = this.mainText.getTextSize();
        this.smallerTopSize = this.defaultTopSize / 2.0f;
        this.countVar = 0;
        this.curDisplayVal = 0;
        this.lastDisplayPos = 0;
        this.missesCount = 0;
        this.promptDelay = 0;
    }

    /* access modifiers changed from: private */
    public void saveNowCheck() {
        if (this.myBox.checkSaveNow()) {
            this.myWrap.saveLevels(this.myBox.getSaveNowPairs());
        }
    }

    public void buttonClick(View v) {
        if (this.buttonsActive) {
            this.buttonsActive = false;
            for (int i = 0; i < 8; i++) {
                if (v == this.buttons[i]) {
                    if (i == this.positionShuffle.get(0).intValue()) {
                        hitCorrect(i);
                    } else {
                        hitWrong(i);
                    }
                }
            }
        }
        if (this.hintShowing) {
            removeHint();
        }
    }

    private void hitCorrect(int i) {
        this.promptHandler.removeCallbacksAndMessages((Object) null);
        for (Button myBut : this.buttons) {
            if (myBut.getVisibility() == 0) {
                myBut.setVisibility(4);
            }
        }
        this.buttons[i].setVisibility(0);
        ArrayList<MetricsLearn.MyPair> gotPairs = this.myBox.updateStats();
        if (gotPairs.size() > 0 && (this.myAsyncSave == null || this.myAsyncSave.getStatus() != AsyncTask.Status.RUNNING)) {
            this.myAsyncSave = new AsyncSave();
            this.myAsyncSave.execute(new ArrayList[]{gotPairs});
        }
        if (this.myBox.FINISHED) {
            endGame();
        } else {
            this.myHandler.postDelayed(this.round, this.myBox.delayValue);
        }
    }

    private void endGame() {
        for (Button myBut : this.buttons) {
            myBut.setVisibility(4);
        }
        this.mainText.setText(Html.fromHtml("<b>The End</b>"));
        this.delayExitHandler.postDelayed(this.endGameExit, 2000);
    }

    private void hitWrong(int i) {
        this.missesCount++;
        this.buttons[i].setVisibility(4);
        this.buttonsActive = true;
        this.myBox.missedFlag = true;
    }

    /* access modifiers changed from: private */
    public void setPositionShuffle() {
        for (int i = this.positionShuffle.size() - 1; i >= 0; i--) {
            this.positionShuffle.remove(i);
        }
        for (int i2 = 0; i2 < this.myBox.itemDisplayNumber; i2++) {
            this.positionShuffle.add(Integer.valueOf(i2));
        }
        do {
            Collections.shuffle(this.positionShuffle);
            if (this.lastDisplayPos != this.positionShuffle.get(0).intValue() || this.positionShuffle.size() <= 3) {
            }
            Collections.shuffle(this.positionShuffle);
            return;
        } while (this.positionShuffle.size() <= 3);
    }

    /* access modifiers changed from: private */
    public void setButtons() {
        for (int i = 0; i < this.positionShuffle.size(); i++) {
            Integer myIn = this.curShuffle.get(i);
            Integer myIn2 = this.positionShuffle.get(i);
            if (this.myBox.itemDisplayType == 0) {
                String myPinzi = this.myItems.get(myIn.intValue()).getPinzi();
                String myEngzi = this.myItems.get(myIn.intValue()).getEngzi();
                if (this.showEnglishBool) {
                    if (this.showPinyinButtonsBool) {
                        this.buttons[myIn2.intValue()].setText(Html.fromHtml(myPinzi + "<br><b>" + myEngzi + "</b>"));
                    } else {
                        this.buttons[myIn2.intValue()].setText(Html.fromHtml("<b>" + myEngzi + "</b>"));
                    }
                } else if (this.showPinyinBool) {
                    this.buttons[myIn2.intValue()].setText(Html.fromHtml(myPinzi));
                } else {
                    this.buttons[myIn2.intValue()].setText("");
                }
                this.buttons[myIn2.intValue()].setLineSpacing(0.0f, 1.1f);
                this.buttons[i].setTextSize(0, this.defaultTextSize);
            } else {
                this.buttons[myIn2.intValue()].setText(this.myItems.get(myIn.intValue()).getHanzi());
                this.buttons[myIn2.intValue()].setLineSpacing(0.0f, 1.0f);
                this.buttons[i].setTextSize(0, this.largerTextSize);
            }
        }
    }

    /* access modifiers changed from: private */
    public void showPrompt() {
        if (this.promptDelay > 0) {
            this.mainText.setText("");
        }
        this.promptHandler.removeCallbacksAndMessages((Object) null);
        this.promptHandler.postDelayed(this.setTextFields, this.promptDelay);
    }

    /* access modifiers changed from: private */
    public void setAppearance() {
        int displayVal = this.positionShuffle.size();
        if (displayVal != this.curDisplayVal) {
            this.buttonLayout1.setVisibility(8);
            this.buttonLayout2.setVisibility(8);
            if (this.positionShuffle.size() > 3) {
                this.buttonLayout1.setVisibility(0);
            }
            if (this.positionShuffle.size() > 5) {
                this.buttonLayout2.setVisibility(0);
            }
            if (this.curDisplayVal == 1 && displayVal == 3) {
                showTransOneThree();
            }
            if (this.curDisplayVal == 3 && displayVal == 1) {
                showTransThreeOne();
            }
            if (this.curDisplayVal == 3 && displayVal == 5) {
                showTransThreeFive();
            }
            if (this.curDisplayVal == 5 && displayVal == 1) {
                showTransFiveOne();
            }
            if (this.curDisplayVal == 5 && displayVal == 8) {
                showTransFiveEight();
            }
            if (this.curDisplayVal == 8 && displayVal == 1) {
                showTransEightOne();
            }
        }
        this.curDisplayVal = displayVal;
        for (int i = 0; i < 8; i++) {
            this.buttons[i].setVisibility(8);
        }
        for (int i2 = 0; i2 < this.positionShuffle.size(); i2++) {
            this.buttons[i2].setVisibility(0);
        }
        this.lastDisplayPos = this.positionShuffle.get(0).intValue();
    }

    public void audClick(View v) {
        if (this.grpBase < 4) {
            try {
                AssetFileDescriptor afd = getAssets().openFd("audio/" + ("group_" + this.grpBase) + "/" + ("tar_set_" + this.tarBase) + "/s_" + this.roundVar + ".mp3");
                this.myMp.reset();
                this.myMp.setDataSource(afd.getFileDescriptor(), afd.getStartOffset(), afd.getLength());
                afd.close();
                this.myMp.prepare();
                this.myMp.start();
            } catch (Exception e) {
            }
        }
    }

    public void settingsClick(View v) {
        Intent myInt = new Intent(getApplicationContext(), SettingsActivityLearn.class);
        myInt.putExtra("playAudBool", this.playAudBool);
        myInt.putExtra("showPinyinBool", this.showPinyinBool);
        myInt.putExtra("showEnglishBool", this.showEnglishBool);
        myInt.putExtra("showTextBool", this.showTextBool);
        myInt.putExtra("delayTextBool", this.delayTextBool);
        myInt.putExtra("showPinyinButtonsBool", this.showPinyinButtonsBool);
        startActivityForResult(myInt, 0);
    }

    /* access modifiers changed from: protected */
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        boolean altBool = data.getBooleanExtra("altBool", false);
        if (resultCode == 0 && altBool) {
            Boolean oldPlayAudBool = Boolean.valueOf(this.playAudBool);
            this.playAudBool = data.getBooleanExtra("playAudBool", false);
            this.showPinyinBool = data.getBooleanExtra("showPinyinBool", false);
            this.showEnglishBool = data.getBooleanExtra("showEnglishBool", false);
            this.showTextBool = data.getBooleanExtra("showTextBool", false);
            this.delayTextBool = data.getBooleanExtra("delayTextBool", false);
            this.showPinyinButtonsBool = data.getBooleanExtra("showPinyinButtonsBool", false);
            if (this.playAudBool && !oldPlayAudBool.booleanValue()) {
                audClick((View) null);
            }
            setButtons();
            if (this.delayTextBool) {
                this.promptDelay = 1000;
            } else {
                this.promptDelay = 0;
            }
            showPrompt();
        }
    }

    private void showTransOneThree() {
        Animation myAni = AnimationUtils.loadAnimation(this, R.anim.one_three_anim);
        Animation myFade = AnimationUtils.loadAnimation(this, R.anim.fade_in_anim);
        this.buttons[0].startAnimation(myAni);
        this.buttons[1].startAnimation(myFade);
        this.buttons[2].startAnimation(myFade);
        this.myWrap.incLearnProgress();
    }

    private void showTransThreeOne() {
        if (this.lastDisplayPos == 0) {
            this.buttons[0].startAnimation(AnimationUtils.loadAnimation(this, R.anim.three_one_ling_anim));
        }
        if (this.lastDisplayPos == 1) {
            this.buttons[0].startAnimation(AnimationUtils.loadAnimation(this, R.anim.three_one_anim));
        }
        if (this.lastDisplayPos == 2) {
            this.buttons[0].startAnimation(AnimationUtils.loadAnimation(this, R.anim.three_one_er_anim));
        }
    }

    private void showTransThreeFive() {
        Animation myFade = AnimationUtils.loadAnimation(this, R.anim.fade_in_anim);
        Animation myAni = AnimationUtils.loadAnimation(this, R.anim.three_five_anim);
        if (this.showTextBool) {
            this.mainText.startAnimation(AnimationUtils.loadAnimation(this, R.anim.three_five_trans_top));
        }
        this.buttonLayout0.startAnimation(myAni);
        for (int i = 0; i < 5; i++) {
            if (i != this.lastDisplayPos) {
                this.buttons[i].startAnimation(myFade);
            }
        }
    }

    private void showTransFiveOne() {
        Animation myAni = AnimationUtils.loadAnimation(this, R.anim.five_one_anim);
        if (this.showTextBool) {
            this.mainText.startAnimation(myAni);
        }
        if (this.lastDisplayPos < 3) {
            this.buttonLayout0.startAnimation(myAni);
            if (this.lastDisplayPos == 0) {
                this.buttons[0].startAnimation(AnimationUtils.loadAnimation(this, R.anim.three_one_ling_anim));
            }
            if (this.lastDisplayPos == 1) {
                this.buttons[0].startAnimation(AnimationUtils.loadAnimation(this, R.anim.three_one_anim));
            }
            if (this.lastDisplayPos == 2) {
                this.buttons[0].startAnimation(AnimationUtils.loadAnimation(this, R.anim.three_one_er_anim));
                return;
            }
            return;
        }
        this.buttons[0].startAnimation(AnimationUtils.loadAnimation(this, R.anim.fade_in_anim));
    }

    private void showTransFiveEight() {
        Animation myFade = AnimationUtils.loadAnimation(this, R.anim.fade_in_anim);
        Animation myAni = AnimationUtils.loadAnimation(this, R.anim.five_eight_trans);
        if (this.showTextBool) {
            this.mainText.startAnimation(AnimationUtils.loadAnimation(this, R.anim.five_eight_trans_top));
        }
        this.buttonLayout0.startAnimation(myAni);
        this.buttonLayout1.startAnimation(myAni);
        for (int i = 0; i < 8; i++) {
            if (i != this.lastDisplayPos) {
                this.buttons[i].startAnimation(myFade);
            }
        }
    }

    private void showTransEightOne() {
        this.buttons[0].startAnimation(AnimationUtils.loadAnimation(this, R.anim.fade_in_anim));
        if (this.showTextBool) {
            this.mainText.startAnimation(AnimationUtils.loadAnimation(this, R.anim.eight_one_trans));
        }
    }

    private class AsyncSave extends AsyncTask<ArrayList<MetricsLearn.MyPair>, Void, Void> {
        private AsyncSave() {
        }

        /* access modifiers changed from: protected */
        public Void doInBackground(ArrayList<MetricsLearn.MyPair>... $gotPairs) {
            LearnActivity.this.myWrap.saveLevels($gotPairs[0]);
            return null;
        }

        /* access modifiers changed from: protected */
        public void onPostExecute(Void unused) {
            AsyncSave unused2 = LearnActivity.this.myAsyncSave = null;
        }
    }
}
