package com.gamestolearnenglish.chineseinflow;

import android.content.Intent;
import android.content.res.AssetFileDescriptor;
import android.media.MediaPlayer;
import android.os.Bundle;
import android.os.Handler;
import android.support.v7.app.AppCompatActivity;
import android.text.Html;
import android.view.View;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.Toast;
import database.DatabaseWrapper;
import java.util.ArrayList;
import java.util.Collections;

public class PracticeActivity extends AppCompatActivity {
    /* access modifiers changed from: private */
    public boolean buttonsActive;
    /* access modifiers changed from: private */
    public CountDownFragment countDown;
    private int countVar;
    /* access modifiers changed from: private */
    public int[] curShuffle;
    private Handler delayExitHandler;
    private boolean delayTextBool = false;
    private Runnable delayedStart = new Runnable() {
        public void run() {
            PracticeActivity.this.roundHandler.post(PracticeActivity.this.round);
            PracticeActivity.this.countDown.startTimer();
        }
    };
    private Runnable endGameExit = new Runnable() {
        public void run() {
            long passedTime = (System.currentTimeMillis() - PracticeActivity.this.startTime) / 1000;
            int myProg = PracticeActivity.this.myBox.getProg();
            Intent myInt = new Intent(PracticeActivity.this.getApplicationContext(), EndActivity.class);
            myInt.putExtra("duration", passedTime);
            myInt.putExtra("outcome", PracticeActivity.this.outcome);
            myInt.putExtra("myProg", myProg);
            myInt.putExtra("activity", 1);
            myInt.putExtra("contentSelected", PracticeActivity.this.tarBase);
            myInt.putExtra("groupSelected", PracticeActivity.this.grpBase);
            myInt.setFlags(65536);
            PracticeActivity.this.startActivity(myInt);
            PracticeActivity.this.overridePendingTransition(0, 0);
            PracticeActivity.this.finish();
        }
    };
    /* access modifiers changed from: private */
    public int grpBase;
    /* access modifiers changed from: private */
    public TextView mainText;
    /* access modifiers changed from: private */
    public MetricsPractice myBox;
    private Button[] myButtons;
    /* access modifiers changed from: private */
    public ArrayList<Item> myItems;
    private MediaPlayer myMp = new MediaPlayer();
    /* access modifiers changed from: private */
    public DatabaseWrapper myWrap;
    /* access modifiers changed from: private */
    public int outcome;
    /* access modifiers changed from: private */
    public boolean playAudBool = false;
    /* access modifiers changed from: private */
    public ArrayList<Integer> positionShuffle;
    private long promptDelay;
    private Handler promptHandler;
    /* access modifiers changed from: private */
    public Runnable round = new Runnable() {
        public void run() {
            PracticeActivity.access$308(PracticeActivity.this);
            int[] unused = PracticeActivity.this.curShuffle = PracticeActivity.this.myBox.getRoundVar();
            int unused2 = PracticeActivity.this.roundVar = PracticeActivity.this.curShuffle[0];
            PracticeActivity.this.showPrompt();
            Collections.shuffle(PracticeActivity.this.positionShuffle);
            PracticeActivity.this.setButtons();
            if (PracticeActivity.this.playAudBool) {
                PracticeActivity.this.audClick((View) null);
            }
        }
    };
    /* access modifiers changed from: private */
    public Handler roundHandler;
    /* access modifiers changed from: private */
    public int roundVar;
    private Runnable setTextFields = new Runnable() {
        public void run() {
            String myPinzi = ((Item) PracticeActivity.this.myItems.get(PracticeActivity.this.roundVar)).getPinzi();
            String myEngzi = ((Item) PracticeActivity.this.myItems.get(PracticeActivity.this.roundVar)).getEngzi();
            if (PracticeActivity.this.showEnglishBool) {
                if (PracticeActivity.this.showPinyinBool) {
                    PracticeActivity.this.mainText.setText(Html.fromHtml(myPinzi + "<br><b>" + myEngzi + "</b>"));
                } else {
                    PracticeActivity.this.mainText.setText(Html.fromHtml("<b>" + myEngzi + "</b>"));
                }
            } else if (PracticeActivity.this.showPinyinBool) {
                PracticeActivity.this.mainText.setText(Html.fromHtml(myPinzi));
            } else {
                PracticeActivity.this.mainText.setText("");
            }
        }
    };
    /* access modifiers changed from: private */
    public boolean showEnglishBool = true;
    /* access modifiers changed from: private */
    public boolean showPinyinBool = true;
    /* access modifiers changed from: private */
    public long startTime;
    /* access modifiers changed from: private */
    public int tarBase;
    private Runnable timeOut2 = new Runnable() {
        public void run() {
            boolean unused = PracticeActivity.this.buttonsActive = false;
            PracticeActivity.this.mainText.setText(Html.fromHtml("<b>Time over</b>"));
            if (PracticeActivity.this.myBox.SDMODE.booleanValue()) {
                PracticeActivity.this.myWrap.incPracticeProgress();
                int unused2 = PracticeActivity.this.outcome = 2;
            } else {
                int unused3 = PracticeActivity.this.outcome = 1;
            }
            PracticeActivity.this.endGame();
            PracticeActivity.this.roundHandler.removeCallbacks(PracticeActivity.this.round);
        }
    };

    static /* synthetic */ int access$308(PracticeActivity x0) {
        int i = x0.countVar;
        x0.countVar = i + 1;
        return i;
    }

    /* access modifiers changed from: protected */
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView((int) R.layout.activity_practice);
        setVolumeControlStream(3);
        initValues();
        ImageView sBut = (ImageView) findViewById(R.id.audBut);
        if (this.grpBase <= 3) {
            sBut.setVisibility(0);
        } else {
            sBut.setVisibility(4);
        }
        this.roundHandler.postDelayed(this.delayedStart, 1000);
        this.startTime = System.currentTimeMillis();
    }

    public void onStop() {
        super.onStop();
        this.myWrap.updateStats((long) this.countVar, (System.currentTimeMillis() - this.startTime) / 1000, -1, -1);
        this.roundHandler.removeCallbacksAndMessages((Object) null);
        this.promptHandler.removeCallbacksAndMessages((Object) null);
        this.delayExitHandler.removeCallbacksAndMessages((Object) null);
    }

    public void onDestroy() {
        super.onDestroy();
        this.myWrap.closeDatabase();
        this.myMp.release();
        this.roundHandler.removeCallbacksAndMessages((Object) null);
        this.delayExitHandler.removeCallbacksAndMessages((Object) null);
    }

    private void initValues() {
        this.myBox = new MetricsPractice();
        this.countDown = (CountDownFragment) getSupportFragmentManager().findFragmentById(R.id.activityPracticeFragment);
        Intent myInt = getIntent();
        this.tarBase = myInt.getIntExtra("contentSelected", 0);
        this.grpBase = myInt.getIntExtra("groupSelected", 0);
        this.myWrap = new DatabaseWrapper(this, this.grpBase, this.tarBase);
        this.myItems = this.myWrap.getItems();
        this.roundHandler = new Handler();
        this.delayExitHandler = new Handler();
        this.promptHandler = new Handler();
        this.mainText = (TextView) findViewById(R.id.activityPracticeText);
        this.mainText.setText("");
        this.myButtons = new Button[7];
        this.positionShuffle = new ArrayList<>();
        for (int i = 0; i < 7; i++) {
            this.myButtons[i] = (Button) findViewById(getResources().getIdentifier("activityPracticeButton" + i, "id", getPackageName()));
            this.myButtons[i].setVisibility(4);
            this.positionShuffle.add(Integer.valueOf(i));
        }
        this.countVar = -1;
    }

    /* access modifiers changed from: private */
    public void showPrompt() {
        if (this.promptDelay > 0) {
            this.mainText.setText("");
        }
        this.promptHandler.removeCallbacksAndMessages((Object) null);
        this.promptHandler.postDelayed(this.setTextFields, this.promptDelay);
    }

    private void hitCorrect(int i) {
        this.countDown.addTime(75);
        this.promptHandler.removeCallbacksAndMessages((Object) null);
        if (this.myBox.timeDelay > 300) {
            this.mainText.setText("Correct");
            this.mainText.setText(Html.fromHtml("<b>Correct</b>"));
            for (Button myBut : this.myButtons) {
                myBut.setVisibility(4);
            }
            this.myButtons[i].setVisibility(0);
        }
        this.myBox.hit();
        if (!this.myBox.FINISHED.booleanValue()) {
            if (this.myBox.SDMODE.booleanValue()) {
                this.countDown.altTimerInc(true);
            } else {
                this.countDown.altTimerInc(false);
            }
            if (this.myBox.SDFLAG.booleanValue()) {
                beginSDMode();
            } else {
                this.roundHandler.postDelayed(this.round, (long) this.myBox.timeDelay);
            }
        } else {
            this.countDown.stopTimer();
            this.myWrap.incPracticeProgress();
            this.mainText.setText(Html.fromHtml("<b>Well Done!</b>"));
            this.outcome = 0;
            endGame();
        }
    }

    private void beginSDMode() {
        this.countDown.addTime(300);
        this.myBox.SDFLAG = false;
        this.roundHandler.postDelayed(this.round, 5000);
        for (int i = 0; i < 7; i++) {
            this.myButtons[i].setVisibility(4);
        }
        this.mainText.setText(Html.fromHtml("<b>Sudden Death Mode Begins!</b>"));
        Toast myToast = Toast.makeText(getApplicationContext(), "This is sudden death mode", 0);
        myToast.setGravity(16, 0, 0);
        myToast.show();
        Toast myToast2 = Toast.makeText(getApplicationContext(), "One mistake will end this round", 0);
        myToast2.setGravity(16, 0, 0);
        myToast2.show();
    }

    private void hitWrong(int i) {
        this.myButtons[i].setVisibility(4);
        this.myBox.miss();
        if (this.myBox.SDMODE.booleanValue()) {
            this.myWrap.incPracticeProgress();
            this.mainText.setText(Html.fromHtml("<b>Sudden Death!</b>"));
            this.countDown.stopTimer();
            this.outcome = 3;
            endGame();
            return;
        }
        this.buttonsActive = true;
    }

    /* access modifiers changed from: private */
    public void setButtons() {
        for (int i = 0; i < 7; i++) {
            this.myButtons[this.positionShuffle.get(i).intValue()].setText(this.myItems.get(Integer.valueOf(this.curShuffle[i]).intValue()).getHanzi());
            this.myButtons[i].setVisibility(0);
        }
        this.buttonsActive = true;
    }

    public void buttonClick(View v) {
        if (this.buttonsActive) {
            for (int i = 0; i < 7; i++) {
                if (v == this.myButtons[i]) {
                    this.buttonsActive = false;
                    if (i == this.positionShuffle.get(0).intValue()) {
                        hitCorrect(i);
                    } else {
                        hitWrong(i);
                    }
                }
            }
        }
    }

    public void audClick(View v) {
        if (this.grpBase < 4) {
            try {
                AssetFileDescriptor afd = getAssets().openFd("audio/" + ("group_" + this.grpBase) + "/" + ("tar_set_" + this.tarBase) + "/s_" + this.roundVar + ".mp3");
                this.myMp.reset();
                this.myMp.setDataSource(afd.getFileDescriptor(), afd.getStartOffset(), afd.getLength());
                afd.close();
                this.myMp.prepare();
                this.myMp.start();
            } catch (Exception e) {
            }
        }
    }

    public void settingsClick(View v) {
        Intent myInt = new Intent(getApplicationContext(), SettingsActivityPractice.class);
        myInt.putExtra("playAudBool", this.playAudBool);
        myInt.putExtra("showPinyinBool", this.showPinyinBool);
        myInt.putExtra("showEnglishBool", this.showEnglishBool);
        myInt.putExtra("delayTextBool", this.delayTextBool);
        startActivityForResult(myInt, 0);
    }

    /* access modifiers changed from: protected */
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        boolean altBool = data.getBooleanExtra("altBool", false);
        if (resultCode == 0 && altBool) {
            Boolean oldPlayAudBool = Boolean.valueOf(this.playAudBool);
            this.playAudBool = data.getBooleanExtra("playAudBool", false);
            this.showPinyinBool = data.getBooleanExtra("showPinyinBool", false);
            this.showEnglishBool = data.getBooleanExtra("showEnglishBool", false);
            this.delayTextBool = data.getBooleanExtra("delayTextBool", false);
            if (this.playAudBool && !oldPlayAudBool.booleanValue()) {
                audClick((View) null);
            }
            setButtons();
            if (this.delayTextBool) {
                this.promptDelay = 1000;
            } else {
                this.promptDelay = 0;
            }
            showPrompt();
        }
    }

    public void timeOut() {
        runOnUiThread(this.timeOut2);
    }

    /* access modifiers changed from: private */
    public void endGame() {
        for (int i = 0; i < 7; i++) {
            this.myButtons[i].setVisibility(4);
        }
        this.delayExitHandler.postDelayed(this.endGameExit, 2000);
    }
}
