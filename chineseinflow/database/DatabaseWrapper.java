package database;

import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import com.gamestolearnenglish.chineseinflow.Item;
import com.gamestolearnenglish.chineseinflow.MetricsLearn;
import java.util.ArrayList;
import java.util.Iterator;

public class DatabaseWrapper {
    private int inden;
    private Context myContext;
    private SQLiteDatabase myDb;
    private DatabaseHelper myHlpr;
    private int tarBaseInd = (this.inden / 30);

    public DatabaseWrapper(Context context, int $db, int $dbInd) {
        this.myContext = context;
        this.inden = $dbInd;
        this.myHlpr = new DatabaseHelper(this.myContext, "churBose_" + $db);
        this.myHlpr.initializeDataBase();
        this.myDb = this.myHlpr.getWritableDatabase();
    }

    public void saveLevels(ArrayList<MetricsLearn.MyPair> $gotPairs) {
        Iterator<MetricsLearn.MyPair> it = $gotPairs.iterator();
        while (it.hasNext()) {
            MetricsLearn.MyPair aPair = it.next();
            ContentValues dataToInsert = new ContentValues();
            dataToInsert.put("level", Integer.valueOf(aPair.returnValue()));
            this.myDb.update("levels", dataToInsert, "_id=" + (aPair.returnKey() + 1 + this.inden), (String[]) null);
        }
    }

    public int[] getLevels() {
        int[] myLevels = new int[30];
        Cursor c = this.myDb.rawQuery("SELECT * FROM 'levels'", (String[]) null);
        c.moveToFirst();
        c.moveToPosition(this.inden);
        for (int i = 0; i < 30; i++) {
            myLevels[i] = c.getInt(1);
            c.moveToNext();
        }
        c.close();
        return myLevels;
    }

    public ArrayList<Item> getItems() {
        ArrayList<Item> myQuests = new ArrayList<>();
        Cursor c = this.myDb.rawQuery("SELECT * FROM 'chars'", (String[]) null);
        c.moveToFirst();
        c.moveToPosition(this.inden);
        for (int i = 0; i < 30; i++) {
            myQuests.add(new Item(c.getString(1), c.getString(2), c.getString(3)));
            c.moveToNext();
        }
        c.close();
        return myQuests;
    }

    public void updateStats(long $countVar, long $passedTime, int $stageVar, int $stageCount) {
        Cursor c = this.myDb.rawQuery("SELECT * FROM 'stats'", (String[]) null);
        c.moveToFirst();
        c.moveToPosition(this.tarBaseInd);
        long gotTotalTime = c.getLong(1);
        ContentValues dataToInsert = new ContentValues();
        dataToInsert.put("total_time", Long.valueOf(gotTotalTime + $passedTime));
        dataToInsert.put("views_count", Long.valueOf(c.getLong(2) + $countVar));
        if ($stageVar != -1) {
            dataToInsert.put("stage_var", Integer.valueOf($stageVar));
        }
        if ($stageCount != -1) {
            dataToInsert.put("stage_count", Integer.valueOf($stageCount));
        }
        this.myDb.update("stats", dataToInsert, "_id=" + (this.tarBaseInd + 1), (String[]) null);
    }

    public int[] getProgress() {
        Cursor c = this.myDb.rawQuery("SELECT * FROM 'stats'", (String[]) null);
        c.moveToFirst();
        c.moveToPosition(this.tarBaseInd);
        return new int[]{c.getInt(3), c.getInt(4)};
    }

    public long[] getStats() {
        Cursor c = this.myDb.rawQuery("SELECT * FROM 'stats'", (String[]) null);
        c.moveToFirst();
        c.moveToPosition(this.tarBaseInd);
        return new long[]{c.getLong(1), c.getLong(2), c.getLong(5), c.getLong(6), c.getLong(7)};
    }

    public int[] getAllStats() {
        int[] returnInts = new int[15];
        Cursor c = this.myDb.rawQuery("SELECT * FROM 'stats'", (String[]) null);
        c.moveToFirst();
        returnInts[0] = c.getInt(5);
        returnInts[1] = c.getInt(6);
        returnInts[2] = c.getInt(7);
        for (int i = 0; i < 4; i++) {
            c.moveToNext();
            returnInts[(i * 3) + 3] = c.getInt(5);
            returnInts[(i * 3) + 4] = c.getInt(6);
            returnInts[(i * 3) + 5] = c.getInt(7);
        }
        return returnInts;
    }

    public void incReviewProgress() {
        Cursor c = this.myDb.rawQuery("SELECT * FROM 'stats'", (String[]) null);
        c.moveToFirst();
        c.moveToPosition(this.tarBaseInd);
        int myProgress = c.getInt(7) + 1;
        if (myProgress > 5) {
            myProgress = 5;
        }
        ContentValues dataToInsert = new ContentValues();
        dataToInsert.put("review_progress", Integer.valueOf(myProgress));
        this.myDb.update("stats", dataToInsert, "_id=" + (this.tarBaseInd + 1), (String[]) null);
    }

    public void incPracticeProgress() {
        Cursor c = this.myDb.rawQuery("SELECT * FROM 'stats'", (String[]) null);
        c.moveToFirst();
        c.moveToPosition(this.tarBaseInd);
        int myProgress = c.getInt(6) + 1;
        if (myProgress > 5) {
            myProgress = 5;
        }
        ContentValues dataToInsert = new ContentValues();
        dataToInsert.put("practice_progress", Integer.valueOf(myProgress));
        this.myDb.update("stats", dataToInsert, "_id=" + (this.tarBaseInd + 1), (String[]) null);
    }

    public void incLearnProgress() {
        Cursor c = this.myDb.rawQuery("SELECT * FROM 'stats'", (String[]) null);
        c.moveToFirst();
        c.moveToPosition(this.tarBaseInd);
        int myProgress = c.getInt(5) + 1;
        if (myProgress > 10) {
            myProgress = 10;
        }
        ContentValues dataToInsert = new ContentValues();
        dataToInsert.put("learn_progress", Integer.valueOf(myProgress));
        this.myDb.update("stats", dataToInsert, "_id=" + (this.tarBaseInd + 1), (String[]) null);
    }

    public void resetData(boolean $all, boolean $learn, boolean $practice, boolean $review) {
        ContentValues dataToInsert2 = new ContentValues();
        if ($learn) {
            for (int i = 0; i < 30; i++) {
                ContentValues dataToInsert = new ContentValues();
                dataToInsert.put("level", 15);
                this.myDb.update("levels", dataToInsert, "_id=" + (this.inden + i + 1), (String[]) null);
            }
            dataToInsert2.put("learn_progress", 0);
            dataToInsert2.put("stage_var", 0);
            dataToInsert2.put("stage_count", 0);
        }
        if ($all) {
            dataToInsert2.put("total_time", 0);
            dataToInsert2.put("views_count", 0);
        }
        if ($practice) {
            dataToInsert2.put("practice_progress", 0);
        }
        if ($review) {
            dataToInsert2.put("review_progress", 0);
        }
        this.myDb.update("stats", dataToInsert2, "_id=" + ((this.inden / 30) + 1), (String[]) null);
    }

    public void closeDatabase() {
        this.myHlpr.close();
        this.myDb.close();
    }
}
