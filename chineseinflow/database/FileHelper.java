package database;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.FileReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.io.Reader;
import java.nio.channels.FileChannel;

public class FileHelper {
    public static void copyFile(InputStream fromFile, OutputStream toFile) throws IOException {
        byte[] buffer = new byte[1024];
        while (true) {
            try {
                int length = fromFile.read(buffer);
                if (length <= 0) {
                    break;
                }
                toFile.write(buffer, 0, length);
            } catch (Throwable th) {
                if (fromFile != null) {
                    fromFile.close();
                }
                throw th;
            }
        }
        if (toFile != null) {
            try {
                toFile.flush();
                toFile.close();
            } catch (Throwable th2) {
                if (fromFile != null) {
                    fromFile.close();
                }
                throw th2;
            }
        }
        if (fromFile != null) {
            fromFile.close();
        }
    }

    public static void copyFile(String fromFile, String toFile) throws IOException {
        copyFile(new FileInputStream(fromFile), new FileOutputStream(toFile));
    }

    public static void copyFile(File fromFile, File toFile) throws IOException {
        copyFile(new FileInputStream(fromFile), new FileOutputStream(toFile));
    }

    public static void copyFile(FileInputStream fromFile, FileOutputStream toFile) throws IOException {
        FileChannel fromChannel = fromFile.getChannel();
        FileChannel toChannel = toFile.getChannel();
        try {
            fromChannel.transferTo(0, fromChannel.size(), toChannel);
            if (fromChannel != null) {
                try {
                    fromChannel.close();
                } catch (Throwable th) {
                    if (toChannel != null) {
                        toChannel.close();
                    }
                    throw th;
                }
            }
            if (toChannel != null) {
                toChannel.close();
            }
        } catch (Throwable th2) {
            if (toChannel != null) {
                toChannel.close();
            }
            throw th2;
        }
    }

    public static String[] parseSqlFile(String sqlFile) throws IOException {
        return parseSqlFile(new BufferedReader(new FileReader(sqlFile)));
    }

    public static String[] parseSqlFile(InputStream sqlFile) throws IOException {
        return parseSqlFile(new BufferedReader(new InputStreamReader(sqlFile)));
    }

    public static String[] parseSqlFile(Reader sqlFile) throws IOException {
        return parseSqlFile(new BufferedReader(sqlFile));
    }

    public static String[] parseSqlFile(BufferedReader sqlFile) throws IOException {
        StringBuilder sql = new StringBuilder();
        String multiLineComment = null;
        while (true) {
            String line = sqlFile.readLine();
            if (line != null) {
                String line2 = line.trim();
                if (multiLineComment == null) {
                    if (line2.startsWith("/*")) {
                        if (!line2.endsWith("}")) {
                            multiLineComment = "/*";
                        }
                    } else if (line2.startsWith("{")) {
                        if (!line2.endsWith("}")) {
                            multiLineComment = "{";
                        }
                    } else if (!line2.startsWith("--") && !line2.equals("")) {
                        sql.append(line2);
                    }
                } else if (multiLineComment.equals("/*")) {
                    if (line2.endsWith("*/")) {
                        multiLineComment = null;
                    }
                } else if (multiLineComment.equals("{") && line2.endsWith("}")) {
                    multiLineComment = null;
                }
            } else {
                sqlFile.close();
                return sql.toString().split(";");
            }
        }
    }
}
