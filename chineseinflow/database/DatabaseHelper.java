package database;

import android.content.Context;
import android.database.sqlite.SQLiteDatabase;
import android.database.sqlite.SQLiteOpenHelper;
import com.gamestolearnenglish.chineseinflow.R;
import java.io.FileOutputStream;
import java.io.IOException;

public class DatabaseHelper extends SQLiteOpenHelper {
    private static String DB_DIR = "/data/data/chineseinflow/databases/";
    private static String DB_NAME = "";
    private static String DB_PATH = (DB_DIR + DB_NAME);
    private static String OLD_DB_PATH = (DB_DIR + "old_" + DB_NAME);
    private boolean createDatabase = false;
    private final Context myContext;
    private boolean upgradeDatabase = false;

    public DatabaseHelper(Context context, String $dbName) {
        super(context, $dbName, (SQLiteDatabase.CursorFactory) null, context.getResources().getInteger(R.integer.databaseVersion));
        DB_NAME = $dbName;
        DB_PATH = DB_DIR + DB_NAME;
        OLD_DB_PATH = DB_DIR + "old_" + DB_NAME;
        this.myContext = context;
        DB_PATH = this.myContext.getDatabasePath(DB_NAME).getAbsolutePath();
    }

    public void initializeDataBase() {
        getWritableDatabase();
        if (this.createDatabase) {
            try {
                copyDataBase();
            } catch (IOException e) {
                throw new Error("Error copying database");
            }
        } else if (this.upgradeDatabase) {
            try {
                FileHelper.copyFile(DB_PATH, OLD_DB_PATH);
                copyDataBase();
                SQLiteDatabase openDatabase = SQLiteDatabase.openDatabase(OLD_DB_PATH, (SQLiteDatabase.CursorFactory) null, 0);
                SQLiteDatabase.openDatabase(DB_PATH, (SQLiteDatabase.CursorFactory) null, 0);
            } catch (IOException e2) {
                throw new Error("Error copying database");
            }
        }
    }

    private void copyDataBase() throws IOException {
        close();
        FileHelper.copyFile(this.myContext.getAssets().open(DB_NAME), new FileOutputStream(DB_PATH));
        getWritableDatabase().close();
    }

    public void onCreate(SQLiteDatabase db) {
        this.createDatabase = true;
    }

    public void onUpgrade(SQLiteDatabase db, int oldVersion, int newVersion) {
        this.upgradeDatabase = true;
    }

    public void onOpen(SQLiteDatabase db) {
        super.onOpen(db);
    }
}
