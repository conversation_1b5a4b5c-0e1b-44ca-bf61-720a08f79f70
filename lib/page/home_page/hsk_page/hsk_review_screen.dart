import 'dart:async';
import 'dart:math';

import 'package:dasso_reader/l10n/generated/L10n.dart';
import 'package:dasso_reader/models/hsk_character.dart';
import 'package:dasso_reader/models/hsk_character_set.dart';
import 'package:dasso_reader/models/hsk_settings.dart';
import 'package:dasso_reader/page/home_page/hsk_page/hsk_time_over_screen.dart';
import 'package:dasso_reader/providers/hsk_providers.dart';
import 'package:dasso_reader/config/typography.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:audioplayers/audioplayers.dart';
import 'package:dasso_reader/widgets/responsive_button_text.dart';

/// A screen that provides a review experience for HSK characters.
///
/// It shows characters one by one, allowing the user to reveal their pinyin and English
/// translations, and mark whether they knew the character or need to review it again.
class HskReviewScreen extends ConsumerStatefulWidget {
  final HskCharacterSet characterSet;

  const HskReviewScreen({
    super.key,
    required this.characterSet,
  });

  @override
  ConsumerState<HskReviewScreen> createState() => _HskReviewScreenState();
}

class _HskReviewScreenState extends ConsumerState<HskReviewScreen> {
  // Character state
  List<HskCharacter> _itemOrderList = [];
  late HskCharacter _currentCharacter;
  int _currentIndex = 0;
  int _repeatsCount = 0;
  int _viewsCount = 0;
  late int _totalCards;

  // UI state
  bool _showingAnswer = false;
  bool _autoPlaySound = false;
  bool _showPinyin = true;
  bool _showEnglish = true;

  // Audio
  final AudioPlayer _audioPlayer = AudioPlayer();

  // Audio error handling (like HskLearnScreen)
  bool _audioError = false;
  String _audioErrorMsg = "";

  // Start time for tracking duration
  late DateTime _startTime;

  // --- UI Constants for Responsive Design ---
  static const double _kCharacterDisplaySize =
      72.0; // Increased for better visibility in review mode
  static const double _kPinyinFontSize = 28.0; // Larger for review mode
  static const double _kEnglishFontSize = 24.0; // Larger for review mode

  // Container constraints
  static const double _kCharacterContainerMinHeight =
      140.0; // Increased for larger text
  static const double _kCharacterContainerMaxHeight =
      200.0; // Increased for larger text
  static const double _kAnswerContainerMinHeight =
      120.0; // Increased for larger text
  static const double _kAnswerContainerMaxHeight =
      220.0; // Increased for larger text

  // Button dimensions
  static const double _kButtonHeight = 60.0;
  static const double _kButtonFontSize = 18.0;
  static const double _kButtonBorderRadius = 12.0;

  // Spacing and padding
  static const EdgeInsets _kContainerPadding = EdgeInsets.symmetric(
    vertical: 20.0, // Reduced from 24.0
    horizontal: 16.0,
  );
  static const EdgeInsets _kMainPadding = EdgeInsets.all(16.0);
  static const double _kMainSpacing = 24.0;
  static const double _kButtonSpacing = 12.0;

  @override
  void initState() {
    super.initState();
    _startTime = DateTime.now();

    // Initialize characters in random order
    _initializeCharacters();

    // Start the session
    Future.microtask(() {
      ref.read(hskSessionProgressProvider.notifier).startLearnSession(
            _totalCards,
          );
    });

    // Start first round
    _startNewRound();
  }

  void _loadSettings() async {
    try {
      // Create a default settings instance to use if needed
      const defaultSettings = HskReviewSettings(
        autoPlaySound: false,
        showPinyin: true,
        showEnglish: true,
      );

      // Try to get settings from the provider
      final settings =
          await ref.read(hskReviewSettingsProvider.future).catchError((e) {
        debugPrint('Error loading review settings: $e');
        return defaultSettings;
      });

      setState(() {
        _autoPlaySound = settings.autoPlaySound;
        _showPinyin = settings.showPinyin;
        _showEnglish = settings.showEnglish;
      });
    } catch (e) {
      debugPrint('Error in _loadSettings: $e');
      // Use defaults if there's any error
      setState(() {
        _autoPlaySound = false;
        _showPinyin = true;
        _showEnglish = true;
      });
    }
  }

  void _initializeCharacters() {
    // Get characters from the set
    List<HskCharacter> characters = widget.characterSet.characters.toList();

    // Shuffle the characters for random order
    characters.shuffle();

    // Limit to 30 cards maximum, like in the Java version
    _totalCards = min(characters.length, 30);
    _itemOrderList = characters.take(_totalCards).toList();

    // Set the first character
    _currentCharacter = _itemOrderList[0];
  }

  @override
  void dispose() {
    _audioPlayer.dispose();

    // End the session and update total view time
    Future.microtask(() {
      ref.read(hskSessionProgressProvider.notifier).endSession();

      // Update character view counts
      ref
          .read(characterProgressProvider.notifier)
          .updateCharacter(_currentCharacter.copyWith(
            viewCount: _currentCharacter.viewCount + _viewsCount,
          ));
    });

    super.dispose();
  }

  void _startNewRound() {
    setState(() {
      _currentCharacter = _itemOrderList[_currentIndex];
      _showingAnswer = false;
    });

    if (_autoPlaySound) {
      _playAudio();
    }
  }

  void _showAnswer() {
    setState(() {
      _showingAnswer = true;
      _viewsCount++;
    });
  }

  void _handleGoodPress() {
    if (_showingAnswer) {
      // Go to next card
      _goToNextCard();
    } else {
      // Reveal the answer
      _showAnswer();
    }
  }

  void _handleAgainPress() {
    // Move this card 5 positions later in the deck
    _moveCardLater();
    _repeatsCount++;
    _goToNextCard();
  }

  void _goToNextCard() {
    _currentIndex++;

    if (_currentIndex >= _totalCards) {
      // End the review session
      _navigateToCompletionScreen();
    } else {
      // Show the next card
      _startNewRound();
    }
  }

  void _moveCardLater() {
    // Similar to the Java 'proder' method
    int cardsToMove = 5;

    // Handle the case when we're near the end of the deck
    if (_currentIndex >= _totalCards - 5) {
      cardsToMove = _totalCards - _currentIndex - 1;
    }

    if (cardsToMove <= 0) return;

    // Store the current character
    HskCharacter currentCard = _currentCharacter;

    // Move cards forward
    for (int i = _currentIndex; i < _currentIndex + cardsToMove; i++) {
      _itemOrderList[i] = _itemOrderList[i + 1];
    }

    // Put the current card at the new position
    _itemOrderList[_currentIndex + cardsToMove] = currentCard;

    // Decrement the index since we'll increment it in goToNextCard
    _currentIndex--;
  }

  void _navigateToCompletionScreen() {
    if (!mounted) return;

    // Calculate session duration
    final sessionDuration = ref
        .read(hskSessionProgressProvider.notifier)
        .getSessionDurationSeconds();

    // Navigate to the completion screen (now using our custom screen)
    Navigator.pushReplacement(
      context,
      MaterialPageRoute(
        builder: (context) => HskReviewCompleteScreen(
          characterSet: widget.characterSet,
          repeats: _repeatsCount,
          duration: sessionDuration,
        ),
      ),
    );
  }

  void _playAudio() async {
    try {
      // Reset error state
      setState(() {
        _audioError = false;
        _audioErrorMsg = "";
      });

      // Stop any current playback
      await _audioPlayer.stop();

      // Get the audio path
      final audioPath = _currentCharacter.audioAssetPath;

      // Set volume to max
      await _audioPlayer.setVolume(1.0);

      // Play audio with asset source
      await _audioPlayer.play(AssetSource(audioPath));
    } catch (e) {
      // Try alternate path format as fallback
      try {
        final hskNum =
            _currentCharacter.hskLevel.replaceAll(RegExp(r'[^0-9]'), '');
        final fallbackPath =
            'audio/hsk$hskNum/${_currentCharacter.characterId}.mp3';

        await _audioPlayer.play(AssetSource(fallbackPath));
      } catch (fallbackError) {
        // Set error state to show visual indicator
        setState(() {
          _audioError = true;
          _audioErrorMsg =
              'Audio not available for ${_currentCharacter.character}';
        });

        debugPrint(
            'Error playing audio for character: ${_currentCharacter.character}, '
            'Path: ${_currentCharacter.audioAssetPath}');
      }
    }
  }

  void _showSettingsDialog(BuildContext context) async {
    try {
      // Create default settings for use if needed
      const defaultSettings = HskReviewSettings(
        autoPlaySound: false,
        showPinyin: true,
        showEnglish: true,
      );

      final currentSettings =
          await ref.read(hskReviewSettingsProvider.future).catchError((e) {
        debugPrint('Error in _showSettingsDialog: $e');
        return defaultSettings;
      });

      // ignore: use_build_context_synchronously
      showDialog(
        context: context,
        builder: (context) => ReviewSettingsDialog(
          initialSettings: currentSettings,
          onSettingsChanged: (newSettings) {
            // Update local state immediately (the provider might fail)
            setState(() {
              _autoPlaySound = newSettings.autoPlaySound;
              _showPinyin = newSettings.showPinyin;
              _showEnglish = newSettings.showEnglish;
            });

            // Try to save to provider (but continue even if it fails)
            try {
              // Play audio if setting was just enabled
              if (_autoPlaySound && !currentSettings.autoPlaySound) {
                _playAudio();
              }
            } catch (e) {
              debugPrint('Error updating settings: $e');
            }
          },
        ),
      );
    } catch (e) {
      debugPrint('Error showing settings dialog: $e');

      // Show a simple dialog if the proper settings dialog fails
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text("Settings"),
          content: const Text("Something went wrong loading settings."),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text("OK"),
            ),
          ],
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          "Review Mode",
          style: TextStyle(color: Colors.white),
        ),
        backgroundColor: Colors.blue.shade900,
        iconTheme: const IconThemeData(color: Colors.white),
        actions: [
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () => _showSettingsDialog(context),
          ),
        ],
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.blue.shade900,
              Colors.teal.shade700,
            ],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // Progress indicator
              Padding(
                padding: const EdgeInsets.all(8.0),
                child: Text(
                  "${_currentIndex + 1}/$_totalCards",
                  style: const TextStyle(
                    color: Colors.white70,
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
              ),

              // Main content with responsive layout
              Expanded(
                child: Padding(
                  padding: _kMainPadding,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      // Character card with responsive sizing
                      Container(
                        width: double.infinity,
                        constraints: const BoxConstraints(
                          minHeight: _kCharacterContainerMinHeight,
                          maxHeight: _kCharacterContainerMaxHeight,
                        ),
                        padding: _kContainerPadding,
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.15),
                          borderRadius: BorderRadius.circular(16.0),
                        ),
                        clipBehavior: Clip
                            .hardEdge, // Ensure content is clipped to bounds
                        child: Center(
                          child: ResponsiveButtonText(
                            character: _currentCharacter.character,
                            characterFontSize: _kCharacterDisplaySize,
                            maxWidth: MediaQuery.of(context).size.width * 0.8,
                            maxHeight: _kCharacterContainerMaxHeight -
                                (_kContainerPadding.vertical * 2),
                          ),
                        ),
                      ),

                      const SizedBox(height: _kMainSpacing),

                      // Answer area with responsive container
                      Container(
                        width: double.infinity,
                        constraints: const BoxConstraints(
                          minHeight: _kAnswerContainerMinHeight,
                          maxHeight: _kAnswerContainerMaxHeight,
                        ),
                        padding: _kContainerPadding,
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.15),
                          borderRadius: BorderRadius.circular(16.0),
                        ),
                        clipBehavior: Clip
                            .hardEdge, // Ensure content is clipped to bounds
                        child: Center(
                          child: _showingAnswer
                              ? _buildAnswerContent()
                              : const SizedBox(), // Empty but maintains container size
                        ),
                      ),

                      const Spacer(),

                      // Action buttons with improved responsive layout
                      _buildActionButtons(),
                    ],
                  ),
                ),
              ),

              // Audio button positioned at bottom center (matching HskLearnScreen)
              Align(
                alignment: Alignment.bottomCenter,
                child: Padding(
                  padding: const EdgeInsets.only(right: 12.0, bottom: 16.0),
                  child: _buildAudioButton(),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Builds the answer content with responsive text handling
  Widget _buildAnswerContent() {
    // Use the responsive widget for consistent text handling across all screens
    return ResponsiveButtonText(
      pinyin: _currentCharacter.pinyin,
      english: _currentCharacter.englishTranslation,
      showPinyin: _showPinyin,
      showEnglish: _showEnglish,
      pinyinFontSize: _kPinyinFontSize,
      englishFontSize: _kEnglishFontSize,
      maxEnglishLines:
          3, // Allow more lines in review mode for better readability
      maxWidth: MediaQuery.of(context).size.width * 0.8, // 80% of screen width
      maxHeight: _kAnswerContainerMaxHeight - (_kContainerPadding.vertical * 2),
    );
  }

  /// Builds the action buttons with responsive layout
  Widget _buildActionButtons() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: _showingAnswer
          ? Row(
              children: [
                // Good button
                Expanded(
                  child: _buildActionButton(
                    text: "Good",
                    color: Colors.orange.shade700,
                    onPressed: _handleGoodPress,
                  ),
                ),
                const SizedBox(width: _kButtonSpacing),
                // Again button
                Expanded(
                  child: _buildActionButton(
                    text: "Again",
                    color: Colors.grey.shade800,
                    onPressed: _handleAgainPress,
                  ),
                ),
              ],
            )
          : _buildActionButton(
              text: "Show",
              color: Colors.orange.shade700,
              onPressed: _handleGoodPress,
              isFullWidth: true,
            ),
    );
  }

  // Helper to build consistent responsive buttons
  Widget _buildActionButton({
    required String text,
    required Color color,
    required VoidCallback onPressed,
    bool isFullWidth = false,
  }) {
    return SizedBox(
      width: isFullWidth ? double.infinity : null,
      height: _kButtonHeight,
      child: ElevatedButton(
        onPressed: onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: color,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 16,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(_kButtonBorderRadius),
          ),
        ),
        child: FittedBox(
          fit: BoxFit.scaleDown,
          child: Text(
            text,
            style: const TextStyle(
              fontSize: _kButtonFontSize,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ),
    );
  }

  /// Builds the floating action button for replaying audio (matching HskLearnScreen design)
  Widget _buildAudioButton() {
    return Container(
      margin: const EdgeInsets.only(bottom: 4.0),
      child: Stack(
        alignment: Alignment.center,
        children: [
          // Main audio button
          Material(
            color: Colors.blue.shade800,
            shape: const CircleBorder(),
            elevation: 3.0,
            child: InkWell(
              onTap: _playAudio,
              customBorder: const CircleBorder(),
              child: Padding(
                padding: const EdgeInsets.all(8.0), // Reduced padding
                child: Icon(
                  Icons.volume_up,
                  color: _audioError ? Colors.red.shade200 : Colors.white,
                  size: 28, // Slightly smaller
                ),
              ),
            ),
          ),

          // Subtle error indicator
          if (_audioError)
            Positioned(
              right: 0,
              bottom: 0,
              child: Container(
                width: 12, // Slightly smaller
                height: 12, // Slightly smaller
                decoration: BoxDecoration(
                  color: Colors.red.shade400,
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.close,
                  size: 8, // Slightly smaller
                  color: Colors.white,
                ),
              ),
            ),
        ],
      ),
    );
  }
}

/// Dialog for configuring HSK review mode settings.
///
/// Allows users to toggle options such as auto-playing audio,
/// visibility of pinyin and English translations.
class ReviewSettingsDialog extends StatefulWidget {
  final HskReviewSettings initialSettings;
  final void Function(HskReviewSettings) onSettingsChanged;

  const ReviewSettingsDialog({
    super.key,
    required this.initialSettings,
    required this.onSettingsChanged,
  });

  @override
  State<ReviewSettingsDialog> createState() => _ReviewSettingsDialogState();
}

class _ReviewSettingsDialogState extends State<ReviewSettingsDialog> {
  late bool _autoPlaySound;
  late bool _showPinyin;
  late bool _showEnglish;

  @override
  void initState() {
    super.initState();
    _autoPlaySound = widget.initialSettings.autoPlaySound;
    _showPinyin = widget.initialSettings.showPinyin;
    _showEnglish = widget.initialSettings.showEnglish;
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text("Review Mode Settings"),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            CheckboxListTile(
              title: const Text("Auto Play Sound"),
              value: _autoPlaySound,
              onChanged: (value) {
                setState(() {
                  _autoPlaySound = value ?? false;
                });
              },
            ),
            CheckboxListTile(
              title: const Text("Show Pinyin"),
              value: _showPinyin,
              onChanged: (value) {
                setState(() {
                  _showPinyin = value ?? true;
                });
              },
            ),
            CheckboxListTile(
              title: const Text("Show English"),
              value: _showEnglish,
              onChanged: (value) {
                setState(() {
                  _showEnglish = value ?? true;
                });
              },
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () {
            Navigator.of(context).pop();
          },
          child: const Text("CANCEL"),
        ),
        TextButton(
          onPressed: () {
            final newSettings = HskReviewSettings(
              autoPlaySound: _autoPlaySound,
              showPinyin: _showPinyin,
              showEnglish: _showEnglish,
            );
            widget.onSettingsChanged(newSettings);
            Navigator.of(context).pop();
          },
          child: const Text("OKAY"),
        ),
      ],
    );
  }
}

// Add a dedicated Review completion screen
class HskReviewCompleteScreen extends StatelessWidget {
  final HskCharacterSet characterSet;
  final int repeats;
  final int duration;

  // --- UI Constants for Responsive Design ---
  static const double _kTitleFontSize = 36.0;
  static const double _kMessageFontSize = 18.0;
  static const double _kStatsFontSize = 28.0;
  static const double _kButtonFontSize = 18.0;
  static const double _kButtonHeight = 60.0;
  static const double _kButtonBorderRadius = 12.0;
  static const double _kMainSpacing = 24.0;
  static const double _kStatsSpacing = 16.0;
  static const double _kButtonSpacing = 12.0;
  static const EdgeInsets _kMainPadding = EdgeInsets.all(24.0);
  static const EdgeInsets _kButtonPadding = EdgeInsets.symmetric(
    horizontal: 16,
    vertical: 16,
  );

  const HskReviewCompleteScreen({
    super.key,
    required this.characterSet,
    required this.repeats,
    required this.duration,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        width: double.infinity,
        height: double.infinity,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.blue.shade900,
              Colors.teal.shade700,
            ],
          ),
        ),
        child: SafeArea(
          child: Padding(
            padding: _kMainPadding,
            child: LayoutBuilder(
              builder: (context, constraints) {
                return Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Spacer(flex: 1),

                    // Title with responsive sizing
                    FittedBox(
                      fit: BoxFit.scaleDown,
                      child: Text(
                        "Review Completed",
                        style: HskTypography.withSize(
                          HskTypography.completionTitle,
                          _kTitleFontSize,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),

                    const SizedBox(height: _kMainSpacing),

                    // Message with responsive container
                    Container(
                      constraints: BoxConstraints(
                        maxWidth: constraints.maxWidth * 0.9,
                      ),
                      child: Text(
                        "Well done. You have successfully reviewed this set of characters. Tap the return button to go back to the main menu or tap the again button to review again.",
                        textAlign: TextAlign.center,
                        style: HskTypography.withSize(
                          HskTypography.completionDescription,
                          _kMessageFontSize,
                        ),
                        maxLines: 4,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),

                    const Spacer(flex: 1),

                    // Statistics with responsive layout
                    Container(
                      constraints: BoxConstraints(
                        maxWidth: constraints.maxWidth * 0.8,
                      ),
                      child: Column(
                        children: [
                          // Statistics - Repeats
                          FittedBox(
                            fit: BoxFit.scaleDown,
                            child: Text(
                              "Repeats $repeats",
                              style: HskTypography.withSize(
                                HskTypography.completionStats,
                                _kStatsFontSize,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ),

                          const SizedBox(height: _kStatsSpacing),

                          // Statistics - Time spent
                          FittedBox(
                            fit: BoxFit.scaleDown,
                            child: Text(
                              "Time spent ${_formatTime(duration)}",
                              style: HskTypography.withSize(
                                HskTypography.completionStats,
                                _kStatsFontSize,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ),
                        ],
                      ),
                    ),

                    const Spacer(flex: 2),

                    // Buttons with improved responsive layout
                    Container(
                      constraints: BoxConstraints(
                        maxWidth: constraints.maxWidth * 0.9,
                      ),
                      child: Row(
                        children: [
                          // Return button
                          Expanded(
                            child: _buildActionButton(
                              text: "Return",
                              color: Colors.grey.shade800,
                              onPressed: () {
                                // Pop back to HskSetDetailsScreen (consistent with Learn mode)
                                Navigator.pop(context);
                              },
                            ),
                          ),

                          const SizedBox(width: _kButtonSpacing),

                          // Again button
                          Expanded(
                            child: _buildActionButton(
                              text: "Again",
                              color: Colors.orange.shade700,
                              onPressed: () {
                                Navigator.pushReplacement(
                                  context,
                                  MaterialPageRoute(
                                    builder: (context) => HskReviewScreen(
                                      characterSet: characterSet,
                                    ),
                                  ),
                                );
                              },
                            ),
                          ),
                        ],
                      ),
                    ),

                    const Spacer(flex: 1),

                    // Mountain silhouette with responsive sizing
                    Container(
                      height: min(100, constraints.maxHeight * 0.12),
                      width: double.infinity,
                      alignment: Alignment.bottomCenter,
                      child: CustomPaint(
                        size: Size(double.infinity,
                            min(80, constraints.maxHeight * 0.1)),
                        painter: MountainPainter(),
                      ),
                    ),
                  ],
                );
              },
            ),
          ),
        ),
      ),
    );
  }

  /// Builds consistent responsive action buttons
  Widget _buildActionButton({
    required String text,
    required Color color,
    required VoidCallback onPressed,
  }) {
    return SizedBox(
      height: _kButtonHeight,
      child: ElevatedButton(
        onPressed: onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: color,
          foregroundColor: Colors.white,
          padding: _kButtonPadding,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(_kButtonBorderRadius),
          ),
        ),
        child: FittedBox(
          fit: BoxFit.scaleDown,
          child: Text(
            text,
            style: const TextStyle(
              fontSize: _kButtonFontSize,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ),
    );
  }

  String _formatTime(int seconds) {
    if (seconds < 60) {
      return "${seconds}s";
    } else if (seconds < 3600) {
      final minutes = seconds ~/ 60;
      final remainingSeconds = seconds % 60;
      return "$minutes min ${remainingSeconds > 0 ? '$remainingSeconds sec' : ''}";
    } else {
      final hours = seconds ~/ 3600;
      final minutes = (seconds % 3600) ~/ 60;
      return "$hours h ${minutes > 0 ? '$minutes min' : ''}";
    }
  }
}

// Mountain painter (copied from existing implementation)
class MountainPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.white.withOpacity(0.2)
      ..style = PaintingStyle.fill;

    final path = Path();

    // Start at bottom left
    path.moveTo(0, size.height);

    // First mountain
    path.lineTo(size.width * 0.2, size.height * 0.4);

    // Second mountain
    path.lineTo(size.width * 0.35, size.height * 0.7);

    // Third mountain
    path.lineTo(size.width * 0.5, size.height * 0.2);

    // Fourth mountain
    path.lineTo(size.width * 0.7, size.height * 0.6);

    // Fifth mountain
    path.lineTo(size.width * 0.85, size.height * 0.3);

    // End at bottom right
    path.lineTo(size.width, size.height);

    // Close the path
    path.close();

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
