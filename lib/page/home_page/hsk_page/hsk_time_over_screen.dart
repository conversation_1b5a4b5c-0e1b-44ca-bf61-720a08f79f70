import 'package:dasso_reader/l10n/generated/L10n.dart';
import 'package:dasso_reader/models/hsk_character_set.dart';
import 'package:dasso_reader/page/home_page/hsk_page/hsk_practice_screen.dart';
import 'package:dasso_reader/providers/hsk_providers.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class HskTimeOverScreen extends ConsumerStatefulWidget {
  final HskCharacterSet characterSet;
  final int correctAnswers;
  final int maxProgressPoints;
  final bool isTimeout;
  final bool endedDueToSuddenDeathMiss;
  final int actualProgressPoints;
  final int totalRoundsPlayed;
  final bool fullyCompletedSuccessfully;

  const HskTimeOverScreen({
    super.key,
    required this.characterSet,
    required this.correctAnswers,
    required this.maxProgressPoints,
    required this.isTimeout,
    required this.endedDueToSuddenDeathMiss,
    required this.actualProgressPoints,
    required this.totalRoundsPlayed,
    required this.fullyCompletedSuccessfully,
  });

  @override
  ConsumerState<HskTimeOverScreen> createState() => _HskTimeOverScreenState();
}

class _HskTimeOverScreenState extends ConsumerState<HskTimeOverScreen> {
  int _sessionDurationSeconds = 0;
  bool _initialized = false;

  @override
  void initState() {
    super.initState();
    // Schedule getting the duration after build
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // Use a delayed call to prevent modifying during build
      Future.microtask(() {
        if (mounted) {
          final duration = ref
              .read(hskSessionProgressProvider.notifier)
              .getSessionDurationSeconds();
          setState(() {
            _sessionDurationSeconds = duration;
            _initialized = true;
          });
        }
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    // Simplified to only two screens as per requirements
    final String titleText =
        widget.isTimeout ? "Time Over!" : "Practice Complete";

    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.blue.shade900,
              Colors.teal.shade700,
            ],
          ),
        ),
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(24.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Title
                Text(
                  titleText,
                  style: const TextStyle(
                    fontSize: 32,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                    shadows: [
                      Shadow(
                        offset: Offset(1, 1),
                        blurRadius: 3.0,
                        color: Color.fromARGB(150, 0, 0, 0),
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 48),

                // Simplified statistics as per requirements
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 24,
                    vertical: 16,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.black26,
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Column(
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const Text(
                            "Completed:",
                            style: TextStyle(color: Colors.white70),
                          ),
                          Text(
                            "${widget.actualProgressPoints}/60",
                            style: const TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const Text(
                            "Time spent:",
                            style: TextStyle(color: Colors.white70),
                          ),
                          Text(
                            _formatTime(_sessionDurationSeconds),
                            style: const TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 64),

                // Buttons - unchanged
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    ElevatedButton(
                      onPressed: () {
                        // Pop back to HskSetDetailsScreen (consistent with Learn mode)
                        Navigator.pop(context);
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.grey.shade800,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(
                          horizontal: 32,
                          vertical: 16,
                        ),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: const Text(
                        "Return",
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    ElevatedButton(
                      onPressed: () {
                        Navigator.pushReplacement(
                          context,
                          MaterialPageRoute(
                            builder: (context) => HskPracticeScreen(
                              characterSet: widget.characterSet,
                            ),
                          ),
                        );
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.orange.shade700,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(
                          horizontal: 32,
                          vertical: 16,
                        ),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: const Text(
                        "Again",
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),

                const Spacer(),

                // Mountain silhouette decoration (same as before)
                Container(
                  height: 100,
                  width: double.infinity,
                  alignment: Alignment.bottomCenter,
                  child: CustomPaint(
                    size: const Size(double.infinity, 80),
                    painter: MountainPainter(),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  String _formatTime(int seconds) {
    if (seconds < 60) {
      return "$seconds sec";
    } else if (seconds < 3600) {
      final minutes = seconds ~/ 60;
      final remainingSeconds = seconds % 60;
      return "$minutes min ${remainingSeconds > 0 ? '$remainingSeconds sec' : ''}";
    } else {
      final hours = seconds ~/ 3600;
      final minutes = (seconds % 3600) ~/ 60;
      return "$hours h ${minutes > 0 ? '$minutes min' : ''}";
    }
  }
}

// Simple painter to create a mountain silhouette effect (same as in other screens)
class MountainPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.white.withOpacity(0.2)
      ..style = PaintingStyle.fill;

    final path = Path();

    // Start at bottom left
    path.moveTo(0, size.height);

    // First mountain
    path.lineTo(size.width * 0.2, size.height * 0.4);

    // Second mountain
    path.lineTo(size.width * 0.35, size.height * 0.7);

    // Third mountain
    path.lineTo(size.width * 0.5, size.height * 0.2);

    // Fourth mountain
    path.lineTo(size.width * 0.7, size.height * 0.6);

    // Fifth mountain
    path.lineTo(size.width * 0.85, size.height * 0.3);

    // End at bottom right
    path.lineTo(size.width, size.height);

    // Close the path
    path.close();

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
