import 'dart:async';
import 'dart:math';

import 'package:dasso_reader/models/hsk_character.dart';
import 'package:dasso_reader/models/hsk_character_set.dart';
import 'package:dasso_reader/models/hsk_settings.dart';
import 'package:dasso_reader/page/home_page/hsk_page/hsk_time_over_screen.dart';
import 'package:dasso_reader/providers/hsk_providers.dart';
import 'package:dasso_reader/services/java_practice_adapter.dart';
import 'package:dasso_reader/config/typography.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:audioplayers/audioplayers.dart';
import 'package:dasso_reader/widgets/responsive_button_text.dart';

class HskPracticeScreen extends ConsumerStatefulWidget {
  final HskCharacterSet characterSet;

  const HskPracticeScreen({
    super.key,
    required this.characterSet,
  });

  @override
  ConsumerState<HskPracticeScreen> createState() => _HskPracticeScreenState();
}

class _HskPracticeScreenState extends ConsumerState<HskPracticeScreen> {
  JavaPracticeRound? _currentRound;
  late int _correctAnswersInSession;
  List<bool> _buttonVisibleState = [];

  Timer? _questionTimer;
  double _questionTimerValue = 1.0;
  double _questionTimerStartValue = 1.0;
  bool _showingFeedback = false;
  bool _isShowingWellDoneMessage = false;
  int _selectedAnswerIndex =
      -1; // -1: no selection, -2: timeout, -3: sudden death miss, 0+: actual button index
  bool _isCorrect = false;
  final AudioPlayer _audioPlayer = AudioPlayer();

  bool _isAnnouncingSuddenDeath = false;
  Timer? _sdAnnouncementTimer;
  String? _currentSuddenDeathSubMessage;
  Timer? _subMessageTimer;

  // --- Java-Style Timer Acceleration Variables ---
  double _timerSpeed =
      0.8; // Initial speed (Java's timerInc), units per 100ms equivalent
  static const double _initialTimerSpeed = 0.8;
  static const double _normalModeSpeedIncrement =
      0.05; // +0.05 per correct answer in normal mode
  static const double _suddenDeathSpeedIncrement =
      0.2; // +0.2 per correct answer in sudden death
  static const double _maxNormalSpeed = 2.0; // Maximum speed in normal mode
  static const double _maxSuddenDeathSpeed =
      3.4; // Maximum speed in sudden death mode

  // --- Java-Style Delayed Text Prompt Variables ---
  Timer? _delayedTextTimer;
  bool _showDelayedText = false;

  static const int _requiredCharactersForAdapter = 30;

  // --- UI Constants for better maintainability ---
  static const double _kMinButtonWidth = 90.0;
  static const double _kMaxButtonWidth = 150.0;
  static const double _kMinButtonHeight = 60.0;
  static const double _kMaxButtonHeight = 100.0;

  // Further reduced font sizes to ensure pinyin fits on all devices
  static const double _kBaseFontSizeMultiplier =
      0.75; // Further reduced from 0.9
  static const double _kCharFontSize =
      26.0 * _kBaseFontSizeMultiplier; // Further reduced
  static const double _kButtonBorderRadius = 16.0;
  static const EdgeInsets _kButtonPadding =
      EdgeInsets.symmetric(horizontal: 4.0, vertical: 3.0);
  static const EdgeInsets _kButtonContentPadding = EdgeInsets.symmetric(
      horizontal: 6.0, vertical: 4.0); // Reduced vertical padding

  // --- Animation Durations ---
  static const Duration _kButtonPulseDuration = Duration(milliseconds: 150);

  // --- Prompt Area Constants ---
  static const double _kPromptMinHeight = 80.0;
  static const double _kPromptMaxHeight = 130.0;
  static const double _kPromptBorderRadius = 16.0;

  @override
  void initState() {
    super.initState();
    _correctAnswersInSession = 0;

    // Initialize timer speed to Java default (0.8 units per 100ms)
    _timerSpeed = _initialTimerSpeed;

    List<HskCharacter> practiceCharacters = _prepareCharactersForAdapter();

    if (practiceCharacters.length == _requiredCharactersForAdapter) {
      Future.microtask(() {
        ref
            .read(javaPracticeAdapterProvider.notifier)
            .initializeAdapter(practiceCharacters);
        _startNextRound();
      });
    } else {
      print(
          "Error: HSK Practice Screen requires $_requiredCharactersForAdapter characters for the JavaPracticeAdapter, but got ${practiceCharacters.length}.");
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
                content: Text(
                    "Error: Not enough unique characters for this practice mode.")),
          );
          Navigator.of(context).pop();
        }
      });
    }
  }

  List<HskCharacter> _prepareCharactersForAdapter() {
    List<HskCharacter> allChars = widget.characterSet.characters.toList();
    allChars.shuffle();
    if (allChars.length >= _requiredCharactersForAdapter) {
      return allChars.sublist(0, _requiredCharactersForAdapter);
    } else {
      List<HskCharacter> prepared = List.from(allChars);
      while (prepared.length < _requiredCharactersForAdapter &&
          allChars.isNotEmpty) {
        prepared.addAll(allChars.take(min(
            _requiredCharactersForAdapter - prepared.length, allChars.length)));
      }
      return prepared;
    }
  }

  @override
  void dispose() {
    _questionTimer?.cancel();
    _sdAnnouncementTimer?.cancel();
    _subMessageTimer?.cancel();
    _delayedTextTimer?.cancel();
    _audioPlayer.dispose();
    Future.microtask(() {
      ref.read(javaPracticeAdapterProvider.notifier).resetAdapter();
    });
    super.dispose();
  }

  void _startNextRound() {
    if (_isAnnouncingSuddenDeath) return;

    final adapterNotifier = ref.read(javaPracticeAdapterProvider.notifier);
    final adapter = ref.read(javaPracticeAdapterProvider);

    if (adapter == null || adapter.finished) {
      _navigateToTimeOverScreen(
          isTimeout: false, endedDueToSuddenDeathMiss: false);
      return;
    }

    if (adapter.isSuddenDeathPending) {
      setState(() {
        _isAnnouncingSuddenDeath = true;
        _showingFeedback = false;
        _currentRound = null;
        _buttonVisibleState = List.generate(
            _buttonVisibleState.isNotEmpty ? _buttonVisibleState.length : 7,
            (index) => false);
        _currentSuddenDeathSubMessage = "This is sudden death mode";

        // Java-compatible sudden death timer boost: +300/300 = +100% boost
        _questionTimerValue = 1.0; // Full timer boost when sudden death starts
      });
      _questionTimer?.cancel();
      _subMessageTimer?.cancel();

      _subMessageTimer = Timer(const Duration(seconds: 2), () {
        if (!mounted || !_isAnnouncingSuddenDeath) return;
        setState(() {
          _currentSuddenDeathSubMessage = "One mistake will end this round.";
        });
      });

      _sdAnnouncementTimer?.cancel();
      _sdAnnouncementTimer = Timer(const Duration(seconds: 5), () {
        if (!mounted) return;
        adapterNotifier.acknowledgeSdFlag();
        setState(() {
          _isAnnouncingSuddenDeath = false;
          _currentSuddenDeathSubMessage = null;
        });
        _startNextRound();
      });
      return;
    }

    final newRound = adapterNotifier.getRound();

    setState(() {
      _currentRound = newRound;
      _showingFeedback = false;
      _selectedAnswerIndex = -1;
      _isCorrect = false;
      _showDelayedText = false; // Reset delayed text state for new round

      // Only reset timer to 100% for the very first round (countVar == 1)
      // All subsequent rounds preserve additive timer rewards
      if (adapter != null && adapter.currentRoundNumberPublicGetter == 1) {
        _questionTimerValue = 1.0; // First round starts with full timer
      }
      // For all other rounds, timer value carries over with accumulated rewards

      if (newRound != null) {
        _buttonVisibleState =
            List.generate(newRound.options.length, (index) => true);
      } else {
        _buttonVisibleState = [];
      }
    });

    if (_currentRound != null) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _playAudio();
        _startQuestionTimer();
        _startDelayedTextPrompt(); // Java-style delayed text prompt
      });
    } else {
      _navigateToTimeOverScreen();
    }
  }

  void _playAudio() async {
    if (_currentRound == null) return;
    final settings = await ref.read(hskPracticeSettingsNotifierProvider.future);
    if (settings.autoPlaySound) {
      try {
        final audioPath = _currentRound!.correctCharacter.audioAssetPath;
        debugPrint(
            'Playing audio: $audioPath (ID: ${_currentRound!.correctCharacter.characterId})');
        await _audioPlayer.setVolume(1.0);
        await _audioPlayer.play(AssetSource(audioPath));
      } catch (e) {
        debugPrint(
            'Error playing audio for ID: ${_currentRound!.correctCharacter.characterId}, Error: $e');
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text("Audio error: $e"),
              duration: const Duration(seconds: 2),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  void _startQuestionTimer() async {
    _questionTimer?.cancel();
    final settings = await ref.read(hskPracticeSettingsNotifierProvider.future);
    final maxDuration = settings.timerDurationSeconds;

    // Store the current timer value as the starting point for this round
    _questionTimerStartValue = _questionTimerValue;

    // Java-style timer with acceleration
    _questionTimer = Timer.periodic(
      const Duration(milliseconds: 100),
      (timer) {
        if (!mounted) {
          timer.cancel();
          return;
        }
        setState(() {
          // Java-style acceleration: timer decreases faster as speed increases
          // Convert timerSpeed (units per 100ms) to percentage decrease per 100ms
          final decreasePerTick = _timerSpeed /
              (maxDuration * 10); // Convert to percentage per 100ms

          _questionTimerValue -= decreasePerTick;

          if (_questionTimerValue <= 0) {
            _questionTimerValue = 0.0;
            _handleTimeout();
          }
        });
      },
    );
  }

  void _handleTimeout() {
    _questionTimer?.cancel();
    if (_currentRound == null || _showingFeedback || _isAnnouncingSuddenDeath)
      return;

    final adapterNotifier = ref.read(javaPracticeAdapterProvider.notifier);
    if (!_isCorrect) {
      adapterNotifier.miss(_currentRound!.correctCharacterMasterListIndex);
    }

    setState(() {
      _showingFeedback = true;
      _selectedAnswerIndex = -2; // Special marker for timeout
      _isCorrect = false;
      if (_currentRound != null) {
        _buttonVisibleState =
            List.generate(_currentRound!.options.length, (index) => false);
      }
    });

    // Java-style 2000ms delay before transitioning to completion screen
    Future.delayed(const Duration(milliseconds: 2000), () {
      if (mounted) {
        _navigateToTimeOverScreen(
            isTimeout: true, endedDueToSuddenDeathMiss: false);
      }
    });
  }

  /// Handles user answer selection with improved timer logic.
  ///
  /// **Timer Behavior (Java-Compatible):**
  /// - ✅ Normal Mode Correct: +75/300 = +25% of max timer (additive)
  /// - ⚡ Sudden Death Correct: +75/300 = +25% of max timer (additive)
  /// - 🚀 SD Mode Start: +300/300 = +100% boost (full timer)
  /// - ✅ Timer always capped at 100% maximum
  /// - ❌ Wrong answers keep timer pressure (no gaming the system)
  /// - 🎯 Creates escalating difficulty matching original Java logic
  ///
  /// This provides authentic Java-compatible timer behavior.
  void _handleAnswerSelected(int selectedOptionIndexOnScreen) {
    if (_currentRound == null || _showingFeedback || _isAnnouncingSuddenDeath)
      return;

    final selectedChar = _currentRound!.options[selectedOptionIndexOnScreen];
    final correctChar = _currentRound!.correctCharacter;
    final isActuallyCorrect =
        selectedChar.characterId == correctChar.characterId;

    final adapterNotifier = ref.read(javaPracticeAdapterProvider.notifier);
    final adapter = ref.read(javaPracticeAdapterProvider)!;

    if (adapter.isSuddenDeathActive && !isActuallyCorrect) {
      // Cancel timer for sudden death miss (game ends)
      _questionTimer?.cancel();
      adapterNotifier.miss(_currentRound!.correctCharacterMasterListIndex);
      setState(() {
        _selectedAnswerIndex = -3; // Special marker for sudden death miss
        _isCorrect = false;
        _showingFeedback = true;
        _buttonVisibleState =
            List.generate(_currentRound!.options.length, (_) => false);
      });
      // Java-style 2000ms delay before transitioning to completion screen
      Future.delayed(const Duration(milliseconds: 2000), () {
        if (mounted) {
          _navigateToTimeOverScreen(endedDueToSuddenDeathMiss: true);
        }
      });
      return;
    }

    // Only cancel timer for CORRECT answers - wrong answers keep timer pressure!
    if (isActuallyCorrect) {
      _questionTimer?.cancel();
    }

    setState(() {
      _selectedAnswerIndex = selectedOptionIndexOnScreen;
      _isCorrect = isActuallyCorrect;
      _showingFeedback = true;

      if (isActuallyCorrect) {
        // Java-compatible additive timer logic:
        // Both normal and sudden death modes: +75/300 = +25% of max timer
        _questionTimerValue += 0.25; // Add 25% (equivalent to Java's +75/300)
        if (_questionTimerValue > 1.0) {
          _questionTimerValue =
              1.0; // Cap at 100% (equivalent to Java's cap at 300)
        }

        // Java-style timer acceleration (timer gets faster with each correct answer)
        if (adapter != null && adapter.isSuddenDeathActive) {
          // Sudden Death mode: faster acceleration (+0.2 per correct)
          _timerSpeed += _suddenDeathSpeedIncrement;
          if (_timerSpeed > _maxSuddenDeathSpeed) {
            _timerSpeed = _maxSuddenDeathSpeed;
          }
        } else {
          // Normal mode: slower acceleration (+0.05 per correct)
          _timerSpeed += _normalModeSpeedIncrement;
          if (_timerSpeed > _maxNormalSpeed) {
            _timerSpeed = _maxNormalSpeed;
          }
        }

        _correctAnswersInSession++;
        adapterNotifier.hit(_currentRound!.correctCharacterMasterListIndex);

        // Only hide incorrect buttons, keep the correct one visible
        for (int i = 0; i < _buttonVisibleState.length; i++) {
          if (i != selectedOptionIndexOnScreen) {
            _buttonVisibleState[i] = false;
          }
        }
      } else {
        // Wrong answer: hide the button but keep timer running!
        _buttonVisibleState[selectedOptionIndexOnScreen] = false;
        _showingFeedback = false;
      }
    });

    if (isActuallyCorrect) {
      final delayDuration = Duration(milliseconds: adapter.timeDelayMs);

      // Check if this is the last question
      final isLastQuestion = adapter.finished;

      if (isLastQuestion) {
        // For the last question, show "Correct!" first, then "Well Done!", then navigate
        Future.delayed(delayDuration, () {
          if (mounted && _showingFeedback && _isCorrect) {
            setState(() {
              // Update prompt to "Well Done!" before transitioning
              _isShowingWellDoneMessage = true;
            });

            // After showing "Well Done!" briefly, navigate to completion screen
            Future.delayed(const Duration(milliseconds: 1200), () {
              if (mounted) {
                _navigateToTimeOverScreen(
                    isTimeout: false, endedDueToSuddenDeathMiss: false);
              }
            });
          }
        });
      } else {
        // For non-final questions, automatically proceed to next question after delay
        Future.delayed(delayDuration, () {
          if (mounted && _showingFeedback && _isCorrect) {
            _startNextRound();
          }
        });
      }
    } else {
      // Wrong answer logic: NO TIMER RESET! Let the pressure continue.
      final anyButtonLeftToTry = _buttonVisibleState.any((visible) => visible);

      if (!anyButtonLeftToTry) {
        // No buttons left to try - trigger timeout immediately
        _handleTimeout();
      }
      // Note: Removed timer restart logic - timer continues running!
      // This maintains pressure and prevents gaming the system.
    }
  }

  void _navigateToTimeOverScreen(
      {bool isTimeout = false, bool endedDueToSuddenDeathMiss = false}) {
    if (!mounted) return;
    final adapter = ref.read(javaPracticeAdapterProvider);

    int finalCorrectAnswers = _correctAnswersInSession;
    int maxProgPoints = _requiredCharactersForAdapter * 2;
    int actualProgPoints = 0;
    int roundsPlayed = 0;
    bool fullyCompleted = false;

    if (adapter != null) {
      maxProgPoints = adapter.totalCharactersToMaster * 2;
      actualProgPoints = adapter.getProgress();
      roundsPlayed = adapter.currentRoundNumberPublicGetter;
      fullyCompleted =
          adapter.finished && !isTimeout && !endedDueToSuddenDeathMiss;
    } else {
      // Fallback if adapter is somehow null, though unlikely here
      maxProgPoints = _requiredCharactersForAdapter * 2;
    }

    Navigator.pushReplacement(
      context,
      MaterialPageRoute(
        builder: (context) => HskTimeOverScreen(
          characterSet: widget.characterSet,
          correctAnswers: finalCorrectAnswers,
          maxProgressPoints: maxProgPoints, // Renamed from totalQuestions
          isTimeout: isTimeout,
          endedDueToSuddenDeathMiss: endedDueToSuddenDeathMiss,
          actualProgressPoints: actualProgPoints,
          totalRoundsPlayed: roundsPlayed,
          fullyCompletedSuccessfully: fullyCompleted,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final adapter = ref.watch(javaPracticeAdapterProvider);
    final settingsFuture =
        ref.watch(hskPracticeSettingsNotifierProvider.future);

    if (adapter == null) {
      return Scaffold(
        appBar: AppBar(title: const Text("Practice Mode")),
        body: const Center(child: CircularProgressIndicator()),
      );
    }

    if (_currentRound == null && !_isAnnouncingSuddenDeath) {
      return Scaffold(
        appBar: AppBar(title: const Text("Practice Mode")),
        body: const Center(
            child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(Colors.orange))),
      );
    }

    final progressPercent = adapter.totalCharactersToMaster > 0
        ? adapter.getProgress() /
            (adapter.totalCharactersToMaster + adapter.totalCharactersToMaster)
        : 0.0;

    return Scaffold(
      appBar: AppBar(
        title: Text(
          adapter.isSuddenDeathActive ? "Sudden Death!" : "Practice Mode",
          style: const TextStyle(color: Colors.white),
        ),
        backgroundColor: Colors.blue.shade900,
        iconTheme: const IconThemeData(color: Colors.white),
        actions: [
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () => _showSettingsDialog(context),
          ),
        ],
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.blue.shade900,
              Colors.teal.shade700,
            ],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // Progress indicator
              LinearProgressIndicator(
                value: progressPercent.clamp(0.0, 1.0),
                backgroundColor: Colors.black12,
                valueColor: AlwaysStoppedAnimation<Color>(
                  Colors.orange.shade300,
                ),
              ),

              // Round indicator
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 4.0),
                child: Text(
                  "Round: ${adapter.currentRoundNumberPublicGetter} / ${adapter.totalCharactersToMaster * 2} (S:${adapter.standardLearnedCount} SD:${adapter.successfulSdRounds}) - Speed: ${_timerSpeed.toStringAsFixed(1)}x",
                  style: HskTypography.stageIndicator,
                ),
              ),

              // Main content with proper layout management
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.fromLTRB(16.0, 8.0, 16.0,
                      16.0), // Better padding like Learn Screen
                  child: Column(
                    children: [
                      // Prompt area with proper constraints
                      FutureBuilder<HskPracticeSettings>(
                        future: settingsFuture,
                        builder: (context, snapshot) {
                          if (!snapshot.hasData) {
                            return const SizedBox(
                                height: 100,
                                child:
                                    Center(child: CircularProgressIndicator()));
                          }
                          final settings = snapshot.data!;
                          return _buildPromptArea(settings, adapter);
                        },
                      ),

                      // Minimal spacing between prompt and buttons
                      const SizedBox(height: 8.0),

                      // Answer area in flexible container that adapts to content
                      Expanded(
                        child: Column(
                          children: [
                            // Button grid takes available space
                            Expanded(
                              child: (_showingFeedback && _isCorrect) ||
                                      (!_showingFeedback) ||
                                      (_showingFeedback &&
                                          !_isCorrect &&
                                          _buttonVisibleState.any((v) => v))
                                  ? _buildAnswerGrid(adapter)
                                  : const SizedBox.shrink(),
                            ),

                            // Timer bar and audio section at bottom
                            // Note: Timer remains visible during all states including feedback
                            // to maintain consistent time pressure and user awareness
                            Column(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                // Timer bar - Always visible (even during feedback)
                                Container(
                                  width: double.infinity,
                                  height: 8,
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(4),
                                    color: Colors.black26,
                                  ),
                                  child: FractionallySizedBox(
                                    widthFactor:
                                        _questionTimerValue.clamp(0.0, 1.0),
                                    alignment: Alignment.centerLeft,
                                    child: Container(
                                      decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(4),
                                        color: _questionTimerValue > 0.3
                                            ? Colors.green
                                            : Colors.red,
                                      ),
                                    ),
                                  ),
                                ),
                                const SizedBox(height: 12),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              // Audio button positioned at bottom center (floating like Learn Screen)
              Align(
                alignment: Alignment.bottomCenter,
                child: Padding(
                  padding: const EdgeInsets.only(right: 12.0, bottom: 4.0),
                  child: _buildAudioButton(),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPromptArea(
      HskPracticeSettings settings, JavaPracticeAdapter adapter) {
    if (_isAnnouncingSuddenDeath) {
      return Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(vertical: 20.0, horizontal: 16.0),
        constraints: const BoxConstraints(minHeight: 150),
        decoration: BoxDecoration(
          color: Colors.black26,
          borderRadius: BorderRadius.circular(16.0),
        ),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                "Sudden Death Mode Begins!",
                style: HskTypography.withColor(
                  HskTypography.withSize(HskTypography.feedbackError, 30),
                  Colors.orangeAccent,
                ),
                textAlign: TextAlign.center,
              ),
              if (_currentSuddenDeathSubMessage != null)
                Padding(
                  padding: const EdgeInsets.only(top: 12.0),
                  child: Text(
                    _currentSuddenDeathSubMessage!,
                    style: HskTypography.withSize(
                      HskTypography.englishMedium,
                      20,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
            ],
          ),
        ),
      );
    }
    if (_currentRound == null) return const SizedBox.shrink();
    final correctCharacterForPrompt = _currentRound!.correctCharacter;

    // Determine prompt content with responsive text handling
    Widget promptContent;

    if (_showingFeedback && _isCorrect) {
      // Display "Correct" or "Well Done!" within the prompt area
      promptContent = Text(
        _isShowingWellDoneMessage ? "Well Done!" : "Correct!",
        style: HskTypography.withColor(
          HskTypography.feedbackSuccess,
          Colors.green.shade100,
        ),
        textAlign: TextAlign.center,
      );
    } else if (_showingFeedback && _selectedAnswerIndex == -2) {
      // Java-style timeout message
      promptContent = Text(
        "Time over",
        style: HskTypography.withColor(
          HskTypography.feedbackError,
          Colors.red.shade100,
        ),
        textAlign: TextAlign.center,
      );
    } else if (_showingFeedback && _selectedAnswerIndex == -3) {
      // Java-style sudden death message
      promptContent = Text(
        "Sudden Death!",
        style: HskTypography.withColor(
          HskTypography.feedbackError,
          Colors.red.shade100,
        ),
        textAlign: TextAlign.center,
      );
    } else if (settings.delayTextPrompt && !_showDelayedText) {
      // Java-style delayed text: show empty/waiting state initially
      promptContent = Text(
        "", // Empty text initially (like Java implementation)
        style: HskTypography.withSize(
          HskTypography.englishMedium,
          22,
        ),
        textAlign: TextAlign.center,
      );
    } else {
      // Show pinyin and/or English (either immediately or after delay)
      promptContent = Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          if (settings.showPinyin)
            FittedBox(
              fit: BoxFit.scaleDown,
              child: Text(
                correctCharacterForPrompt.pinyin,
                style: HskTypography.pinyinMedium,
                textAlign: TextAlign.center,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          if (settings.showPinyin && settings.showEnglish)
            const SizedBox(height: 4), // Reduced spacing like Learn Screen
          if (settings.showEnglish)
            Flexible(
              child: Text(
                correctCharacterForPrompt.englishTranslation,
                style: HskTypography.englishMedium,
                textAlign: TextAlign.center,
                maxLines: 2, // Allow English to wrap up to 2 lines
                overflow: TextOverflow.ellipsis,
              ),
            ),
        ],
      );
    }

    // Always use the same container style for visual stability with proper constraints
    return Container(
      width: double.infinity,
      constraints: const BoxConstraints(
        minHeight: _kPromptMinHeight,
        maxHeight: _kPromptMaxHeight,
      ),
      padding: const EdgeInsets.symmetric(
        horizontal: 20,
        vertical: 12, // Reduced vertical padding like Learn Screen
      ),
      decoration: BoxDecoration(
        color: Colors.black26,
        borderRadius: BorderRadius.circular(_kPromptBorderRadius),
      ),
      child: Center(child: promptContent),
    );
  }

  Widget _buildAnswerGrid(JavaPracticeAdapter adapter) {
    if (_currentRound == null || _currentRound!.options.isEmpty) {
      return const Text("Loading options...",
          style: TextStyle(color: Colors.white));
    }

    final options = _currentRound!.options;
    if (_buttonVisibleState.isEmpty && options.isNotEmpty) {
      _buttonVisibleState = List.generate(options.length, (index) => true);
    }

    return LayoutBuilder(builder: (context, constraints) {
      final double availableWidthForGrid = constraints.maxWidth;
      final double availableHeightForGrid = constraints.maxHeight;
      double targetButtonWidth;
      double targetButtonHeight;
      final itemCount = options.length;

      // Determine effective items per row for sizing strategy (improved logic from Learn Screen)
      int effectiveItemsPerRow;
      if (itemCount == 0) {
        effectiveItemsPerRow = 1; // Avoid division by zero
      } else if (itemCount == 1) {
        effectiveItemsPerRow = 1;
      } else if (itemCount == 2) {
        effectiveItemsPerRow = 2;
      } else {
        // 3 or more items, assume a max of 3 for width calculation for consistency
        effectiveItemsPerRow = 3;
      }

      // Calculate targetButtonWidth based on effectiveItemsPerRow (improved from Learn Screen)
      double totalHorizontalGapFactor;
      if (itemCount == 1) {
        totalHorizontalGapFactor =
            0.35; // Single button can be smaller than full width
      } else if (effectiveItemsPerRow == 2) {
        totalHorizontalGapFactor =
            (effectiveItemsPerRow * 0.05) + 0.05; // Aim for ~85% utilization
      } else {
        totalHorizontalGapFactor =
            (effectiveItemsPerRow * 0.04) + 0.04; // Aim for ~88% utilization
      }

      targetButtonWidth = (availableWidthForGrid / effectiveItemsPerRow) *
          (1 - totalHorizontalGapFactor);
      targetButtonWidth =
          targetButtonWidth.clamp(_kMinButtonWidth, _kMaxButtonWidth);

      // Calculate vertical layout based on number of rows needed (improved logic)
      int rows;
      if (itemCount == 7) {
        rows = 3; // Special 2-3-2 layout
      } else {
        rows = itemCount <= 3 ? 1 : (itemCount <= 6 ? 2 : 3);
      }

      // Adjust button height based on available height and row count
      // Maximize usage of vertical space while ensuring no overflow
      double maxHeightPerRow = rows > 0
          ? (availableHeightForGrid * 0.85) / rows // Reduced to 85% for safety
          : _kMinButtonHeight;
      targetButtonHeight =
          maxHeightPerRow * 0.9; // Reduced to 90% to ensure no overflow

      // Clamp button height
      targetButtonHeight =
          targetButtonHeight.clamp(_kMinButtonHeight, _kMaxButtonHeight);

      // Ensure proper button aspect ratio
      if (itemCount == 1 && targetButtonWidth < targetButtonHeight * 0.8) {
        targetButtonWidth = targetButtonHeight * 0.8;
      }

      // Use minimal spacing for multi-row layouts to prevent overflow
      double rowSpacing = rows > 1 ? 4.0 : 6.0; // Increased spacing for safety

      // Build layout with improved row distribution
      return Padding(
        padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.start, // Align from top
          crossAxisAlignment: CrossAxisAlignment.center, // Center horizontally
          children: [
            // Special 2-3-2 layout for 7 items (restoring original pattern)
            if (itemCount == 7) ...[
              // Row 1: 2 buttons
              _buildButtonRow(
                  0, 2, options, targetButtonWidth, targetButtonHeight),
              SizedBox(height: rowSpacing),
              // Row 2: 3 buttons
              _buildButtonRow(
                  2, 3, options, targetButtonWidth, targetButtonHeight),
              SizedBox(height: rowSpacing),
              // Row 3: 2 buttons
              _buildButtonRow(
                  5, 2, options, targetButtonWidth, targetButtonHeight),
            ] else ...[
              // Generic layout for other item counts
              if (itemCount > 0) ...[
                _buildButtonRow(0, min(3, itemCount), options,
                    targetButtonWidth, targetButtonHeight),
              ],
              if (itemCount > 3) ...[
                SizedBox(height: rowSpacing),
                _buildButtonRow(3, min(3, itemCount - 3), options,
                    targetButtonWidth, targetButtonHeight),
              ],
              if (itemCount > 6) ...[
                SizedBox(height: rowSpacing),
                _buildButtonRow(6, min(3, itemCount - 6), options,
                    targetButtonWidth, targetButtonHeight),
              ],
            ],
          ],
        ),
      );
    });
  }

  // Helper to build a row of buttons, similar to HskLearnScreen
  Widget _buildButtonRow(int startIndex, int countInRow,
      List<HskCharacter> options, double targetWidth, double targetHeight) {
    if (countInRow <= 0) return const SizedBox.shrink();
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(countInRow, (indexInRow) {
        final optionIndex = startIndex + indexInRow;
        if (optionIndex >= options.length) return const SizedBox.shrink();
        return _buildPracticeButtonWidget(
            optionIndex, options[optionIndex], targetWidth, targetHeight);
      }),
    );
  }

  // Renamed original button building logic to avoid conflicts and pass dimensions
  Widget _buildPracticeButtonWidget(int optionIndex, HskCharacter character,
      double itemWidth, double itemHeight) {
    // Visibility check from _buttonVisibleState
    if (!_buttonVisibleState[optionIndex]) {
      return SizedBox(
          width: itemWidth + _kButtonPadding.horizontal,
          height: itemHeight + _kButtonPadding.vertical); // Keep space
    }

    // Determine button background color with better visual feedback
    Color currentButtonBgColor = Colors.white.withOpacity(0.15);

    // Add selection feedback like in Learn Screen
    if (_selectedAnswerIndex == optionIndex && _showingFeedback) {
      if (_isCorrect) {
        currentButtonBgColor = Colors.green.withOpacity(0.4);
      } else {
        currentButtonBgColor = Colors.red.withOpacity(0.4);
      }
    }

    return Opacity(
      opacity: 1.0,
      child: Padding(
        padding: _kButtonPadding,
        child: AnimatedScale(
          scale: _selectedAnswerIndex == optionIndex && _showingFeedback
              ? 1.05
              : 1.0,
          duration: _kButtonPulseDuration,
          child: Material(
            type: MaterialType.transparency,
            borderRadius: BorderRadius.circular(_kButtonBorderRadius),
            child: InkWell(
              onTap: _showingFeedback && _isCorrect
                  ? null
                  : () => _handleAnswerSelected(optionIndex),
              borderRadius: BorderRadius.circular(_kButtonBorderRadius),
              splashColor: Colors.white.withOpacity(0.2),
              highlightColor: Colors.white.withOpacity(0.1),
              child: Container(
                width: itemWidth,
                height: itemHeight,
                decoration: BoxDecoration(
                  color: currentButtonBgColor,
                  borderRadius: BorderRadius.circular(_kButtonBorderRadius),
                  border: Border.all(
                    color: Colors.white.withOpacity(0.3),
                    width: 1.0,
                  ),
                ),
                alignment: Alignment.center,
                padding: _kButtonContentPadding,
                clipBehavior:
                    Clip.hardEdge, // Ensure content is clipped to bounds
                child: ResponsiveButtonText(
                  character: character.character,
                  maxWidth: itemWidth - _kButtonContentPadding.horizontal,
                  maxHeight: itemHeight - _kButtonContentPadding.vertical,
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _showSettingsDialog(BuildContext context) async {
    _questionTimer?.cancel();
    final currentSettings =
        await ref.read(hskPracticeSettingsNotifierProvider.future);

    await showDialog<HskPracticeSettings>(
      context: context,
      builder: (context) => PracticeSettingsDialog(
          initialSettings: currentSettings,
          onSettingsChanged: (updatedSettings) {
            ref
                .read(hskPracticeSettingsNotifierProvider.notifier)
                .updateSettings(updatedSettings);
          }),
    );

    if (mounted && !_showingFeedback && _currentRound != null) {
      _startQuestionTimer();
    }
  }

  Widget _buildAudioButton() {
    return Container(
      margin: const EdgeInsets.only(bottom: 4.0),
      child: Material(
        color: Colors.blue.shade800,
        shape: const CircleBorder(),
        elevation: 3.0,
        child: InkWell(
          onTap: _playAudio,
          customBorder: const CircleBorder(),
          child: Padding(
            padding: const EdgeInsets.all(8.0), // Consistent with Learn Screen
            child: Icon(
              Icons.volume_up,
              color: Colors.white,
              size: 28, // Slightly smaller than before for better proportions
            ),
          ),
        ),
      ),
    );
  }

  void _startDelayedTextPrompt() async {
    if (_currentRound == null) return;
    final settings = await ref.read(hskPracticeSettingsNotifierProvider.future);

    if (settings.delayTextPrompt) {
      _delayedTextTimer?.cancel();
      // Java-compatible 1 second delay
      _delayedTextTimer = Timer(const Duration(seconds: 1), () {
        if (mounted) {
          setState(() {
            _showDelayedText = true;
          });
        }
      });
    } else {
      // If no delay, show text immediately
      setState(() {
        _showDelayedText = true;
      });
    }
  }
}

class PracticeSettingsDialog extends StatefulWidget {
  final HskPracticeSettings initialSettings;
  final void Function(HskPracticeSettings) onSettingsChanged;

  const PracticeSettingsDialog({
    super.key,
    required this.initialSettings,
    required this.onSettingsChanged,
  });

  @override
  State<PracticeSettingsDialog> createState() => _PracticeSettingsDialogState();
}

class _PracticeSettingsDialogState extends State<PracticeSettingsDialog> {
  late HskPracticeSettings settings;

  @override
  void initState() {
    super.initState();
    settings = widget.initialSettings;
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text("Practice Mode Settings"),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            CheckboxListTile(
              title: const Text("Auto Play Sound"),
              value: settings.autoPlaySound,
              onChanged: (value) {
                setState(() {
                  settings = settings.copyWith(
                    autoPlaySound: value ?? true,
                  );
                });
              },
            ),
            CheckboxListTile(
              title: const Text("Show English"),
              value: settings.showEnglish,
              onChanged: (value) {
                setState(() {
                  settings = settings.copyWith(
                    showEnglish: value ?? true,
                  );
                });
              },
            ),
            CheckboxListTile(
              title: const Text("Show Pinyin"),
              value: settings.showPinyin,
              onChanged: (value) {
                setState(() {
                  settings = settings.copyWith(
                    showPinyin: value ?? true,
                  );
                });
              },
            ),
            CheckboxListTile(
              title: const Text("Delay text prompt"),
              value: settings.delayTextPrompt,
              onChanged: (value) {
                setState(() {
                  settings = settings.copyWith(
                    delayTextPrompt: value ?? false,
                  );
                });
              },
            ),
            const SizedBox(height: 8),
            ListTile(
              title: const Text("Timer Duration (sec)"),
              subtitle: Text(
                  "${settings.timerDurationSeconds.toStringAsFixed(1)} seconds (Java-inspired accelerating timer)"),
            ),
            Slider(
              value: settings.timerDurationSeconds,
              min: 10.0, // Minimum 10 seconds
              max: 50.0, // Maximum 50 seconds (Java inspired + extension)
              divisions: 80, // 0.5 second increments
              label: "${settings.timerDurationSeconds.toStringAsFixed(1)}s",
              onChanged: (value) {
                setState(() {
                  settings = settings.copyWith(
                    timerDurationSeconds: value, // Keep as double
                  );
                });
              },
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () {
            Navigator.of(context).pop(null);
          },
          child: const Text("CANCEL"),
        ),
        TextButton(
          onPressed: () {
            widget.onSettingsChanged(settings);
            Navigator.of(context).pop(settings);
          },
          child: const Text("OKAY"),
        ),
      ],
    );
  }
}
