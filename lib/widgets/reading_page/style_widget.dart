import 'dart:io';

import 'package:dasso_reader/config/shared_preference_provider.dart';
import 'package:dasso_reader/l10n/generated/L10n.dart';
import 'package:dasso_reader/models/book_style.dart';
import 'package:dasso_reader/models/font_model.dart';
import 'package:dasso_reader/page/reading_page.dart';
import 'package:dasso_reader/page/settings_page/subpage/fonts.dart';
import 'package:dasso_reader/service/book_player/book_player_server.dart';
import 'package:dasso_reader/service/font.dart';
import 'package:dasso_reader/utils/font_parser.dart';
import 'package:dasso_reader/utils/get_path/get_base_path.dart';
import 'package:dasso_reader/widgets/icon_and_text.dart';
import 'package:dasso_reader/widgets/reading_page/more_settings/more_settings.dart';
import 'package:dasso_reader/widgets/reading_page/widget_title.dart';
import 'package:dasso_reader/dao/theme.dart';
import 'package:dasso_reader/main.dart';
import 'package:dasso_reader/models/read_theme.dart';
import 'package:dasso_reader/page/book_player/epub_player.dart';
import 'package:flutter/material.dart';
import 'package:flutter_colorpicker/flutter_colorpicker.dart';
import 'dart:ui' as ui;

// Custom thumb shape that displays text inside
class TextThumbShape extends SliderComponentShape {
  final double thumbRadius;
  final TextStyle textStyle;
  final double elevation;
  final String Function(double value)? valueToString;

  const TextThumbShape({
    required this.thumbRadius,
    required this.textStyle,
    this.elevation = 2.0,
    this.valueToString,
  });

  @override
  Size getPreferredSize(bool isEnabled, bool isDiscrete) {
    return Size.fromRadius(thumbRadius);
  }

  @override
  void paint(
    PaintingContext context,
    Offset center, {
    required Animation<double> activationAnimation,
    required Animation<double> enableAnimation,
    required bool isDiscrete,
    required TextPainter labelPainter,
    required RenderBox parentBox,
    required SliderThemeData sliderTheme,
    required TextDirection textDirection,
    required double value,
    required double textScaleFactor,
    required Size sizeWithOverflow,
  }) {
    final Canvas canvas = context.canvas;

    // Draw shadow
    if (elevation > 0) {
      final shadowPath = Path()
        ..addOval(Rect.fromCircle(center: center, radius: thumbRadius));
      canvas.drawShadow(shadowPath, Colors.black, elevation, true);
    }

    // Draw the white circle
    final fillPaint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.fill;
    canvas.drawCircle(center, thumbRadius, fillPaint);

    // Draw a subtle border
    final borderPaint = Paint()
      ..color = Colors.grey.withOpacity(0.3)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.0;
    canvas.drawCircle(center, thumbRadius, borderPaint);

    // Get the text to display - either from the valueToString callback or directly
    final text = valueToString != null
        ? valueToString!(value)
        : value.toStringAsFixed(1);

    // Draw the text
    final textSpan = TextSpan(
      text: text,
      style: textStyle,
    );
    final textPainter = TextPainter(
      text: textSpan,
      textDirection: TextDirection.ltr,
      textAlign: TextAlign.center,
    );
    textPainter.layout();

    final textOffset = Offset(
      center.dx - (textPainter.width / 2),
      center.dy - (textPainter.height / 2),
    );

    textPainter.paint(canvas, textOffset);
  }
}

// Custom track shape with icons and dots
class IconSliderTrackShape extends RoundedRectSliderTrackShape {
  final int divisions;
  final Color dotColor;
  final double dotSize;
  final double opacity;
  final IconData? leftIcon;
  final IconData? rightIcon;
  final String? leftText;
  final String? rightText;
  final double iconSize;
  final double fontSize;
  final double largeFontSize;

  const IconSliderTrackShape({
    required this.divisions,
    this.dotColor = Colors.black,
    this.dotSize = 2.5,
    this.opacity = 0.2,
    this.leftIcon,
    this.rightIcon,
    this.leftText,
    this.rightText,
    this.iconSize = 12.0,
    this.fontSize = 10.0,
    this.largeFontSize = 16.0,
  });

  @override
  void paint(
    PaintingContext context,
    Offset offset, {
    required RenderBox parentBox,
    required SliderThemeData sliderTheme,
    required Animation<double> enableAnimation,
    required TextDirection textDirection,
    required Offset thumbCenter,
    Offset? secondaryOffset,
    bool isDiscrete = false,
    bool isEnabled = false,
    double additionalActiveTrackHeight = 0,
  }) {
    // Get the canvas
    final Canvas canvas = context.canvas;

    // Calculate the track rect
    final Rect trackRect = getPreferredRect(
      parentBox: parentBox,
      offset: offset,
      sliderTheme: sliderTheme,
      isEnabled: isEnabled,
      isDiscrete: isDiscrete,
    );

    // Draw track background
    final Paint trackPaint = Paint()
      ..color = sliderTheme.inactiveTrackColor ?? Colors.grey.withOpacity(0.1)
      ..style = PaintingStyle.fill;

    final Radius trackRadius = Radius.circular(trackRect.height / 2);

    canvas.drawRRect(
      RRect.fromRectAndRadius(trackRect, trackRadius),
      trackPaint,
    );

    // Define icon/text padding (space needed for icons/text at edges)
    final double iconPadding = 26.0;

    // Draw left icon or text
    double leftOffset = trackRect.left;
    if (leftIcon != null) {
      final iconData = leftIcon!;
      final iconPainter = TextPainter(
        text: TextSpan(
          text: String.fromCharCode(iconData.codePoint),
          style: TextStyle(
            fontSize: iconSize,
            fontFamily: iconData.fontFamily,
            package: iconData.fontPackage,
            color: dotColor.withOpacity(0.8),
          ),
        ),
        textDirection: TextDirection.ltr,
      );

      iconPainter.layout();
      final double iconX = trackRect.left + 8;
      final double iconY = trackRect.center.dy - (iconPainter.height / 2);
      iconPainter.paint(canvas, Offset(iconX, iconY));
      leftOffset = iconX + iconPainter.width + 8;
    } else if (leftText != null) {
      final textPainter = TextPainter(
        text: TextSpan(
          text: leftText,
          style: TextStyle(
            fontSize: fontSize,
            fontWeight: FontWeight.bold,
            color: dotColor.withOpacity(0.8),
          ),
        ),
        textDirection: TextDirection.ltr,
      );

      textPainter.layout();
      final double textX = trackRect.left + 8;
      final double textY = trackRect.center.dy - (textPainter.height / 2);
      textPainter.paint(canvas, Offset(textX, textY));
      leftOffset = textX + textPainter.width + 8;
    } else {
      leftOffset = trackRect.left + iconPadding;
    }

    // Draw right icon or text
    double rightOffset = trackRect.right;
    if (rightIcon != null) {
      final iconData = rightIcon!;
      final iconPainter = TextPainter(
        text: TextSpan(
          text: String.fromCharCode(iconData.codePoint),
          style: TextStyle(
            fontSize: iconSize,
            fontFamily: iconData.fontFamily,
            package: iconData.fontPackage,
            color: dotColor.withOpacity(0.8),
          ),
        ),
        textDirection: TextDirection.ltr,
      );

      iconPainter.layout();
      final double iconX = trackRect.right - 8 - iconPainter.width;
      final double iconY = trackRect.center.dy - (iconPainter.height / 2);
      iconPainter.paint(canvas, Offset(iconX, iconY));
      rightOffset = iconX - 8;
    } else if (rightText != null) {
      final textPainter = TextPainter(
        text: TextSpan(
          text: rightText,
          style: TextStyle(
            fontSize: largeFontSize,
            fontWeight: FontWeight.bold,
            color: dotColor.withOpacity(0.8),
          ),
        ),
        textDirection: TextDirection.ltr,
      );

      textPainter.layout();
      final double textX = trackRect.right - 8 - textPainter.width;
      final double textY = trackRect.center.dy - (textPainter.height / 2);
      textPainter.paint(canvas, Offset(textX, textY));
      rightOffset = textX - 8;
    } else {
      rightOffset = trackRect.right - iconPadding;
    }

    // Draw dots along the track within the usable range (between icons/text)
    if (divisions > 0) {
      final Paint dotPaint = Paint()
        ..color = dotColor.withOpacity(opacity)
        ..style = PaintingStyle.fill;

      // Calculate usable area width
      final double usableWidth = rightOffset - leftOffset;
      // Calculate step width within the usable area
      final double stepWidth = usableWidth / divisions;

      // Draw dots only within the usable range
      for (int i = 0; i <= divisions; i++) {
        final double x = leftOffset + i * stepWidth;
        final double y = trackRect.center.dy;
        canvas.drawCircle(Offset(x, y), dotSize, dotPaint);
      }
    }
  }
}

enum PageTurn {
  noAnimation,
  slide,
  scroll;

  String getLabel(BuildContext context) {
    switch (this) {
      case PageTurn.noAnimation:
        return L10n.of(context).no_animation;
      case PageTurn.slide:
        return L10n.of(context).slide;
      case PageTurn.scroll:
        return L10n.of(context).scroll;
    }
  }
}

class StyleWidget extends StatefulWidget {
  const StyleWidget({
    super.key,
    required this.themes,
    required this.epubPlayerKey,
    required this.setCurrentPage,
    this.backgroundColor,
    this.textColor,
  });

  final List<ReadTheme> themes;
  final GlobalKey<EpubPlayerState> epubPlayerKey;
  final Function setCurrentPage;
  final Color? backgroundColor;
  final Color? textColor;

  @override
  StyleWidgetState createState() => StyleWidgetState();
}

class StyleWidgetState extends State<StyleWidget> {
  BookStyle bookStyle = Prefs().bookStyle;
  int? currentThemeId = Prefs().readTheme.id;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      decoration: BoxDecoration(
        color: widget.backgroundColor ?? Theme.of(context).colorScheme.surface,
        // Remove border radius and shadow to create continuous appearance with bottom bar
      ),
      child: Column(
        children: [
          // Add 24px top padding for consistent spacing
          const SizedBox(height: 24.0),
          sliders(),
          const SizedBox(height: 10),
          fontSelection(),
          // Add bottom padding for consistent spacing
          const SizedBox(height: 24.0),
        ],
      ),
    );
  }

  List<FontModel> fonts() {
    Directory fontDir = getFontDir();
    List<FontModel> fontList = [
      FontModel(
        label: L10n.of(context).download_fonts,
        name: 'download',
        path: '',
      ),
      FontModel(
        label: L10n.of(context).add_new_font,
        name: 'newFont',
        path: '',
      ),
      FontModel(
        label: L10n.of(context).follow_book,
        name: 'book',
        path: '',
      ),
      FontModel(
        label: L10n.of(context).system_font,
        name: 'system',
        path: 'system',
      ),
    ];
    // fontDir.listSync().forEach((element) {
    //   if (element is File) {
    //     fontList.add(FontModel(
    //       label: getFontNameFromFile(element),
    //       name: 'customFont' + ,
    //       path:
    //           'http://localhost:${Server().port}/fonts/${element.path.split('/').last}',
    //     ));
    //   }
    // });
    // name = 'customFont' + index
    for (int i = 0; i < fontDir.listSync().length; i++) {
      File element = fontDir.listSync()[i] as File;
      fontList.add(FontModel(
        label: getFontNameFromFile(element),
        name: 'customFont$i',
        path:
            'http://localhost:${Server().port}/fonts/${element.path.split(Platform.pathSeparator).last}',
      ));
    }

    return fontList;
  }

  Widget fontSelection() {
    FontModel? font = fonts().firstWhere(
        (element) => element.path == Prefs().font.path,
        orElse: () => FontModel(
            label: L10n.of(context).follow_book, name: 'book', path: ''));

    Widget? leadingIcon(String name) {
      if (name == 'download') {
        return const Icon(Icons.download);
      } else if (name == 'newFont') {
        return const Icon(Icons.add);
      }
      return null;
    }

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: DropdownMenu<FontModel>(
        label: Text(L10n.of(context).font),
        initialSelection: font,
        width: MediaQuery.of(context).size.width > 400
            ? 380
            : MediaQuery.of(context).size.width * 0.85,
        inputDecorationTheme: InputDecorationTheme(
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(50),
          ),
        ),
        onSelected: (FontModel? font) async {
          if (font == null) return;
          if (font.name == 'newFont') {
            await importFont();
            setState(() {});
            return;
          } else if (font.name == 'download') {
            Navigator.push(
              context,
              MaterialPageRoute(builder: (context) => const FontsSettingPage()),
            );
            return;
          }
          widget.epubPlayerKey.currentState!.changeFont(font);
          Prefs().font = font;
        },
        dropdownMenuEntries: fonts()
            .map((font) => DropdownMenuEntry(
                  value: font,
                  label: font.label,
                  leadingIcon: leadingIcon(font.name),
                ))
            .toList(),
      ),
    );
  }

  Widget sliders() {
    return Padding(
      padding: const EdgeInsets.all(3.0),
      child: Column(
        children: [
          const SizedBox(height: 10),
          fontSizeSlider(),
          const SizedBox(height: 15),
          Row(
            children: [
              Expanded(child: lineHeightSlider()),
              const SizedBox(width: 15),
              Expanded(child: sideMarginSlider()),
            ],
          ),
        ],
      ),
    );
  }

  Widget sideMarginSlider() {
    final txtColor =
        widget.textColor ?? Theme.of(context).colorScheme.onSurface;

    return SliderTheme(
      data: SliderThemeData(
        trackHeight: 40.0,
        thumbShape: const RoundSliderThumbShape(
          enabledThumbRadius: 16.0,
          elevation: 2.0,
        ),
        overlayShape: const RoundSliderOverlayShape(overlayRadius: 24.0),
        trackShape: const IconSliderTrackShape(
          divisions: 4,
          dotColor: Colors.black,
          dotSize: 2.5,
          opacity: 0.2,
          leftIcon: Icons.margin_rounded,
          rightIcon: Icons.format_align_justify,
        ),
        activeTrackColor: Colors.transparent,
        inactiveTrackColor: txtColor.withOpacity(0.10),
        thumbColor: Colors.white,
        overlayColor: txtColor.withAlpha(30),
        showValueIndicator: ShowValueIndicator.never,
      ),
      child: Slider(
        value: bookStyle.sideMargin,
        onChanged: (double value) {
          setState(() {
            bookStyle.sideMargin = value;
            widget.epubPlayerKey.currentState!.changeStyle(bookStyle);
            Prefs().saveBookStyleToPrefs(bookStyle);
          });
        },
        min: 0,
        max: 20,
        divisions: 20,
      ),
    );
  }

  Widget lineHeightSlider() {
    final txtColor =
        widget.textColor ?? Theme.of(context).colorScheme.onSurface;

    return SliderTheme(
      data: SliderThemeData(
        trackHeight: 40.0,
        thumbShape: const RoundSliderThumbShape(
          enabledThumbRadius: 16.0,
          elevation: 2.0,
        ),
        overlayShape: const RoundSliderOverlayShape(overlayRadius: 24.0),
        trackShape: const IconSliderTrackShape(
          divisions: 4,
          dotColor: Colors.black,
          dotSize: 2.5,
          opacity: 0.2,
          leftIcon: Icons.line_weight,
          rightIcon: Icons.height,
        ),
        activeTrackColor: Colors.transparent,
        inactiveTrackColor: txtColor.withOpacity(0.10),
        thumbColor: Colors.white,
        overlayColor: txtColor.withAlpha(30),
        showValueIndicator: ShowValueIndicator.never,
      ),
      child: Slider(
        value: bookStyle.lineHeight,
        onChanged: (double value) {
          setState(() {
            bookStyle.lineHeight = value;
            widget.epubPlayerKey.currentState!.changeStyle(bookStyle);
            Prefs().saveBookStyleToPrefs(bookStyle);
          });
        },
        min: 0,
        max: 3,
        divisions: 10,
      ),
    );
  }

  Widget fontSizeSlider() {
    final txtColor =
        widget.textColor ?? Theme.of(context).colorScheme.onSurface;

    return SliderTheme(
      data: SliderThemeData(
        trackHeight: 40.0,
        thumbShape: TextThumbShape(
          thumbRadius: 16.0,
          textStyle: TextStyle(
            fontSize: 10,
            fontWeight: FontWeight.bold,
            color: txtColor,
          ),
          valueToString: (value) => value.toStringAsFixed(1),
        ),
        overlayShape: const RoundSliderOverlayShape(overlayRadius: 24.0),
        trackShape: const IconSliderTrackShape(
          divisions: 10,
          dotColor: Colors.black,
          dotSize: 2.5,
          opacity: 0.2,
          leftText: "A",
          rightText: "A",
          fontSize: 10.0,
          largeFontSize: 16.0,
        ),
        activeTrackColor: Colors.transparent,
        inactiveTrackColor: txtColor.withOpacity(0.10),
        thumbColor: Colors.white,
        overlayColor: txtColor.withAlpha(30),
        showValueIndicator: ShowValueIndicator.never,
      ),
      child: Slider(
        value: bookStyle.fontSize,
        onChanged: (double value) {
          setState(() {
            bookStyle.fontSize = value;
            widget.epubPlayerKey.currentState!.changeStyle(bookStyle);
            Prefs().saveBookStyleToPrefs(bookStyle);
          });
        },
        min: 0.5,
        max: 3.0,
        divisions: 25,
      ),
    );
  }
}

class ThemeChangeWidget extends StatefulWidget {
  const ThemeChangeWidget({
    super.key,
    required this.readTheme,
    required this.setCurrentPage,
  });

  final ReadTheme readTheme;
  final Function setCurrentPage;

  @override
  State<ThemeChangeWidget> createState() => _ThemeChangeWidgetState();
}

class _ThemeChangeWidgetState extends State<ThemeChangeWidget> {
  late ReadTheme readTheme;

  @override
  void initState() {
    super.initState();
    readTheme = widget.readTheme;
  }

  @override
  Widget build(BuildContext context) {
    return Row(children: [
      IconButton(
        onPressed: () async {
          String? pickingColor =
              await showColorPickerDialog(readTheme.backgroundColor);
          if (pickingColor != '') {
            setState(() {
              readTheme.backgroundColor = pickingColor!;
            });
            updateTheme(readTheme);
          }
        },
        icon: Icon(Icons.circle,
            size: 80,
            color: Color(int.parse('0x${readTheme.backgroundColor}'))),
      ),
      IconButton(
          onPressed: () async {
            String? pickingColor =
                await showColorPickerDialog(readTheme.textColor);
            if (pickingColor != '') {
              setState(() {
                readTheme.textColor = pickingColor!;
              });
              updateTheme(readTheme);
            }
          },
          icon: Icon(Icons.text_fields,
              size: 60, color: Color(int.parse('0x${readTheme.textColor}')))),
      const Expanded(
        child: SizedBox(),
      ),
      IconButton(
        onPressed: () {
          deleteTheme(readTheme.id!);
          widget.setCurrentPage(const SizedBox(height: 1));
          // setState(() {});
        },
        icon: const Icon(
          Icons.delete,
          size: 40,
        ),
      ),
    ]);
  }

  Future<String?> showColorPickerDialog(String currColor) async {
    Color pickedColor = Color(int.parse('0x$currColor'));

    await showDialog<void>(
      context: navigatorKey.currentState!.overlay!.context,
      builder: (BuildContext context) {
        return AlertDialog(
          content: SingleChildScrollView(
            child: ColorPicker(
              hexInputBar: true,
              pickerColor: pickedColor,
              onColorChanged: (Color color) {
                pickedColor = color;
              },
              pickerAreaHeightPercent: 0.8,
            ),
          ),
          actions: <Widget>[
            TextButton(
              child: const Text('Cancel'),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
            TextButton(
              child: const Text('OK'),
              onPressed: () {
                Navigator.of(context).pop(pickedColor.value.toRadixString(16));
              },
            ),
          ],
        );
      },
    );

    return pickedColor.value.toRadixString(16);
  }
}
