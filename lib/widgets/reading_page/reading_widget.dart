import 'package:dasso_reader/config/shared_preference_provider.dart';
import 'package:dasso_reader/l10n/generated/L10n.dart';
import 'package:dasso_reader/models/book_style.dart';
import 'package:dasso_reader/page/book_player/epub_player.dart';
import 'package:dasso_reader/page/reading_page.dart';
import 'package:dasso_reader/widgets/icon_and_text.dart';
import 'package:dasso_reader/widgets/reading_page/more_settings/more_settings.dart';
import 'package:dasso_reader/widgets/reading_page/widget_title.dart';
import 'package:dasso_reader/widgets/reading_page/style_widget.dart';
import 'package:flutter/material.dart';

class ReadingWidget extends StatefulWidget {
  const ReadingWidget({
    super.key,
    required this.epubPlayerKey,
    this.backgroundColor,
    this.textColor,
  });

  final GlobalKey<EpubPlayerState> epubPlayerKey;
  final Color? backgroundColor;
  final Color? textColor;

  @override
  State<ReadingWidget> createState() => _ReadingWidgetState();
}

class _ReadingWidgetState extends State<ReadingWidget> {
  BookStyle bookStyle = Prefs().bookStyle;
  bool autoScrollEnabled = false;
  double scrollSpeed = 5.0; // Default speed (1-10)

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      decoration: BoxDecoration(
        color: widget.backgroundColor ?? Theme.of(context).colorScheme.surface,
        // Remove border radius and shadow to create continuous appearance with bottom bar
      ),
      child: _buildReadingOptions(),
    );
  }

  Widget _buildReadingOptions() {
    final txtColor =
        widget.textColor ?? Theme.of(context).colorScheme.onSurface;

    return Column(
      children: [
        // Add 24px top padding for consistent spacing
        const SizedBox(height: 24.0),
        // Page Turning Method Dropdown - Clean minimal style
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 16.0),
          child: Row(
            children: [
              // Text label on the left
              Text(
                L10n.of(context).reading_page_page_turning_method,
                style: TextStyle(
                  color: txtColor,
                  fontSize: 16,
                ),
              ),
              const Spacer(),
              // Simple dropdown with no border
              DropdownButton<PageTurn>(
                value: Prefs().pageTurnStyle,
                icon: Icon(Icons.arrow_drop_down, color: txtColor),
                underline: Container(), // Remove the underline
                dropdownColor: widget.backgroundColor ??
                    Theme.of(context).colorScheme.surface,
                style: TextStyle(color: txtColor, fontSize: 16),
                onChanged: (PageTurn? value) {
                  if (value != null) {
                    Prefs().pageTurnStyle = value;
                    widget.epubPlayerKey.currentState!
                        .changePageTurnStyle(value);
                  }
                },
                items: PageTurn.values
                    .map<DropdownMenuItem<PageTurn>>((PageTurn value) {
                  return DropdownMenuItem<PageTurn>(
                    value: value,
                    child: Text(value.getLabel(context)),
                  );
                }).toList(),
              ),
            ],
          ),
        ),

        const Divider(height: 1),

        // Auto-Scroll Toggle
        SwitchListTile(
          title: Text(
            "Auto-scroll", // Hardcoded for now
            style: TextStyle(color: txtColor),
          ),
          value: autoScrollEnabled,
          activeColor: Theme.of(context).colorScheme.primary,
          onChanged: (value) {
            setState(() {
              autoScrollEnabled = value;
            });
            if (value) {
              // Start auto-scroll with current speed
              _startAutoScroll(scrollSpeed);
            } else {
              // Stop auto-scroll
              _stopAutoScroll();
            }
          },
        ),

        // Scroll Speed Slider
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          child: Row(
            children: [
              IconAndText(
                icon: const Icon(Icons.speed),
                text: "Scroll Speed", // Hardcoded for now
              ),
              Expanded(
                child: SliderTheme(
                  data: SliderThemeData(
                    trackHeight: 36.0,
                    thumbShape: RoundSliderThumbShape(
                      enabledThumbRadius: 18.0,
                      elevation: 2.0,
                    ),
                    overlayShape:
                        const RoundSliderOverlayShape(overlayRadius: 26.0),
                    trackShape: const RoundedRectSliderTrackShape(),
                    activeTrackColor: txtColor.withOpacity(0.25),
                    inactiveTrackColor: txtColor.withAlpha(25),
                    thumbColor: Colors.white,
                    overlayColor: txtColor.withAlpha(50),
                  ),
                  child: Slider(
                    value: scrollSpeed,
                    min: 1.0,
                    max: 10.0,
                    divisions: 9,
                    label: scrollSpeed.round().toString(),
                    onChanged: (value) {
                      setState(() {
                        scrollSpeed = value;
                      });
                      if (autoScrollEnabled) {
                        // Update auto-scroll speed if enabled
                        _updateScrollSpeed(value);
                      }
                    },
                  ),
                ),
              ),
            ],
          ),
        ),
        // Add bottom padding for consistent spacing
        const SizedBox(height: 24.0),
      ],
    );
  }

  // Placeholder methods that would need to be implemented in real world
  void _startAutoScroll(double speed) {
    // This would call the actual implementation in widget.epubPlayerKey.currentState
    print('Starting auto-scroll with speed: $speed');
  }

  void _stopAutoScroll() {
    // This would call the actual implementation in widget.epubPlayerKey.currentState
    print('Stopping auto-scroll');
  }

  void _updateScrollSpeed(double speed) {
    // This would call the actual implementation in widget.epubPlayerKey.currentState
    print('Updating scroll speed to: $speed');
  }
}
