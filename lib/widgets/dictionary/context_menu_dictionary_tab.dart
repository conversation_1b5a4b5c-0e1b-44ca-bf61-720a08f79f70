import 'package:dasso_reader/l10n/generated/L10n.dart';
import 'package:dasso_reader/models/dictionary/dictionary_entry.dart';
import 'package:dasso_reader/service/dictionary/dictionary_service.dart';
import 'package:dasso_reader/service/dictionary/online_dictionary_service.dart';
import 'package:dasso_reader/utils/log/common.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import 'package:pinyin/pinyin.dart';

/// A specialized dictionary component specifically for use in the context menu
/// This provides separation of concerns from the main dictionary tab
class ContextMenuDictionaryTab extends StatefulWidget {
  /// The text to look up in the dictionary
  final String text;

  const ContextMenuDictionaryTab({
    Key? key,
    required this.text,
  }) : super(key: key);

  @override
  State<ContextMenuDictionaryTab> createState() =>
      _ContextMenuDictionaryTabState();
}

class _ContextMenuDictionaryTabState extends State<ContextMenuDictionaryTab> {
  bool _isLoading = true;
  DictionaryEntry? _entry;
  bool _notFound = false;

  // Services
  final OnlineDictionaryService _onlineDictionaryService =
      OnlineDictionaryService();

  @override
  void initState() {
    super.initState();
    _initDictionary();
  }

  /// Play audio for the current word
  Future<void> _playAudio() async {
    if (_entry == null) return;

    try {
      final word = _entry!.simplified;

      // Use the online dictionary service to play pronunciation
      await _onlineDictionaryService.playPronunciation(word, context: context);
    } catch (e) {
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error playing audio: $e'),
          duration: const Duration(seconds: 2),
        ),
      );
    }
  }

  Future<void> _initDictionary() async {
    try {
      // Add a timeout to ensure we never get stuck loading
      final result = await Future.any([
        _performLookup(), // Actual lookup operation
        // 2-second timeout - short for better UX in context menu
        Future.delayed(const Duration(seconds: 2), () => false),
      ]);

      if (mounted) {
        setState(() {
          _isLoading = false;
          _notFound = result == false; // Timeout gives false
        });
      }
    } catch (e) {
      AnxLog.severe('Error initializing dictionary service: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
          _notFound = true;
        });
      }
    }
  }

  /// Perform the actual dictionary lookup
  Future<bool> _performLookup() async {
    // Pre-initialize the dictionary service
    await DictionaryService().initialize();

    // Try to lookup the word
    if (_isChinese(widget.text)) {
      _entry = await DictionaryService().lookupChinese(widget.text);

      // Check if the entry is a fallback entry (has "No definition available")
      if (_entry != null &&
          _entry!.definitions.length == 1 &&
          _entry!.definitions[0] == 'No definition available') {
        // This is a fallback entry, so we should show our custom UI
        return false;
      }
    } else {
      // For non-Chinese text, could implement English lookup here
      _entry = null;
    }

    return _entry != null;
  }

  /// Check if the text is Chinese
  bool _isChinese(String text) {
    // Simple check: if the text contains any Chinese characters
    return RegExp(r'[\u4e00-\u9fa5]').hasMatch(text);
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Center(
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 16.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Display the text being looked up
              Text(
                widget.text,
                style: Theme.of(context).textTheme.titleMedium,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 8),
              // Loading indicator with short text
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Looking up...',
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                ],
              ),
            ],
          ),
        ),
      );
    }

    // If no entry found, show a clean "not found" view
    if (_notFound || _entry == null) {
      return _buildNotFoundView();
    }

    // If we have an entry, show the dictionary view
    return _buildDictionaryView();
  }

  /// Build a clean UI for when no dictionary entry is found
  Widget _buildNotFoundView() {
    final theme = Theme.of(context);

    // Generate pinyin for the text if it's Chinese
    String pinyinText = '';
    try {
      if (_isChinese(widget.text)) {
        pinyinText = PinyinHelper.getPinyin(widget.text,
            separator: ' ', format: PinyinFormat.WITH_TONE_MARK);
      }
    } catch (e) {
      AnxLog.warning('Error generating pinyin for not found view: $e');
      pinyinText = '';
    }

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with word
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              // Word display
              Expanded(
                child: Text(
                  widget.text,
                  style: theme.textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),

              // Action buttons
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Copy button
                  IconButton(
                    icon: const Icon(Icons.copy_outlined),
                    iconSize: 20,
                    visualDensity: VisualDensity.compact,
                    onPressed: () {
                      Clipboard.setData(ClipboardData(text: widget.text));
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text(L10n.of(context).notes_page_copied),
                          duration: const Duration(seconds: 2),
                        ),
                      );
                    },
                    tooltip: L10n.of(context).common_copy,
                  ),

                  // Only show pronunciation button for Chinese text
                  if (_isChinese(widget.text))
                    IconButton(
                      icon: const Icon(Icons.volume_up),
                      iconSize: 20,
                      visualDensity: VisualDensity.compact,
                      onPressed: () async {
                        try {
                          await _onlineDictionaryService
                              .playPronunciation(widget.text, context: context);
                        } catch (e) {
                          if (!mounted) return;
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text('Error playing audio: $e'),
                              duration: const Duration(seconds: 2),
                            ),
                          );
                        }
                      },
                      tooltip: 'Pronunciation',
                    ),
                ],
              ),
            ],
          ),

          // Show pinyin if available
          if (pinyinText.isNotEmpty) ...[
            const SizedBox(height: 4),
            Text(
              pinyinText,
              style: theme.textTheme.titleMedium?.copyWith(
                fontStyle: FontStyle.italic,
                color: theme.colorScheme.primary,
              ),
            ),
          ],

          // No "No definition found" message to save space
        ],
      ),
    );
  }

  /// Build the dictionary view with the entry
  Widget _buildDictionaryView() {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header with word and actions
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              // Word display
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Text(
                          _entry!.simplified,
                          style: theme.textTheme.headlineSmall?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        if (_entry!.hskLevel != null) ...[
                          const SizedBox(width: 8),
                          _buildHskLevelBadge(_entry!.hskLevel!),
                        ],
                      ],
                    ),
                    if (_entry!.pinyin.isNotEmpty)
                      Text(
                        _entry!.formattedPinyin(),
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontStyle: FontStyle.italic,
                          color: theme.colorScheme.primary,
                        ),
                      ),
                  ],
                ),
              ),

              // Action buttons
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Copy button
                  IconButton(
                    icon: const Icon(Icons.copy_outlined),
                    iconSize: 20,
                    visualDensity: VisualDensity.compact,
                    onPressed: () {
                      Clipboard.setData(
                          ClipboardData(text: _entry!.simplified));
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text(L10n.of(context).notes_page_copied),
                          duration: const Duration(seconds: 2),
                        ),
                      );
                    },
                    tooltip: L10n.of(context).common_copy,
                  ),

                  // Pronunciation button
                  IconButton(
                    icon: const Icon(Icons.volume_up),
                    iconSize: 20,
                    visualDensity: VisualDensity.compact,
                    onPressed: _playAudio,
                    tooltip: 'Pronunciation',
                  ),
                ],
              ),
            ],
          ),
        ),

        // Definitions
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Horizontal definitions list (limited to 3)
              Padding(
                padding: const EdgeInsets.only(bottom: 4.0),
                child: Text(
                  _formatDefinitionsHorizontally(_entry!.definitions,
                      maxCount: 3),
                  style: theme.textTheme.bodyMedium,
                  overflow: TextOverflow.ellipsis,
                  maxLines: 3,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// Format definitions as a horizontal list with commas
  String _formatDefinitionsHorizontally(List<String> definitions,
      {int maxCount = 3}) {
    // Limit the number of definitions
    final limitedDefs = definitions.length > maxCount
        ? definitions.sublist(0, maxCount)
        : definitions;

    // Format each definition with a number and join with commas
    return limitedDefs.asMap().entries.map((entry) {
      return '${entry.key + 1}. ${entry.value}';
    }).join(', ');
  }

  /// Build an HSK level badge
  Widget _buildHskLevelBadge(int level) {
    // Define colors for different HSK levels
    final Map<int, Color> hskColors = {
      1: Colors.green,
      2: Colors.lightGreen,
      3: Colors.amber,
      4: Colors.orange,
      5: Colors.deepOrange,
      6: Colors.red,
    };

    final color = hskColors[level] ?? Colors.grey;

    return Tooltip(
      message: 'HSK Level $level',
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
        decoration: BoxDecoration(
          color: color.withAlpha((0.2 * 255).round()),
          border: Border.all(color: color),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Text(
          'HSK $level',
          style: TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.bold,
            color: color.withAlpha((0.8 * 255).round()),
          ),
        ),
      ),
    );
  }
}
