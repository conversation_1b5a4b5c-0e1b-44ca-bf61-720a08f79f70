import 'package:dasso_reader/config/shared_preference_provider.dart';
import 'package:dasso_reader/dao/book_note.dart';
import 'package:dasso_reader/l10n/generated/L10n.dart';
import 'package:dasso_reader/models/book_note.dart';
import 'package:dasso_reader/page/reading_page.dart';
import 'package:dasso_reader/service/translate/index.dart';
import 'package:dasso_reader/utils/haptic/haptic_feedback.dart';
import 'package:dasso_reader/utils/log/common.dart';
import 'package:dasso_reader/utils/toast/common.dart';
import 'package:dasso_reader/widgets/dictionary/context_menu_dictionary_tab.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:icons_plus/icons_plus.dart';
import 'package:pointer_interceptor/pointer_interceptor.dart';

// Constants
const List<String> notesColors = [
  '66CCFF',
  'FF0000',
  '00FF00',
  'EB3BFF',
  'FFD700'
];
const List<Map<String, dynamic>> notesType = [
  {'type': 'highlight', 'icon': AntDesign.highlight_outline},
  {'type': 'underline', 'icon': Icons.format_underline},
];

// Performance optimization: Track consecutive menu invocations
DateTime _lastMenuShownTime = DateTime.fromMillisecondsSinceEpoch(0);
bool _isRapidInvocation = false;
const int _rapidInvocationThreshold = 500; // milliseconds

/// Shows the unified context menu when text is selected
void showUnifiedContextMenu(
  BuildContext context,
  double x,
  double y,
  String dir,
  String annoContent,
  String annoCfi,
  int? annoId,
  bool footnote,
) {
  // Check if this is a rapid invocation (for performance optimization)
  final now = DateTime.now();
  final timeSinceLastShow = now.difference(_lastMenuShownTime);
  _isRapidInvocation =
      timeSinceLastShow.inMilliseconds < _rapidInvocationThreshold;
  _lastMenuShownTime = now;

  // Create animation controller for the floating menu animation with optimized timing
  final animationController = AnimationController(
    duration: _isRapidInvocation
        ? Duration.zero // No animation during rapid invocation
        : const Duration(
            milliseconds: 180), // Optimized for context menu responsiveness
    vsync: Navigator.of(context),
  );

  // Create scale and opacity animations with MD3 standard curves
  final scaleAnimation = CurvedAnimation(
    parent: animationController,
    // MD3 emphasized easing for scale
    curve: Curves.easeOutCubic,
  );

  final fadeAnimation = CurvedAnimation(
    parent: animationController,
    // MD3 standard easing for opacity
    curve: Curves.easeOutCubic,
  );

  final playerKey = epubPlayerKey.currentState!;

  // Remove any existing overlay first to prevent stacking
  playerKey.removeOverlay();

  double screenWidth = MediaQuery.of(context).size.width;
  double screenHeight = MediaQuery.of(context).size.height;

  // Calculate responsive menu width based on screen size
  double menuWidth;

  // Handle different device size classes
  if (screenWidth < 360) {
    // Small phones
    menuWidth = screenWidth * 0.95; // Almost full width
  } else if (screenWidth < 600) {
    // Standard phones
    menuWidth = screenWidth * 0.85;
  } else if (screenWidth < 960) {
    // Large phones, small tablets
    menuWidth = screenWidth * 0.65;
  } else {
    // Tablets and larger
    menuWidth = screenWidth * 0.45;
  }

  // Set min and max constraints based on device category
  if (screenWidth < 600) {
    menuWidth = menuWidth.clamp(280.0, 500.0);
  } else {
    menuWidth = menuWidth.clamp(400.0, 600.0); // Larger min/max for tablets
  }

  x *= screenWidth;
  y *= screenHeight;

  // Position the menu properly on screen
  double widgetLeft =
      x + menuWidth > screenWidth ? screenWidth - menuWidth - 20 : x;

  // Function to close the menu
  void onClose() {
    try {
      // Reverse the animation before removing the overlay
      if (!_isRapidInvocation &&
          animationController.status != AnimationStatus.dismissed) {
        animationController.reverse().then((_) {
          playerKey.webViewController
              .evaluateJavascript(source: 'clearSelection()');
          playerKey.removeOverlay();
        });
      } else {
        playerKey.webViewController
            .evaluateJavascript(source: 'clearSelection()');
        playerKey.removeOverlay();
      }
    } catch (e) {
      // Ensure overlay is removed even if JavaScript fails
      playerKey.removeOverlay();
    }
  }

  // Get current reading theme colors
  final readingBackgroundColor =
      Color(int.parse('0x${Prefs().readTheme.backgroundColor}'));
  final readingTextColor = Color(int.parse('0x${Prefs().readTheme.textColor}'));

  // Create the overlay entry with the unified menu
  playerKey.contextMenuEntry = OverlayEntry(
    builder: (context) {
      // Get the current keyboard visibility and safe area
      final bottomInset = MediaQuery.of(context).viewInsets.bottom;
      final isKeyboardVisible = bottomInset > 0;
      final safeArea = MediaQuery.of(context).padding;

      // Calculate estimated menu height (approximate for positioning decisions)
      // This is a rough estimate - actual height will depend on content
      final estimatedMenuHeight = annoContent.length > 50 ? 350.0 : 250.0;

      // Determine if menu should appear above or below selection
      // If selection is in the top half of screen, show menu below it
      // If selection is in the bottom half, show menu above it
      final bool showBelow = y < (screenHeight / 2);

      // Position variables - we'll use either top or bottom based on position
      double? top;
      double? bottom;

      if (showBelow) {
        // Show menu below selection
        top = y + 50; // Add some space below the selection

        // Check if menu would go off bottom of screen
        if (top + estimatedMenuHeight >
            screenHeight -
                safeArea.bottom -
                (isKeyboardVisible ? bottomInset : 0)) {
          // If it would go off bottom, flip to show above instead
          top = null;
          bottom = screenHeight - y + 50;
        }
      } else {
        // Show menu above selection
        bottom = screenHeight - y + 50;

        // Check if menu would go off top of screen
        if (bottom + estimatedMenuHeight > screenHeight - safeArea.top) {
          // If it would go off top, flip to show below instead
          bottom = null;
          top = y + 50;
        }
      }

      // Final safety check - if menu still doesn't fit, center it on screen
      if ((top != null &&
              top + estimatedMenuHeight >
                  screenHeight -
                      safeArea.bottom -
                      (isKeyboardVisible ? bottomInset : 0)) ||
          (bottom != null &&
              bottom + estimatedMenuHeight > screenHeight - safeArea.top)) {
        // Center the menu on screen as a fallback
        top = (screenHeight - estimatedMenuHeight) / 2;
        bottom = null;
      }

      // Ensure menu is visible if keyboard is shown
      if (isKeyboardVisible &&
          top != null &&
          top + estimatedMenuHeight > screenHeight - bottomInset) {
        // Adjust to be above keyboard
        top = screenHeight - bottomInset - estimatedMenuHeight - 20;
      }

      return StatefulBuilder(
        builder: (context, setState) {
          final ValueNotifier<bool> showColorPalette =
              ValueNotifier<bool>(false);
          final ValueNotifier<bool> showTranslation =
              ValueNotifier<bool>(false);
          final ValueNotifier<String?> translatedText =
              ValueNotifier<String?>(null);
          final ValueNotifier<bool> isTranslating = ValueNotifier<bool>(false);

          void toggleTranslation() {
            showTranslation.value = !showTranslation.value;
            if (showTranslation.value && translatedText.value == null) {
              // Start translation
              isTranslating.value = true;
              translateText(annoContent).then((result) {
                translatedText.value = result;
                isTranslating.value = false;
              }).catchError((e) {
                translatedText.value = L10n.of(context).translate_error;
                isTranslating.value = false;
              });
            }
          }

          return Stack(
            children: [
              // Invisible full-screen button to detect taps outside the menu
              Positioned.fill(
                child: GestureDetector(
                  behavior: HitTestBehavior.opaque,
                  onTap: onClose,
                  child: Container(
                    color: Colors.transparent,
                  ),
                ),
              ),
              // Context-Aware Floating Menu with scale and fade animation
              Positioned(
                left: widgetLeft,
                top: top,
                bottom: bottom,
                child: AnimatedBuilder(
                  animation: animationController,
                  builder: (context, child) {
                    return Transform.scale(
                      // MD3 standard scale values (0.95 to 1.0)
                      scale: _isRapidInvocation
                          ? 1.0
                          : 0.95 + (0.05 * scaleAnimation.value),
                      child: Opacity(
                        opacity: _isRapidInvocation ? 1.0 : fadeAnimation.value,
                        child: child,
                      ),
                    );
                  },
                  child: PointerInterceptor(
                    child: Container(
                      width: menuWidth,
                      constraints: BoxConstraints(
                        maxHeight: isKeyboardVisible
                            ? MediaQuery.of(context).size.height -
                                bottomInset -
                                40
                            : MediaQuery.of(context).size.height * 0.7,
                      ),
                      decoration: BoxDecoration(
                        color: readingBackgroundColor,
                        borderRadius: BorderRadius.circular(16),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withAlpha(80),
                            blurRadius: 0.5,
                            spreadRadius: 0,
                            offset: const Offset(0, 0),
                          ),
                          BoxShadow(
                            color: Colors.black.withAlpha(50),
                            blurRadius: 4,
                            spreadRadius: -2,
                            offset: const Offset(0, 0),
                          ),
                        ],
                      ),
                      child: Material(
                        color: Colors.transparent,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(16),
                        ),
                        clipBehavior: Clip.antiAlias,
                        child: DefaultTextStyle(
                          style: TextStyle(color: readingTextColor),
                          child: Theme(
                            data: Theme.of(context).copyWith(
                              iconTheme: IconThemeData(color: readingTextColor),
                              dividerColor: readingTextColor.withOpacity(0.2),
                              colorScheme: ColorScheme.fromSeed(
                                seedColor:
                                    Theme.of(context).colorScheme.primary,
                                brightness:
                                    ThemeData.estimateBrightnessForColor(
                                        readingBackgroundColor),
                                primary: Theme.of(context).colorScheme.primary,
                                onPrimary:
                                    Theme.of(context).colorScheme.onPrimary,
                                secondary:
                                    Theme.of(context).colorScheme.secondary,
                                surface: readingBackgroundColor,
                                onSurface: readingTextColor,
                                background: readingBackgroundColor,
                                onBackground: readingTextColor,
                                surfaceContainerLow:
                                    readingBackgroundColor.withOpacity(0.9),
                                outlineVariant:
                                    readingTextColor.withOpacity(0.2),
                              ),
                            ),
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                // Action buttons tray
                                Container(
                                  decoration: BoxDecoration(
                                    border: Border(
                                      bottom: BorderSide(
                                        color:
                                            readingTextColor.withOpacity(0.2),
                                        width: 0.5,
                                      ),
                                    ),
                                  ),
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 8,
                                    vertical: 8,
                                  ),
                                  child: ValueListenableBuilder<bool>(
                                    valueListenable: showColorPalette,
                                    builder: (context, isShowingColors, _) {
                                      // Simplified animation for color palette
                                      return _isRapidInvocation
                                          ? (isShowingColors
                                              ? _buildColorSelector(
                                                  context,
                                                  () {
                                                    showColorPalette.value =
                                                        false;
                                                  },
                                                  annoContent,
                                                  annoCfi,
                                                  onClose,
                                                )
                                              : Row(
                                                  mainAxisAlignment:
                                                      MainAxisAlignment
                                                          .spaceAround,
                                                  key: const ValueKey(
                                                      'mainActions'),
                                                  children: _buildActionButtons(
                                                      context,
                                                      annoId,
                                                      footnote,
                                                      annoContent,
                                                      annoCfi,
                                                      onClose,
                                                      showColorPalette),
                                                ))
                                          : AnimatedSwitcher(
                                              // Optimized timing for context menu responsiveness
                                              duration: const Duration(
                                                  milliseconds: 180),
                                              // MD3 standard easing
                                              switchInCurve:
                                                  Curves.easeOutCubic,
                                              switchOutCurve:
                                                  Curves.easeInCubic,
                                              transitionBuilder:
                                                  (child, animation) {
                                                // MD3 container transform pattern
                                                final curvedAnimation =
                                                    CurvedAnimation(
                                                  parent: animation,
                                                  curve: Curves.easeOutCubic,
                                                );

                                                return SlideTransition(
                                                  position: Tween<Offset>(
                                                    begin:
                                                        const Offset(-0.3, 0),
                                                    end: Offset.zero,
                                                  ).animate(curvedAnimation),
                                                  child: FadeTransition(
                                                    opacity: curvedAnimation,
                                                    child: child,
                                                  ),
                                                );
                                              },
                                              child: isShowingColors
                                                  ? _buildColorSelector(
                                                      context,
                                                      () {
                                                        showColorPalette.value =
                                                            false;
                                                      },
                                                      annoContent,
                                                      annoCfi,
                                                      onClose,
                                                    )
                                                  : Row(
                                                      mainAxisAlignment:
                                                          MainAxisAlignment
                                                              .spaceAround,
                                                      key: const ValueKey(
                                                          'mainActions'),
                                                      children: [
                                                        // Delete button (only for existing annotations)
                                                        if (annoId != null)
                                                          CircleIconButton(
                                                            icon: Icons
                                                                .delete_outline,
                                                            semanticsLabel: L10n
                                                                    .of(context)
                                                                .common_delete,
                                                            onTap: () {
                                                              // Haptic feedback for delete action
                                                              AnxHapticFeedback
                                                                  .mediumImpact();
                                                              deleteBookNoteById(
                                                                  annoId);
                                                              epubPlayerKey
                                                                  .currentState!
                                                                  .removeAnnotation(
                                                                      annoCfi);
                                                              AnxToast.show(L10n
                                                                      .of(context)
                                                                  .common_delete);
                                                              onClose();
                                                            },
                                                          ),

                                                        // Underline button (only for new annotations)
                                                        if (annoId == null)
                                                          CircleIconButton(
                                                            icon: Icons
                                                                .format_underline,
                                                            onTap: !footnote
                                                                ? () {
                                                                    // Haptic feedback for underline action
                                                                    AnxHapticFeedback
                                                                        .selectionClick();
                                                                    onAnnotationSelected(
                                                                      'underline',
                                                                      Prefs()
                                                                          .annotationColor,
                                                                      annoContent,
                                                                      annoCfi,
                                                                      onClose,
                                                                    );
                                                                  }
                                                                : null,
                                                          ),

                                                        // Highlight button (only for new annotations)
                                                        if (annoId == null)
                                                          CircleIconButton(
                                                            icon: Icons.brush,
                                                            onTap: !footnote
                                                                ? () {
                                                                    // Haptic feedback for highlight action
                                                                    AnxHapticFeedback
                                                                        .selectionClick();
                                                                    onAnnotationSelected(
                                                                      'highlight',
                                                                      Prefs()
                                                                          .annotationColor,
                                                                      annoContent,
                                                                      annoCfi,
                                                                      onClose,
                                                                    );
                                                                  }
                                                                : null,
                                                          ),

                                                        // Color button (only for new annotations)
                                                        if (annoId == null)
                                                          CircleIconButton(
                                                            icon: Icons
                                                                .color_lens,
                                                            showColorIndicator:
                                                                true,
                                                            colorValue: Prefs()
                                                                .annotationColor,
                                                            onTap: !footnote
                                                                ? () {
                                                                    // Haptic feedback for color palette
                                                                    AnxHapticFeedback
                                                                        .lightImpact();
                                                                    showColorPalette
                                                                            .value =
                                                                        true;
                                                                  }
                                                                : null,
                                                          ),

                                                        // Note button
                                                        CircleIconButton(
                                                          icon: Icons
                                                              .note_alt_outlined,
                                                          semanticsLabel: L10n
                                                                  .of(context)
                                                              .context_menu_add_note_tips,
                                                          onTap: !footnote
                                                              ? () {
                                                                  // Haptic feedback for note editor
                                                                  AnxHapticFeedback
                                                                      .lightImpact();
                                                                  showNoteEditor(
                                                                    context,
                                                                    annoId,
                                                                    annoContent,
                                                                    annoCfi,
                                                                    onClose,
                                                                  );
                                                                }
                                                              : null,
                                                        ),

                                                        // Removed redundant Copy button

                                                        // AI chat button
                                                        CircleIconButton(
                                                          icon: EvaIcons
                                                              .message_circle_outline,
                                                          semanticsLabel:
                                                              "AI Chat",
                                                          onTap: () {
                                                            // Haptic feedback for AI chat
                                                            AnxHapticFeedback
                                                                .mediumImpact();
                                                            onClose();
                                                            final key =
                                                                readingPageKey
                                                                    .currentState;
                                                            if (key != null) {
                                                              key.showAiChat(
                                                                content:
                                                                    annoContent,
                                                                sendImmediate:
                                                                    false,
                                                              );
                                                              key
                                                                  .aiChatKey
                                                                  .currentState
                                                                  ?.inputController
                                                                  .text = annoContent;
                                                            }
                                                          },
                                                        ),
                                                      ],
                                                    ),
                                            );
                                    },
                                  ),
                                ),

                                // If annotation exists and has notes, show the optimized note section
                                if (annoId != null)
                                  FutureBuilder<BookNote?>(
                                    future: selectBookNoteById(annoId),
                                    builder: (context, snapshot) {
                                      if (snapshot.connectionState ==
                                              ConnectionState.done &&
                                          snapshot.hasData &&
                                          snapshot.data?.readerNote != null &&
                                          snapshot
                                              .data!.readerNote!.isNotEmpty) {
                                        final noteText =
                                            snapshot.data!.readerNote!;

                                        return Column(
                                          children: [
                                            // Thinner divider
                                            // MD3 divider with proper color token
                                            Divider(
                                              color: readingTextColor
                                                  .withOpacity(0.2),
                                              height: 0.5,
                                              thickness: 0.5,
                                            ),
                                            // Simple container with subtle background (non-interactive)
                                            Container(
                                              margin:
                                                  const EdgeInsets.symmetric(
                                                horizontal: 12.0,
                                                vertical: 4.0,
                                              ),
                                              padding:
                                                  const EdgeInsets.symmetric(
                                                horizontal: 8.0,
                                                vertical: 4.0,
                                              ),
                                              decoration: BoxDecoration(
                                                // MD3 color token for subtle container backgrounds
                                                color: readingBackgroundColor
                                                    .withOpacity(0.8),
                                                borderRadius:
                                                    BorderRadius.circular(4),
                                              ),
                                              child: Row(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  // Smaller icon with less spacing
                                                  Padding(
                                                    padding: const EdgeInsets
                                                        .only(
                                                        top:
                                                            2.0), // Align with text
                                                    child: Icon(
                                                      Icons.note_alt,
                                                      size: 14, // Smaller icon
                                                      // MD3 color token for secondary icons
                                                      color: readingTextColor,
                                                    ),
                                                  ),
                                                  const SizedBox(
                                                      width:
                                                          4), // Reduced spacing
                                                  // Note text with optimized typography
                                                  Expanded(
                                                    child: Directionality(
                                                      textDirection: Localizations
                                                                          .localeOf(
                                                                              context)
                                                                      .languageCode ==
                                                                  'ar' ||
                                                              Localizations.localeOf(
                                                                          context)
                                                                      .languageCode ==
                                                                  'he'
                                                          ? TextDirection.rtl
                                                          : TextDirection.ltr,
                                                      child: Text(
                                                        noteText,
                                                        // MD3 typography - bodySmall
                                                        style: Theme.of(context)
                                                            .textTheme
                                                            .bodySmall
                                                            ?.copyWith(
                                                              height:
                                                                  1.2, // Maintain tighter line height
                                                              color:
                                                                  readingTextColor,
                                                            ),
                                                        maxLines:
                                                            2, // Show max 2 lines by default
                                                        overflow: TextOverflow
                                                            .ellipsis,
                                                      ),
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ],
                                        );
                                      }
                                      return const SizedBox.shrink();
                                    },
                                  ),

                                // Removed duplicate selected text content to save vertical space

                                // Dictionary tab
                                // MD3 divider with proper color token
                                Divider(
                                  color: readingTextColor.withOpacity(0.2),
                                  height: 0.5,
                                  thickness: 0.5,
                                ),
                                Padding(
                                  padding: const EdgeInsets.all(8.0),
                                  child: ContextMenuDictionaryTab(
                                    text: annoContent,
                                  ),
                                ),

                                // Translation widget (always visible)
                                _buildInlineTranslationWidget(
                                    context, annoContent),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          );
        },
      );
    },
  );

  // Add haptic feedback when menu appears
  AnxHapticFeedback.lightImpact();

  // Add the overlay to the player
  Overlay.of(context).insert(playerKey.contextMenuEntry!);

  // Start the animation
  if (!_isRapidInvocation) {
    animationController.forward();
  }
}

/// Creates an annotation with the selected type and color
Future<void> onAnnotationSelected(
  String type,
  String color,
  String content,
  String cfi,
  Function() onClose,
) async {
  Prefs().annotationType = type;
  Prefs().annotationColor = color;

  BookNote bookNote = BookNote(
    id: null,
    bookId: epubPlayerKey.currentState!.widget.book.id,
    content: content,
    cfi: cfi,
    chapter: epubPlayerKey.currentState!.chapterTitle,
    type: type,
    color: color,
    createTime: DateTime.now(),
    updateTime: DateTime.now(),
  );

  int noteId = await insertBookNote(bookNote);
  bookNote.setId(noteId);
  epubPlayerKey.currentState!.addAnnotation(bookNote);
  onClose();
}

/// Shows a modal dialog to edit reader notes
void showNoteEditor(
  BuildContext context,
  int? noteId,
  String content,
  String cfi,
  Function() onClose,
) async {
  final textController = TextEditingController();
  BookNote? existingNote;

  // If there's an existing note, load it
  if (noteId != null) {
    existingNote = await selectBookNoteById(noteId);
    if (existingNote != null && existingNote.readerNote != null) {
      textController.text = existingNote.readerNote!;
    }
  }
  // Otherwise create a new annotation first
  else {
    // Create a new annotation first
    BookNote bookNote = BookNote(
      id: null,
      bookId: epubPlayerKey.currentState!.widget.book.id,
      content: content,
      cfi: cfi,
      chapter: epubPlayerKey.currentState!.chapterTitle,
      type: Prefs().annotationType,
      color: Prefs().annotationColor,
      readerNote: '',
      createTime: DateTime.now(),
      updateTime: DateTime.now(),
    );

    noteId = await insertBookNote(bookNote);
    bookNote.setId(noteId);
    epubPlayerKey.currentState!.addAnnotation(bookNote);
  }

  // Capture localized strings before async gap
  final addNoteTips = L10n.of(context).context_menu_add_note_tips;
  final cancelText = L10n.of(context).common_cancel;
  final saveText = L10n.of(context).common_save;

  // Get the navigator context before closing the menu
  final navigatorContext = Navigator.of(context).context;

  // Close the context menu
  onClose();

  // Use a more reliable method to show the dialog after the context menu is closed
  WidgetsBinding.instance.addPostFrameCallback((_) {
    showDialog(
      context: navigatorContext,
      barrierDismissible: true,
      builder: (dialogContext) => AlertDialog(
        title: Text(
          addNoteTips, // Use captured string
          // MD3 typography - titleLarge for dialog titles
          style: Theme.of(dialogContext).textTheme.titleLarge,
        ),
        content: TextField(
          controller: textController,
          autofocus: true,
          maxLines: 5,
          minLines: 3,
          decoration: InputDecoration(
            hintText: addNoteTips, // Use captured string
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.0),
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(dialogContext).pop();
            },
            child: Text(
              cancelText, // Use captured string
              // MD3 typography - labelLarge for button text
              style: Theme.of(dialogContext).textTheme.labelLarge,
            ),
          ),
          TextButton(
            onPressed: () async {
              if (noteId != null) {
                // Capture context for async operations
                final currentContext = dialogContext;
                final savedMessage = L10n.of(currentContext).common_saved;

                BookNote note = await selectBookNoteById(noteId);
                note.readerNote = textController.text.trim();
                await updateBookNoteById(note);

                // Check if context is still valid
                if (currentContext.mounted) {
                  AnxToast.show(savedMessage);
                  Navigator.of(currentContext).pop();
                }
              }
            },
            child: Text(
              saveText, // Use captured string
              // MD3 typography - labelLarge for button text
              style: Theme.of(dialogContext).textTheme.labelLarge,
            ),
          ),
        ],
      ),
    );
  });
}

/// Builds the color selector row that slides in when color button is tapped
Widget _buildColorSelector(
  BuildContext context,
  VoidCallback onBack,
  String content,
  String cfi,
  Function() onClose,
) {
  // Get reading theme colors
  final readingTextColor = Color(int.parse('0x${Prefs().readTheme.textColor}'));
  final readingBackgroundColor =
      Color(int.parse('0x${Prefs().readTheme.backgroundColor}'));

  return Row(
    key: const ValueKey('colorSelector'),
    children: [
      // Back button
      CircleIconButton(
        icon: Icons.arrow_back,
        onTap: onBack,
      ),
      const SizedBox(width: 8),
      // Color options
      for (String color in notesColors)
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 4),
          child: InkWell(
            borderRadius: BorderRadius.circular(16),
            onTap: () {
              Prefs().annotationColor = color;
              // Create annotation with selected color
              onAnnotationSelected(
                Prefs().annotationType,
                color,
                content,
                cfi,
                onClose,
              );
            },
            child: Container(
              width: 30,
              height: 30,
              decoration: BoxDecoration(
                color: Color(int.parse('0xFF$color')),
                shape: BoxShape.circle,
                // Use simple border for selected state
                border: Border.all(
                  color: Prefs().annotationColor == color
                      ? Theme.of(context).colorScheme.primary
                      : Colors.transparent,
                  width: 2,
                ),
              ),
            ),
          ),
        ),
    ],
  );
}

/// Helper method to build the action buttons
List<Widget> _buildActionButtons(
  BuildContext context,
  int? annoId,
  bool footnote,
  String annoContent,
  String annoCfi,
  Function() onClose,
  ValueNotifier<bool> showColorPalette,
) {
  return [
    // Delete button (only for existing annotations)
    if (annoId != null)
      CircleIconButton(
        icon: Icons.delete_outline,
        semanticsLabel: L10n.of(context).common_delete,
        onTap: () {
          // Haptic feedback for delete action
          AnxHapticFeedback.mediumImpact();
          deleteBookNoteById(annoId);
          epubPlayerKey.currentState!.removeAnnotation(annoCfi);
          AnxToast.show(L10n.of(context).common_delete);
          onClose();
        },
      ),

    // Underline button (only for new annotations)
    if (annoId == null)
      CircleIconButton(
        icon: Icons.format_underline,
        onTap: !footnote
            ? () {
                // Haptic feedback for underline action
                AnxHapticFeedback.selectionClick();
                onAnnotationSelected(
                  'underline',
                  Prefs().annotationColor,
                  annoContent,
                  annoCfi,
                  onClose,
                );
              }
            : null,
      ),

    // Highlight button (only for new annotations)
    if (annoId == null)
      CircleIconButton(
        icon: Icons.brush,
        onTap: !footnote
            ? () {
                // Haptic feedback for highlight action
                AnxHapticFeedback.selectionClick();
                onAnnotationSelected(
                  'highlight',
                  Prefs().annotationColor,
                  annoContent,
                  annoCfi,
                  onClose,
                );
              }
            : null,
      ),

    // Color button (only for new annotations)
    if (annoId == null)
      CircleIconButton(
        icon: Icons.color_lens,
        showColorIndicator: true,
        colorValue: Prefs().annotationColor,
        onTap: !footnote
            ? () {
                // Haptic feedback for color palette
                AnxHapticFeedback.lightImpact();
                showColorPalette.value = true;
              }
            : null,
      ),

    // Note button
    CircleIconButton(
      icon: Icons.note_alt_outlined,
      semanticsLabel: L10n.of(context).context_menu_add_note_tips,
      onTap: !footnote
          ? () {
              // Haptic feedback for note editor
              AnxHapticFeedback.lightImpact();
              showNoteEditor(
                context,
                annoId,
                annoContent,
                annoCfi,
                onClose,
              );
            }
          : null,
    ),

    // Removed redundant Copy button

    // AI chat button
    CircleIconButton(
      icon: EvaIcons.message_circle_outline,
      semanticsLabel: "AI Chat",
      onTap: () {
        // Haptic feedback for AI chat
        AnxHapticFeedback.mediumImpact();
        onClose();
        final key = readingPageKey.currentState;
        if (key != null) {
          key.showAiChat(
            content: annoContent,
            sendImmediate: false,
          );
          key.aiChatKey.currentState?.inputController.text = annoContent;
        }
      },
    ),
  ];
}

/// Builds a simplified translation widget that displays under the selected text
/// with improved loading state that shows language dropdown immediately
Widget _buildInlineTranslationWidget(BuildContext context, String text) {
  // Use a stateful widget to properly manage the translation state
  return _TranslationWidget(text: text);
}

/// A stateful widget for the translation section to properly manage state
class _TranslationWidget extends StatefulWidget {
  final String text;

  const _TranslationWidget({required this.text});

  @override
  State<_TranslationWidget> createState() => _TranslationWidgetState();
}

class _TranslationWidgetState extends State<_TranslationWidget> {
  String? translatedText;
  bool isLoading = true;
  late LangList currentLanguage;

  @override
  void initState() {
    super.initState();
    // Store current language to detect changes
    currentLanguage = Prefs().translateTo;
    _loadTranslation();

    // Listen for language changes
    Prefs().addListener(_onPrefsChanged);
  }

  @override
  void dispose() {
    // Remove listener when widget is disposed
    Prefs().removeListener(_onPrefsChanged);
    super.dispose();
  }

  void _onPrefsChanged() {
    // Check if the translation language has changed
    final newLanguage = Prefs().translateTo;
    if (newLanguage != currentLanguage) {
      currentLanguage = newLanguage;
      // Show loading state and reload translation
      setState(() {
        isLoading = true;
      });
      _loadTranslation();
    }
  }

  Future<void> _loadTranslation() async {
    try {
      final result = await translateText(widget.text);
      if (mounted) {
        setState(() {
          translatedText = result;
          isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          translatedText = L10n.of(context).translate_error;
          isLoading = false;
        });
      }
    }
  }

  void _copyToClipboard() {
    if (translatedText != null) {
      // Copy text to clipboard - this is instantaneous
      Clipboard.setData(ClipboardData(text: translatedText!));
      AnxToast.show(L10n.of(context).notes_page_copied);
      HapticFeedback.lightImpact();
    }
  }

  @override
  Widget build(BuildContext context) {
    // Get reading theme colors
    final readingTextColor =
        Color(int.parse('0x${Prefs().readTheme.textColor}'));
    final readingBackgroundColor =
        Color(int.parse('0x${Prefs().readTheme.backgroundColor}'));

    return Padding(
      padding: const EdgeInsets.fromLTRB(16.0, 0.0, 16.0, 4.0),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Divider is always visible
          // MD3 divider with proper color token
          Divider(
            color: readingTextColor.withOpacity(0.2),
            height: 0.5,
            thickness: 0.5,
          ),
          Container(
            padding:
                const EdgeInsets.symmetric(horizontal: 10.0, vertical: 6.0),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                // Translated text area with loading state
                Expanded(
                  child: isLoading
                      ? const SizedBox(
                          height: 24, // Fixed height to prevent layout shifts
                          child: Center(
                            child: SizedBox(
                              width: 18,
                              height: 18,
                              child:
                                  CircularProgressIndicator(strokeWidth: 2.0),
                            ),
                          ),
                        )
                      : translatedText == null
                          ? const SizedBox.shrink()
                          : Directionality(
                              textDirection: Localizations.localeOf(context)
                                              .languageCode ==
                                          'ar' ||
                                      Localizations.localeOf(context)
                                              .languageCode ==
                                          'he'
                                  ? TextDirection.rtl
                                  : TextDirection.ltr,
                              child: Text(
                                translatedText!,
                                // MD3 typography - bodyMedium
                                style: Theme.of(context)
                                    .textTheme
                                    .bodyMedium
                                    ?.copyWith(
                                      // Responsive font size based on screen width
                                      fontSize:
                                          MediaQuery.of(context).size.width <
                                                  360
                                              ? 14
                                              : null,
                                      color: readingTextColor,
                                    ),
                              ),
                            ),
                ),
                // Controls on the right - always visible
                const SizedBox(width: 4.0),
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Copy button - only visible when translation is complete
                    isLoading || translatedText == null
                        ? const SizedBox(
                            width: 20) // Placeholder to maintain layout
                        : IconButton(
                            icon: Icon(
                              Icons.copy_outlined,
                              size: 18,
                              color: readingTextColor,
                            ),
                            onPressed: _copyToClipboard,
                            padding: EdgeInsets.zero,
                            constraints: const BoxConstraints(),
                          ),
                    const SizedBox(width: 2.0),
                    // Language dropdown - always visible
                    _buildCompactLanguageDropdown(context),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

/// Builds a compact language dropdown showing only the destination language with flag
Widget _buildCompactLanguageDropdown(BuildContext context) {
  final MenuController menuController = MenuController();
  final currentLang = Prefs().translateTo;

  // Get reading theme colors
  final readingTextColor = Color(int.parse('0x${Prefs().readTheme.textColor}'));
  final readingBackgroundColor =
      Color(int.parse('0x${Prefs().readTheme.backgroundColor}'));

  // Map of language codes to flag emojis (simplified for example)
  final Map<String, String> flagEmojis = {
    'en': '🇬🇧',
    'zh-CN': '🇨🇳',
    'zh-TW': '🇹🇼',
    'es': '🇪🇸',
    'fr': '🇫🇷',
    'de': '🇩🇪',
    'ja': '🇯🇵',
    'ko': '🇰🇷',
    'ru': '🇷🇺',
    'ar': '🇸🇦',
    'bg': '🇧🇬',
    'ca': '🇪🇸',
    'hr': '🇭🇷',
    'cs': '🇨🇿',
    'da': '🇩🇰',
    'nl': '🇳🇱',
    'fi': '🇫🇮',
    'el': '🇬🇷',
    'hi': '🇮🇳',
    'hu': '🇭🇺',
    'id': '🇮🇩',
    'it': '🇮🇹',
    'ms': '🇲🇾',
    'mt': '🇲🇹',
    'nb': '🇳🇴',
    'pl': '🇵🇱',
    'pt': '🇵🇹',
    'ro': '🇷🇴',
    'sk': '🇸🇰',
    'sl': '🇸🇮',
    'sv': '🇸🇪',
    'ta': '🇮🇳',
    'te': '🇮🇳',
    'th': '🇹🇭',
    'tr': '🇹🇷',
    'uk': '🇺🇦',
    'vi': '🇻🇳'
  };

  // Get flag for current language or default to empty string
  String getFlag(String code) => flagEmojis[code] ?? '';

  return MenuAnchor(
    style: MenuStyle(
      backgroundColor: WidgetStateProperty.all(
        readingBackgroundColor,
      ),
      maximumSize: WidgetStateProperty.all(const Size(200, 140)),
    ),
    controller: menuController,
    alignmentOffset: const Offset(-50, 0), // Shift the menu to the left
    menuChildren: [
      // Create a ListView with ConstrainedBox to make it scrollable but contained
      ConstrainedBox(
        constraints: const BoxConstraints(maxHeight: 140),
        child: DefaultTextStyle(
          style: TextStyle(color: readingTextColor),
          child: SingleChildScrollView(
            // Use a dedicated ScrollController to avoid conflicts with PrimaryScrollController
            controller: ScrollController(),
            // Disable automatic use of PrimaryScrollController
            primary: false,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: LangList.values.map((lang) {
                // Skip Auto language option
                if (lang == LangList.auto) return const SizedBox.shrink();

                return MenuItemButton(
                  onPressed: () {
                    Prefs().translateTo = lang;
                    menuController.close();
                  },
                  style: MenuItemButton.styleFrom(
                    minimumSize: const Size.fromHeight(44),
                    foregroundColor: readingTextColor,
                  ),
                  child: Row(
                    children: [
                      Text(getFlag(lang.code),
                          style: const TextStyle(fontSize: 16)),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          lang.getNative(context),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                );
              }).toList(),
            ),
          ),
        ),
      ),
    ],
    builder: (context, controller, child) {
      return InkWell(
        onTap: () {
          if (controller.isOpen) {
            controller.close();
          } else {
            controller.open();
          }
        },
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 6.0, vertical: 3.0),
          decoration: BoxDecoration(
            border: Border.all(color: readingTextColor.withOpacity(0.2)),
            borderRadius: BorderRadius.circular(16),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(getFlag(currentLang.code),
                  style: const TextStyle(fontSize: 16)),
              const SizedBox(width: 2),
              // Only show language code instead of full name
              Text(
                currentLang.code.split('-')[0].toUpperCase(),
                style: TextStyle(color: readingTextColor),
              ),
              Icon(
                Icons.arrow_drop_down,
                size: 16,
                color: readingTextColor,
              ),
            ],
          ),
        ),
      );
    },
  );
}

/// A custom circular icon button widget for consistent styling
/// A Material Design 3 compliant circular icon button with state layers
class CircleIconButton extends StatelessWidget {
  final IconData icon;
  final VoidCallback? onTap;
  final bool showColorIndicator;
  final String? colorValue;
  final String? semanticsLabel;

  const CircleIconButton({
    super.key,
    required this.icon,
    this.onTap,
    this.showColorIndicator = false,
    this.colorValue,
    this.semanticsLabel,
  });

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final isEnabled = onTap != null;
    final screenWidth = MediaQuery.of(context).size.width;

    // Get reading theme colors
    final readingTextColor =
        Color(int.parse('0x${Prefs().readTheme.textColor}'));

    // Adjust sizes based on screen size
    final buttonSize = screenWidth < 360 ? 32.0 : 36.0;
    final iconSize = screenWidth < 360 ? 18.0 : 20.0;

    // MD3 state layer opacities
    const hoverOpacity = 0.08;
    const focusOpacity = 0.12;
    const pressedOpacity = 0.12;

    // Primary color for the button - using solid colors
    final buttonColor =
        isEnabled ? colorScheme.primary : readingTextColor.withAlpha(102);

    return Semantics(
      button: true,
      enabled: isEnabled,
      label: semanticsLabel,
      child: Material(
        color: Colors.transparent,
        shape: CircleBorder(),
        clipBehavior: Clip.antiAlias,
        child: InkWell(
          onTap: onTap,
          // MD3 state layers with proper colors
          hoverColor: buttonColor.withAlpha((hoverOpacity * 255).round()),
          focusColor: buttonColor.withAlpha((focusOpacity * 255).round()),
          splashColor: buttonColor.withAlpha((pressedOpacity * 255).round()),
          highlightColor:
              buttonColor.withAlpha(((pressedOpacity / 2) * 255).round()),
          // MD3 motion system timing
          splashFactory: InkRipple.splashFactory,
          child: Ink(
            width: buttonSize,
            height: buttonSize,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: isEnabled
                  ? Colors.transparent
                  : readingTextColor.withAlpha(25),
            ),
            child: Stack(
              alignment: Alignment.center,
              children: [
                Icon(
                  icon,
                  size: iconSize,
                  color: buttonColor,
                ),
                if (showColorIndicator && colorValue != null)
                  Positioned(
                    bottom: 3,
                    right: 3,
                    child: Container(
                      width: 10,
                      height: 10,
                      decoration: BoxDecoration(
                        color: Color(int.parse('0xFF$colorValue')),
                        shape: BoxShape.circle,
                        // Simple border to show color indicator
                        border: Border.all(
                          color: Theme.of(context).colorScheme.surface,
                          width: 1,
                        ),
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
