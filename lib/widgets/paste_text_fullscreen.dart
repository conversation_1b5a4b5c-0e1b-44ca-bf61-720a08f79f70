import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// A full-screen modal for pasting text content with a professional MD3 design.
class PasteTextFullScreen extends StatefulWidget {
  /// Callback when user saves the pasted text
  final void Function(String title, String content) onSave;

  /// Whether to automatically paste from clipboard when opened
  final bool autoPasteFromClipboard;

  const PasteTextFullScreen({
    Key? key,
    required this.onSave,
    this.autoPasteFromClipboard = true,
  }) : super(key: key);

  /// Shows the paste text modal with a right-to-left slide transition.
  static Future<void> show(
    BuildContext context, {
    required void Function(String title, String content) onSave,
    bool autoPasteFromClipboard = true,
  }) async {
    await Navigator.of(context).push(
      SlidePageRoute(
        builder: (context) => PasteTextFullScreen(
          onSave: onSave,
          autoPasteFromClipboard: autoPasteFromClipboard,
        ),
      ),
    );
  }

  @override
  State<PasteTextFullScreen> createState() => _PasteTextFullScreenState();
}

/// Custom route that slides from right to left
class SlidePageRoute<T> extends PageRoute<T> {
  final WidgetBuilder builder;

  SlidePageRoute({required this.builder}) : super(fullscreenDialog: true);

  @override
  bool get opaque => false;

  @override
  bool get barrierDismissible => false;

  @override
  Duration get transitionDuration => const Duration(milliseconds: 300);

  @override
  bool get maintainState => true;

  @override
  Color get barrierColor => Colors.black54;

  @override
  Widget buildTransitions(
    BuildContext context,
    Animation<double> animation,
    Animation<double> secondaryAnimation,
    Widget child,
  ) {
    const begin = Offset(1.0, 0.0);
    const end = Offset.zero;
    const curve = Curves.ease;

    var tween = Tween(begin: begin, end: end).chain(CurveTween(curve: curve));
    var offsetAnimation = animation.drive(tween);

    return SlideTransition(position: offsetAnimation, child: child);
  }

  @override
  Widget buildPage(
    BuildContext context,
    Animation<double> animation,
    Animation<double> secondaryAnimation,
  ) {
    return builder(context);
  }

  @override
  String get barrierLabel => 'Paste Text Screen';
}

class _PasteTextFullScreenState extends State<PasteTextFullScreen> {
  final TextEditingController _titleController = TextEditingController();
  final TextEditingController _contentController = TextEditingController();
  final FocusNode _titleFocusNode = FocusNode();
  final FocusNode _contentFocusNode = FocusNode();

  int _characterCount = 0;
  bool _isLoading = false;
  bool _hasContentChanged = false;

  @override
  void initState() {
    super.initState();
    _contentController.addListener(_updateContentState);

    // Try to paste from clipboard automatically if enabled
    if (widget.autoPasteFromClipboard) {
      _tryPasteFromClipboard();
    }
  }

  Future<void> _tryPasteFromClipboard() async {
    ClipboardData? clipboardData =
        await Clipboard.getData(Clipboard.kTextPlain);
    if (clipboardData != null &&
        clipboardData.text != null &&
        clipboardData.text!.isNotEmpty) {
      setState(() {
        _contentController.text = clipboardData.text!;
        _hasContentChanged = true;
      });
    }
  }

  void _updateContentState() {
    setState(() {
      _characterCount = _contentController.text.length;
      _hasContentChanged = true;
    });
  }

  void _handleSave() {
    if (_titleController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please enter a title')),
      );
      return;
    }

    if (_contentController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please enter some content')),
      );
      return;
    }

    setState(() => _isLoading = true);

    // Get the content and title before we clear anything
    final title = _titleController.text;
    final content = _contentController.text;

    // Clear the clipboard after saving (to prevent auto-paste next time)
    Clipboard.setData(const ClipboardData(text: ''));

    // Process with a short delay
    Future.delayed(const Duration(milliseconds: 300), () {
      widget.onSave(title, content);
      Navigator.pop(context);
    });
  }

  void _handlePaste() async {
    ClipboardData? clipboardData =
        await Clipboard.getData(Clipboard.kTextPlain);
    if (clipboardData != null && clipboardData.text != null) {
      setState(() {
        _contentController.text = clipboardData.text!;
        _contentFocusNode.requestFocus();
        _hasContentChanged = true;
      });
    }
  }

  void _handleGoBack() {
    if (_hasContentChanged) {
      _showDiscardChangesDialog();
    } else {
      Navigator.pop(context);
    }
  }

  void _showDiscardChangesDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Discard changes?'),
          content: const Text('If you go back now, your changes will be lost.'),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context); // Close dialog
              },
              child: const Text('KEEP EDITING'),
            ),
            TextButton(
              onPressed: () {
                Navigator.pop(context); // Close dialog
                Navigator.pop(context); // Go back
              },
              child: const Text('DISCARD'),
            ),
          ],
        );
      },
    );
  }

  @override
  void dispose() {
    _titleController.dispose();
    _contentController.dispose();
    _titleFocusNode.dispose();
    _contentFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    // Responsive padding based on screen width
    final horizontalPadding = mediaQuery.size.width > 600 ? 48.0 : 24.0;

    return WillPopScope(
      onWillPop: () async {
        if (_hasContentChanged) {
          _showDiscardChangesDialog();
          return false;
        }
        return true;
      },
      child: Scaffold(
        appBar: AppBar(
          title: Text(
            'Add Book from Text',
            style: theme.textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: _handleGoBack,
          ),
          actions: [
            if (_isLoading)
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                child: Center(
                  child: SizedBox(
                    width: 24,
                    height: 24,
                    child: CircularProgressIndicator(
                      strokeWidth: 2.5,
                      color: colorScheme.primary,
                    ),
                  ),
                ),
              )
            else
              Padding(
                padding: const EdgeInsets.only(right: 8.0),
                child: FilledButton(
                  onPressed:
                      _characterCount > 0 && _titleController.text.isNotEmpty
                          ? _handleSave
                          : null,
                  style: FilledButton.styleFrom(
                    backgroundColor: colorScheme.primary,
                    foregroundColor: colorScheme.onPrimary,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(20),
                    ),
                    padding:
                        const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    elevation: 0,
                  ),
                  child: const Text(
                    'SAVE',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      letterSpacing: 0.5,
                    ),
                  ),
                ),
              ),
          ],
          elevation: 0,
          scrolledUnderElevation: 1.0,
        ),
        body: SafeArea(
          child: CustomScrollView(
            slivers: [
              SliverFillRemaining(
                hasScrollBody: false,
                child: Padding(
                  padding: EdgeInsets.symmetric(
                    horizontal: horizontalPadding,
                    vertical: 16.0,
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Title Section
                      Text(
                        'Book Title',
                        style: theme.textTheme.titleMedium?.copyWith(
                          color: colorScheme.onSurface,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(height: 8),

                      // Title field
                      Container(
                        decoration: BoxDecoration(
                          color: colorScheme.surfaceVariant.withOpacity(0.3),
                          borderRadius: BorderRadius.circular(16),
                          border: Border.all(
                            color: colorScheme.outline.withOpacity(0.2),
                          ),
                        ),
                        padding: const EdgeInsets.symmetric(
                            horizontal: 16, vertical: 8),
                        child: TextField(
                          controller: _titleController,
                          focusNode: _titleFocusNode,
                          decoration: InputDecoration(
                            hintText: 'Enter book title',
                            border: InputBorder.none,
                            hintStyle:
                                TextStyle(color: colorScheme.onSurfaceVariant),
                          ),
                          style: theme.textTheme.titleMedium?.copyWith(
                            color: colorScheme.onSurface,
                            fontWeight: FontWeight.w500,
                          ),
                          textCapitalization: TextCapitalization.words,
                        ),
                      ),
                      const SizedBox(height: 24),

                      // Content Section Header
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'Content',
                            style: theme.textTheme.titleMedium?.copyWith(
                              color: colorScheme.onSurface,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          Row(
                            children: [
                              // Character count
                              Container(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 8, vertical: 4),
                                decoration: BoxDecoration(
                                  color: colorScheme.surfaceVariant,
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: Text(
                                  '$_characterCount characters',
                                  style: theme.textTheme.bodySmall?.copyWith(
                                    color: colorScheme.onSurfaceVariant,
                                  ),
                                ),
                              ),
                              const SizedBox(width: 12),

                              // Paste button
                              IconButton(
                                icon: Icon(
                                  Icons.content_paste_rounded,
                                  color: colorScheme.primary,
                                ),
                                onPressed: _handlePaste,
                                tooltip: 'Paste from clipboard',
                                style: IconButton.styleFrom(
                                  backgroundColor: colorScheme.primaryContainer
                                      .withOpacity(0.4),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),

                      // Content area
                      Expanded(
                        child: Container(
                          decoration: BoxDecoration(
                            color: colorScheme.surfaceVariant.withOpacity(0.3),
                            borderRadius: BorderRadius.circular(16),
                            border: Border.all(
                              color: colorScheme.outline.withOpacity(0.2),
                            ),
                          ),
                          child: TextField(
                            controller: _contentController,
                            focusNode: _contentFocusNode,
                            decoration: InputDecoration(
                              hintText: 'Paste or type content here...',
                              border: InputBorder.none,
                              contentPadding: const EdgeInsets.all(16),
                              hintStyle: TextStyle(
                                  color: colorScheme.onSurfaceVariant
                                      .withOpacity(0.7)),
                            ),
                            style: theme.textTheme.bodyLarge?.copyWith(
                              color: colorScheme.onSurface,
                              height: 1.5,
                            ),
                            keyboardType: TextInputType.multiline,
                            textCapitalization: TextCapitalization.sentences,
                            maxLines: null,
                            expands: true,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
