import 'dart:math' show Random;

/// This class implements the exact learning algorithm from the Java version
class JavaMetricsLearn {
  bool FINISHED = false;
  int countVar = 0;
  int curItemInd = 0;
  List<int> curItemsList = [];
  int delayValue = 100;
  int itemDisplayNumber = 0;
  int itemDisplayType = 0;
  List<int> itemOrderArr = List.filled(30, 0);
  int learnItemsInt = 0;
  List<LearnItem> learnItemsList = [];
  bool missedFlag = false;
  int newItemsLength = 3;
  List<int> repeatList = [];
  List<int> returnItemsList = [];
  bool saveNowFlag = false;
  int stageCount = 0;
  bool stageFourBool = true;
  int stageLength = 0;
  bool stageSevenBool1 = true;
  bool stageSevenBool2 = true;
  int stageVar = 0;

  JavaMetricsLearn() {
    itemOrderArr = ranSeq();
  }

  void init() {
    for (int i = 0; i < 30; i++) {
      learnItemsList.add(LearnItem(i, 0, 15));
    }
    stageVar = 1;
    addNewItems();
    initNewStage();
  }

  void reinit(int stageVar, List<int> levels) {
    this.stageVar = stageVar;
    for (int i = 0; i < 30; i++) {
      learnItemsList.add(LearnItem(i, 0, levels[i]));
    }

    for (int level in levels) {
      if (level != 15) {
        learnItemsInt++;
      }
    }

    // Reset logic for stages 4, 7, and 8 based on the number of learned items
    if (learnItemsInt > 3) {
      stageFourBool = false;
    }
    if (learnItemsInt > 17) {
      stageSevenBool1 = false;
    }
    if (stageVar == 7) {
      stageSevenBool1 = false;
    }
    if (learnItemsInt > 25) {
      stageSevenBool2 = false;
    }
    if (learnItemsInt > 22 && stageVar == 7) {
      stageSevenBool2 = false;
    }

    if (learnItemsInt > 3) {
      stageFourBool = false;
    }

    if (learnItemsInt > 17) {
      stageSevenBool1 = false;
    }

    if (stageVar == 7) {
      stageSevenBool1 = false;
    }

    if (learnItemsInt > 25) {
      stageSevenBool2 = false;
    }

    if (learnItemsInt > 22 && stageVar == 7) {
      stageSevenBool2 = false;
    }

    initNewStage();
  }

  void setRestartStageCount(int stageCount) {
    this.stageCount = stageCount;
    for (int i = 0; i < curItemsList.length; i++) {
      if (i < stageCount) {
        learnItemsList[curItemsList[i]].curLevel++;
      }
    }
  }

  List<int> newRound() {
    countVar++;
    if (stageCount < curItemsList.length) {
      curItemInd = curItemsList[stageCount];
    } else {
      stageCount = 0;
      curItemInd = curItemsList[stageCount];
    }

    returnItemsList = randomizeListArrayInit(curItemsList, curItemInd);
    return returnItemsList;
  }

  List<MyPair> updateStats() {
    learnItemsList[curItemInd].lastShown = countVar;

    if (!missedFlag) {
      if (learnItemsList[curItemInd].curLevel < stageVar + 1) {
        learnItemsList[curItemInd].curLevel = stageVar + 1;
      }
      stageCount++;
    } else {
      repeatList.add(curItemInd);
      stageLength = curItemsList.length;
      randomizeFromPoint(curItemsList, stageCount);
    }

    missedFlag = false;
    List<MyPair> retCurItemsList = [];

    if (stageCompleteCheck()) {
      stageVar++;
      if (stageVar > 9) {
        stageVar = 9;
      }

      if (stageVar == 7 || stageVar == 8) {
        resetLevels();
      }

      for (int i in curItemsList) {
        retCurItemsList.add(MyPair(i, learnItemsList[i].curLevel));
      }

      if (stageVar == 4 && stageFourBool) {
        resetToStageOne();
      }

      if (stageVar == 7 && !stageSevenCheck()) {
        resetToStageOne();
      }

      if (stageVar == 8 && learnItemsInt < 30) {
        resetToStageOne();
      }

      if (stageVar >= 9) {
        FINISHED = true;
      } else {
        initNewStage();
      }
    }

    return retCurItemsList;
  }

  void learnAgainReset() {
    for (int i = 0; i < 30; i++) {
      learnItemsList[i].curLevel = 8;
    }
  }

  void resetLevels() {
    for (int i in repeatList) {
      if (learnItemsList[i].curLevel == 2) {
        learnItemsList[i].curLevel = 1;
      }
      if (learnItemsList[i].curLevel == 4) {
        learnItemsList[i].curLevel = 2;
      }
      if (learnItemsList[i].curLevel == 5) {
        learnItemsList[i].curLevel = 4;
      }
      if (learnItemsList[i].curLevel == 7) {
        learnItemsList[i].curLevel = 5;
      }
    }
    repeatList.clear();
  }

  void resetToStageOne() {
    stageFourBool = false;
    stageVar = 1;
    addNewItems();
  }

  bool stageSevenCheck() {
    if (learnItemsInt > 14 && stageSevenBool1) {
      stageSevenBool1 = false;
      return true;
    } else if (learnItemsInt > 22 && stageSevenBool2) {
      stageSevenBool2 = false;
      return true;
    } else if (learnItemsInt <= 27) {
      return false;
    } else {
      return true;
    }
  }

  bool stageCompleteCheck() {
    return stageCount >= stageLength;
  }

  void addNewItems() {
    int loopVar = 0;
    int addedInt = 0;
    saveNowFlag = true;

    while (addedInt < newItemsLength) {
      if (learnItemsList[itemOrderArr[loopVar]].curLevel == 15) {
        learnItemsList[itemOrderArr[loopVar]].curLevel = 1;
        addedInt++;
        learnItemsInt++;
      }
      loopVar++;
      if (loopVar == 30) {
        addedInt = newItemsLength;
      }
    }
  }

  void initNewStage() {
    itemDisplayType = 0;

    switch (stageVar) {
      case 1:
        fillCurItemsList(3);
        itemDisplayNumber = 1;
        break;
      case 2:
        fillCurItemsList(3);
        itemDisplayNumber = 3;
        break;
      case 3:
        fillCurItemsList(3);
        itemDisplayNumber = 3;
        itemDisplayType = 1;
        break;
      case 4:
        fillCurItemsList(5);
        itemDisplayNumber = 5;
        break;
      case 5:
        fillCurItemsList(5);
        itemDisplayNumber = 5;
        break;
      case 6:
        fillCurItemsList(5);
        itemDisplayNumber = 5;
        itemDisplayType = 1;
        break;
      case 7:
        fillCurItemsList(15);
        itemDisplayNumber = 8;
        break;
      case 8:
        fillCurItemsList(30);
        itemDisplayNumber = 1;
        itemDisplayType = 2;
        break;
      case 9:
        learnAgainReset();
        fillCurItemsList(30);
        itemDisplayNumber = 1;
        itemDisplayType = 2;
        break;
    }

    stageLength = curItemsList.length;
    stageCount = 0;

    // Shuffle until the first item is not the current item
    do {
      curItemsList.shuffle();
    } while (curItemsList.isNotEmpty && curItemsList[0] == curItemInd);
  }

  void fillCurItemsList(int count) {
    // Sort by level and last shown time
    learnItemsList.sort((a, b) {
      int levelCompare = a.curLevel.compareTo(b.curLevel);
      if (levelCompare == 0) {
        return a.lastShown.compareTo(b.lastShown);
      }
      return levelCompare;
    });

    curItemsList.clear();

    // Add items at the current stage level
    for (int i = 0; i < 30; i++) {
      if (learnItemsList[i].curLevel == stageVar) {
        curItemsList.add(learnItemsList[i].ind);
      }
    }

    // Fill with other items if needed
    int lVar = 0;
    while (curItemsList.length < count && lVar < 30) {
      if (learnItemsList[lVar].curLevel != stageVar) {
        curItemsList.add(learnItemsList[lVar].ind);
      }
      lVar++;
    }

    // Sort back by index
    learnItemsList.sort((a, b) => a.ind.compareTo(b.ind));
  }

  int getStageVar() {
    return stageVar;
  }

  int getStageCount() {
    return stageCount;
  }

  bool checkSaveNow() {
    if (!saveNowFlag) {
      return false;
    }
    saveNowFlag = false;
    return true;
  }

  List<MyPair> getSaveNowPairs() {
    List<MyPair> savePairs = [];
    for (int item in curItemsList) {
      savePairs.add(MyPair(item, 1));
    }
    return savePairs;
  }

  List<int> randomizeListArrayInit(List<int> inArr, int init) {
    List<int> tList = List.from(inArr);
    tList.shuffle();

    // Find the index of the init item and swap it to position 0
    int tmp = tList.indexOf(init);
    if (tmp != 0) {
      int temp = tList[0];
      tList[0] = tList[tmp];
      tList[tmp] = temp;
    }

    return List.from(tList);
  }

  void randomizeFromPoint(List<int> inArr, int point) {
    List<int> tList = [];
    for (int i = point; i < inArr.length; i++) {
      tList.add(inArr[i]);
    }

    int tmp = inArr[point];
    if (tList.length > 1) {
      while (tmp == tList[0]) {
        tList.shuffle();
      }
    }

    for (int i = point; i < inArr.length; i++) {
      inArr[i] = tList[i - point];
    }
  }

  List<int> ranSeq() {
    List<int> outArr = List.filled(30, 0);
    List<int> tArr1 = List.generate(30, (i) => i);
    tArr1.shuffle();

    for (int i = 0; i < 30; i++) {
      outArr[i] = tArr1[i];
    }

    return outArr;
  }

  // Helper method to set position shuffle for buttons
  List<int> setPositionShuffle(List<int> lastPositionShuffle) {
    List<int> positionShuffle = [];
    for (int i = 0; i < itemDisplayNumber; i++) {
      positionShuffle.add(i);
    }

    // Get the last position of the correct answer (if any)
    int lastCorrectPos = -1;
    if (lastPositionShuffle.isNotEmpty) {
      lastCorrectPos = lastPositionShuffle.indexOf(0);
    }

    // Shuffle until the correct answer is in a different position
    // (if there are enough options)
    final random = Random();
    if (itemDisplayNumber > 1) {
      do {
        positionShuffle.shuffle(random);
      } while (positionShuffle.indexOf(0) == lastCorrectPos && lastCorrectPos != -1);
    }

    return positionShuffle;
  }
}

class LearnItem {
  int curLevel;
  int ind;
  int lastShown;

  LearnItem(this.ind, this.lastShown, this.curLevel);
}

class MyPair {
  final int key;
  final int value;

  MyPair(this.key, this.value);

  int returnKey() {
    return key;
  }

  int returnValue() {
    return value;
  }
}
