import 'package:flutter/material.dart';

/// Design System constants for the DassoShu Reader app
/// 
/// This class contains standardized values for spacing, elevation, 
/// typography and other design-related constants to maintain 
/// consistency across the app.
class DesignSystem {
  // Private constructor to prevent instantiation
  DesignSystem._();

  // SPACING SYSTEM
  /// Extra small spacing (4.0)
  static const double spaceXS = 4.0;
  
  /// Small spacing (8.0)
  static const double spaceS = 8.0;
  
  /// Medium spacing (16.0) - The base unit
  static const double spaceM = 16.0;
  
  /// Large spacing (24.0)
  static const double spaceL = 24.0;
  
  /// Extra large spacing (32.0)
  static const double spaceXL = 32.0;
  
  /// Double extra large spacing (48.0)
  static const double spaceXXL = 48.0;

  // INSETS
  /// Consistent page padding
  static const EdgeInsets pagePadding = EdgeInsets.all(spaceM);
  
  /// Small container padding
  static const EdgeInsets containerPaddingSmall = EdgeInsets.all(spaceS);
  
  /// Medium container padding
  static const EdgeInsets containerPaddingMedium = EdgeInsets.all(spaceM);
  
  /// Horizontal padding for buttons, text fields, etc.
  static const EdgeInsets buttonPadding = EdgeInsets.symmetric(
    horizontal: spaceM,
    vertical: spaceS,
  );

  // ELEVATION
  /// No elevation (0.0)
  static const double elevationNone = 0.0;
  
  /// Extra small elevation (1.0) - subtle
  static const double elevationXS = 1.0;
  
  /// Small elevation (2.0) - cards, buttons
  static const double elevationS = 2.0;
  
  /// Medium elevation (4.0) - dialogs, floating action buttons
  static const double elevationM = 4.0;
  
  /// Large elevation (8.0) - navigation drawers
  static const double elevationL = 8.0;
  
  /// Extra large elevation (12.0) - modals
  static const double elevationXL = 12.0;

  // BORDER RADIUS
  /// Small border radius (4.0)
  static const double radiusS = 4.0;
  
  /// Medium border radius (8.0)
  static const double radiusM = 8.0;
  
  /// Large border radius (16.0)
  static const double radiusL = 16.0;
  
  /// Circle border radius (28.0) - used for circular buttons
  static const double radiusCircle = 28.0;

  // ANIMATION DURATIONS
  /// Fast animations (150ms)
  static const Duration durationFast = Duration(milliseconds: 150);
  
  /// Medium animations (300ms)
  static const Duration durationMedium = Duration(milliseconds: 300);
  
  /// Slow animations (500ms)
  static const Duration durationSlow = Duration(milliseconds: 500);

  // RESPONSIVE BREAKPOINTS
  /// Mobile breakpoint (< 600)
  static const double breakpointMobile = 600;
  
  /// Tablet breakpoint (< 1000)
  static const double breakpointTablet = 1000;
  
  /// Desktop breakpoint (≥ 1000)
  static const double breakpointDesktop = 1000;

  /// Helper to determine if the current width is mobile
  static bool isMobile(BuildContext context) =>
      MediaQuery.of(context).size.width < breakpointMobile;

  /// Helper to determine if the current width is tablet
  static bool isTablet(BuildContext context) =>
      MediaQuery.of(context).size.width >= breakpointMobile &&
      MediaQuery.of(context).size.width < breakpointDesktop;

  /// Helper to determine if the current width is desktop
  static bool isDesktop(BuildContext context) =>
      MediaQuery.of(context).size.width >= breakpointDesktop;

  /// Returns adaptive padding based on screen width
  static EdgeInsets getAdaptivePadding(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    
    if (width >= breakpointDesktop) {
      return EdgeInsets.all(spaceXL);
    } else if (width >= breakpointTablet) {
      return EdgeInsets.all(spaceL);
    } else {
      return EdgeInsets.all(spaceM);
    }
  }

  /// Returns adaptive item count for grids based on screen width
  static int getAdaptiveGridCount(BuildContext context, {double minItemWidth = 120}) {
    final width = MediaQuery.of(context).size.width;
    int count = (width / minItemWidth).floor();
    return count.clamp(2, 8); // Min 2, max 8 columns
  }
}