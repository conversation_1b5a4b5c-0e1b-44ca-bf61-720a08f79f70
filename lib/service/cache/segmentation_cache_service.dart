import 'dart:convert';
import 'dart:io';

import 'package:dasso_reader/utils/log/common.dart';
import 'package:path_provider/path_provider.dart';

/// Service for caching Chinese text segmentation results
class SegmentationCacheService {
  static final SegmentationCacheService _instance =
      SegmentationCacheService._internal();

  /// Singleton instance
  factory SegmentationCacheService() => _instance;

  SegmentationCacheService._internal();

  /// In-memory cache for segmentation results
  final Map<String, List<String>> _memoryCache = {};

  /// Maximum number of entries in memory cache
  final int _maxMemoryCacheSize = 1000;

  /// Flag to track initialization status
  bool _isInitialized = false;

  /// Initialize the cache service
  Future<void> initialize() async {
    if (_isInitialized) return;

    AnxLog.info('Initializing segmentation cache service');

    try {
      // Load cache from disk if available
      await _loadCacheFromDisk();
      
      _isInitialized = true;
      AnxLog.info('Segmentation cache service initialized successfully');
    } catch (e) {
      AnxLog.severe('Error initializing segmentation cache service: $e');
      // Continue with initialization even if there's an error
      _isInitialized = true;
    }
  }

  /// Load cache from disk
  Future<void> _loadCacheFromDisk() async {
    try {
      final cacheDir = await getTemporaryDirectory();
      final cacheFile = File('${cacheDir.path}/segmentation_cache.json');
      
      if (await cacheFile.exists()) {
        final content = await cacheFile.readAsString();
        final Map<String, dynamic> data = json.decode(content);
        
        data.forEach((key, value) {
          _memoryCache[key] = List<String>.from(value);
        });
        
        AnxLog.info('Loaded ${_memoryCache.length} segmentation cache entries from disk');
      }
    } catch (e) {
      AnxLog.warning('Error loading segmentation cache from disk: $e');
    }
  }
  
  /// Save cache to disk
  Future<void> _saveCacheToDisk() async {
    try {
      final cacheDir = await getTemporaryDirectory();
      final cacheFile = File('${cacheDir.path}/segmentation_cache.json');
      
      // Limit the number of entries to save
      final Map<String, List<String>> dataToSave = {};
      final entries = _memoryCache.entries.take(500).toList();
      
      for (final entry in entries) {
        dataToSave[entry.key] = entry.value;
      }
      
      await cacheFile.writeAsString(json.encode(dataToSave));
      AnxLog.info('Saved ${dataToSave.length} segmentation cache entries to disk');
    } catch (e) {
      AnxLog.warning('Error saving segmentation cache to disk: $e');
    }
  }

  /// Get segmentation result from cache
  Future<List<String>?> getSegmentation(String text) async {
    if (!_isInitialized) {
      await initialize();
    }
    
    // Check memory cache
    if (_memoryCache.containsKey(text)) {
      AnxLog.info('Segmentation cache hit for text: "${_truncateText(text)}"');
      return _memoryCache[text];
    }
    
    return null;
  }
  
  /// Cache segmentation result
  Future<void> cacheSegmentation(String text, List<String> segments) async {
    if (!_isInitialized) {
      await initialize();
    }
    
    // Add to memory cache
    _memoryCache[text] = segments;
    
    // Trim cache if it exceeds maximum size
    if (_memoryCache.length > _maxMemoryCacheSize) {
      final keysToRemove = _memoryCache.keys.take(_memoryCache.length - _maxMemoryCacheSize).toList();
      for (final key in keysToRemove) {
        _memoryCache.remove(key);
      }
    }
    
    // Periodically save cache to disk (every 100 new entries)
    if (_memoryCache.length % 100 == 0) {
      await _saveCacheToDisk();
    }
    
    AnxLog.info('Cached segmentation for text: "${_truncateText(text)}"');
  }
  
  /// Clear the cache
  Future<void> clearCache() async {
    _memoryCache.clear();
    
    try {
      final cacheDir = await getTemporaryDirectory();
      final cacheFile = File('${cacheDir.path}/segmentation_cache.json');
      
      if (await cacheFile.exists()) {
        await cacheFile.delete();
      }
      
      AnxLog.info('Segmentation cache cleared');
    } catch (e) {
      AnxLog.warning('Error clearing segmentation cache: $e');
    }
  }
  
  /// Truncate text for logging
  String _truncateText(String text) {
    if (text.length <= 20) return text;
    return '${text.substring(0, 20)}...';
  }
}
