import 'dart:async';
import 'dart:convert';
import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:dasso_reader/service/dictionary/maximum_matching_segmentation.dart';
import 'package:dasso_reader/utils/log/common.dart';

/// A service for Chinese text segmentation and word boundary detection
class ChineseSegmentationService {
  static final ChineseSegmentationService _instance =
      ChineseSegmentationService._internal();

  /// Singleton instance
  factory ChineseSegmentationService() => _instance;

  ChineseSegmentationService._internal();

  /// Flag to track initialization status
  bool _isInitialized = false;

  /// Lock for synchronization
  final _lock = Object();

  /// Maximum matching segmentation service
  final MaximumMatchingSegmentationService _maxMatchingService =
      MaximumMatchingSegmentationService();

  /// In-memory cache for segmentation results
  final _segmentationCache = _LRUCache<String, List<List<int>>>(100);

  /// Initialize the segmentation service
  Future<void> initialize() async {
    if (_isInitialized) return;

    AnxLog.info('Initializing Chinese segmentation service');

    synchronized(() async {
      if (_isInitialized) return;

      try {
        // Initialize the maximum matching service
        await _maxMatchingService.initialize();

        _isInitialized = true;
        AnxLog.info('Chinese segmentation service initialized');
      } catch (e) {
        AnxLog.severe('Failed to initialize Chinese segmentation service: $e');
      }
    });
  }

  /// Segment Chinese text into words
  /// Returns a list of word boundaries [start, end] tuples
  ///
  /// If [bookId] is provided, the segmentation results will be cached
  Future<List<List<int>>> getWordBoundaries(String text, {int? bookId}) async {
    if (!_isInitialized) {
      await initialize();
    }
    
    // For very short texts, use memory cache for instant results
    if (text.length < 100) {
      final cacheKey = text;
      final cachedResult = _segmentationCache.get(cacheKey);
      if (cachedResult != null) {
        AnxLog.info('✅ MEMORY CACHE HIT: Retrieved cached segmentation with ${cachedResult.length} parts');
        return cachedResult;
      }
    }

    return synchronized(() async {
      try {
        final Stopwatch stopwatch = Stopwatch()..start();
        List<List<int>> boundaries;

        // If not cached or no bookId provided, perform segmentation
        final processingStopwatch = Stopwatch()..start();
        
        // Use compute to move segmentation to a background thread for better performance
        if (text.length > 100) {
          boundaries = await compute(_isolatedSegmentation, {
            'text': text,
            'dictionary': await _maxMatchingService.getDictionary()
          });
        } else {
          boundaries = await _maxMatchingService.getWordBoundaries(text);
        }
        
        final processingTime = processingStopwatch.elapsedMilliseconds;
        AnxLog.info(
            'Segmented text into ${boundaries.length} parts in ${processingTime}ms');
        
        // Store in memory cache for faster future access
        if (text.length < 100) {
          _segmentationCache.set(text, boundaries);
        }

        final elapsed = stopwatch.elapsedMilliseconds;
        AnxLog.info(
            'Total segmentation time: ${elapsed}ms (processing: ${processingTime}ms)');
        return boundaries;
      } catch (e) {
        AnxLog.severe('Error segmenting text: $e');
        // Fallback to character-by-character segmentation
        List<List<int>> result = [];
        for (int i = 0; i < text.length; i++) {
          result.add([i, i + 1]);
        }
        return result;
      }
    });
  }
  
  /// Static method for isolated segmentation (runs in a separate thread)
  static Future<List<List<int>>> _isolatedSegmentation(Map<String, dynamic> params) async {
    final String text = params['text'];
    final Map<String, bool> dictionary = params['dictionary'];
    
    // Simple implementation of maximum matching algorithm for isolate
    List<List<int>> boundaries = [];
    int i = 0;
    
    while (i < text.length) {
      int maxMatchLength = 0;
      
      // Try to find the longest matching word starting at position i
      for (int j = 1; j <= 4 && i + j <= text.length; j++) {
        String word = text.substring(i, i + j);
        if (dictionary.containsKey(word)) {
          maxMatchLength = j;
        }
      }
      
      // If no match found, use single character
      if (maxMatchLength == 0) {
        maxMatchLength = 1;
      }
      
      boundaries.add([i, i + maxMatchLength]);
      i += maxMatchLength;
    }
    
    return boundaries;
  }

  /// Test the segmentation with a sample text
  Future<List<String>> testSegmentation(String text) async {
    if (!_isInitialized) {
      await initialize();
    }

    AnxLog.info('Testing segmentation for: "$text"');

    try {
      final Stopwatch stopwatch = Stopwatch()..start();
      final segments = await _maxMatchingService.testSegmentation(text);
      final elapsed = stopwatch.elapsedMilliseconds;
      AnxLog.info(
          'Segmented words (${segments.length}) in ${elapsed}ms: ${segments.join(' | ')}');
      return segments;
    } catch (e) {
      AnxLog.severe('Error testing segmentation: $e');
      return [text];
    }
  }

  /// Get the word boundary for a specific character position
  /// This helps to find the proper word that contains a specific character
  Future<List<int>> getWordBoundaryForPosition(
      String text, int position) async {
    // Make sure position is within range
    if (position < 0 || position >= text.length) {
      return [position, position + 1]; // Default to single character
    }

    try {
      // Get all word boundaries
      final boundaries = await getWordBoundaries(text);

      // Find the boundary that contains the position
      for (final List<int> boundary in boundaries) {
        final start = boundary[0];
        final end = boundary[1];

        if (start <= position && position < end) {
          AnxLog.info('Found word boundary $start-$end for position $position');
          return boundary;
        }
      }

      // If no matching boundary is found, return single character
      AnxLog.warning('No word boundary found for position $position in text');
      return [position, position + 1];
    } catch (e) {
      AnxLog.severe('Error getting word boundary for position $position: $e');
      return [position, position + 1]; // Fall back to single character
    }
  }

  /// Get the word boundaries that would be used for text selection
  /// Used when spaces are not visible but we still want smart selection
  Future<List<List<int>>> getSelectionBoundaries(String text) async {
    final Stopwatch stopwatch = Stopwatch()..start();

    // For selection, we want to prioritize dictionary-based words
    // but also allow for maximum flexibility in boundary detection
    final boundaries = await getWordBoundaries(text);

    // Add single-character boundaries for any characters not covered
    final Set<int> coveredPositions = {};

    // Mark all positions covered by existing boundaries
    for (final boundary in boundaries) {
      for (int i = boundary[0]; i < boundary[1]; i++) {
        coveredPositions.add(i);
      }
    }

    // Find any positions not covered and add single-character boundaries
    final List<List<int>> allBoundaries = List.from(boundaries);
    for (int i = 0; i < text.length; i++) {
      if (!coveredPositions.contains(i)) {
        allBoundaries.add([i, i + 1]);
      }
    }

    // Sort boundaries by start position
    allBoundaries.sort((a, b) => a[0].compareTo(b[0]));

    final elapsed = stopwatch.elapsedMilliseconds;
    AnxLog.info(
        'Generated ${allBoundaries.length} selection boundaries from ${boundaries.length} word boundaries in ${elapsed}ms');
    return allBoundaries;
  }

  /// Helper function for synchronized blocks
  Future<T> synchronized<T>(Future<T> Function() fn) async {
    if (!_lock.toString().contains('Mutex')) {
      // If using a simple Object for locking
      return await fn();
    } else {
      // Future implementation with proper mutex if needed
      return await fn();
    }
  }
}

/// A simple LRU cache implementation
class _LRUCache<K, V> {
  final int _capacity;
  final Map<K, V> _cache = {};
  final List<K> _keys = [];

  _LRUCache(this._capacity);

  V? get(K key) {
    if (!_cache.containsKey(key)) return null;
    
    // Move key to the end of the list (most recently used)
    _keys.remove(key);
    _keys.add(key);
    
    return _cache[key];
  }

  void set(K key, V value) {
    if (_cache.containsKey(key)) {
      // Update existing key
      _cache[key] = value;
      _keys.remove(key);
      _keys.add(key);
    } else {
      // Add new key
      _cache[key] = value;
      _keys.add(key);
      
      // Remove oldest key if capacity is exceeded
      if (_keys.length > _capacity) {
        final oldestKey = _keys.removeAt(0);
        _cache.remove(oldestKey);
      }
    }
  }

  void clear() {
    _cache.clear();
    _keys.clear();
  }
}
