import 'dart:async';
import 'dart:io';
import 'dart:convert';

import 'package:dasso_reader/config/shared_preference_provider.dart';
import 'package:dasso_reader/models/dictionary/dictionary_entry.dart';
import 'package:dasso_reader/service/dictionary/online_dictionary_service.dart';
import 'package:dasso_reader/utils/log/common.dart';
import 'package:flutter/services.dart';
import 'package:flutter/material.dart';
import 'package:path/path.dart';
import 'package:path_provider/path_provider.dart';
import 'package:sqflite/sqflite.dart';
import 'package:characters/characters.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:pinyin/pinyin.dart';

/// Service class for Chinese dictionary operations
class DictionaryService {
  static final DictionaryService _instance = DictionaryService._internal();

  /// Singleton instance
  factory DictionaryService() => _instance;

  DictionaryService._internal();

  /// Database instance
  Database? _database;

  /// Flag to track initialization status
  bool _isInitialized = false;

  /// Flag to track dictionary preloading status
  bool _isPreloading = false;

  /// Flag to track if dictionary is preloaded
  bool _isPreloaded = false;

  /// Lock for synchronization
  final _lock = Object();

  /// In-memory cache for dictionary entries
  final Map<String, DictionaryEntry> _cache = {};

  /// Maximum cache size for non-preloaded mode
  final int _maxCacheSize = 500;

  /// Online dictionary service for fallback lookups
  final OnlineDictionaryService _onlineDictionaryService =
      OnlineDictionaryService();

  /// Flag to track network connectivity
  bool _isOffline = false;

  /// Subscription for connectivity changes
  StreamSubscription? _connectivitySubscription;

  /// Initialize the dictionary service
  Future<void> initialize() async {
    if (_isInitialized) return;

    AnxLog.info('Initializing dictionary service');

    // Use a synchronized block to prevent concurrent initialization
    synchronized(() async {
      if (_isInitialized) return;

      try {
        final db = await database;

        // Verify database has entries
        final count = Sqflite.firstIntValue(
            await db.rawQuery('SELECT COUNT(*) FROM dictionary'));

        if (count == 0) {
          AnxLog.info(
              'Dictionary database is empty. Will use online dictionary services.');
        } else {
          AnxLog.info('Dictionary database has $count entries');
        }

        // Initialize online dictionary service
        await _onlineDictionaryService.initialize();

        // Set up connectivity monitoring
        await _setupConnectivityMonitoring();

        _isInitialized = true;
      } catch (e) {
        AnxLog.severe('Error initializing dictionary service: $e');
        // Continue with initialization even if there's an error
        // to allow fallback to online services
        _isInitialized = true;
      }
    });
  }

  /// Set up monitoring for network connectivity changes
  Future<void> _setupConnectivityMonitoring() async {
    // Check initial connectivity
    final connectivityResults = await Connectivity().checkConnectivity();
    _updateConnectivityStatus(connectivityResults);

    // Listen for connectivity changes
    _connectivitySubscription =
        Connectivity().onConnectivityChanged.listen((results) {
      _updateConnectivityStatus(results);
    });
  }

  /// Update connectivity status and set offline mode
  void _updateConnectivityStatus(List<ConnectivityResult> results) {
    final wasOffline = _isOffline;
    _isOffline = results.isEmpty || results.contains(ConnectivityResult.none);

    // Only log if status changed
    if (wasOffline != _isOffline) {
      AnxLog.info(
          'Network connectivity changed: ${_isOffline ? 'offline' : 'online'}');
      _onlineDictionaryService.setOfflineMode(_isOffline);
    }
  }

  /// Get the database instance, initializing if necessary
  Future<Database> get database async {
    if (_database != null) return _database!;

    // Initialize the database
    return synchronized(() async {
      if (_database != null) return _database!;
      _database = await _initDatabase();
      return _database!;
    });
  }

  /// Check if the dictionary service is initialized
  bool get isInitialized => _isInitialized;

  /// Check if the device is currently offline
  bool get isOffline => _isOffline;

  /// Initialize the database
  Future<Database> _initDatabase() async {
    // Get the documents directory
    final documentsDirectory = await getApplicationDocumentsDirectory();
    final path = join(documentsDirectory.path, 'chinese_dictionary.db');

    // Check if database already exists
    final exists = await databaseExists(path);

    // If database doesn't exist, create it
    if (!exists) {
      try {
        AnxLog.info(
            'Dictionary database does not exist, copying from assets...');
        await _copyFromAsset(path);
        AnxLog.info('Dictionary database copied successfully');
      } catch (e) {
        AnxLog.severe('Failed to copy dictionary database: $e');
        // Create empty database if copy fails
        AnxLog.info('Creating empty database instead');
        return await _createEmptyDatabase(path);
      }
    }

    // Open the database
    try {
      final db = await openDatabase(path, version: 1);

      // Verify database has tables
      final tables = await db.query('sqlite_master',
          where: "type = 'table' AND name IN ('dictionary', 'character_info')");

      if (tables.length < 2) {
        AnxLog.severe('Dictionary database is missing tables, recreating...');
        await db.close();
        await deleteDatabase(path);
        return _initDatabase();
      }

      // Log some stats
      final dictCount = Sqflite.firstIntValue(
          await db.rawQuery('SELECT COUNT(*) FROM dictionary'));
      final charCount = Sqflite.firstIntValue(
          await db.rawQuery('SELECT COUNT(*) FROM character_info'));

      AnxLog.info(
          'Dictionary database opened successfully with $dictCount entries and $charCount characters');

      _isInitialized = true;
      return db;
    } catch (e) {
      AnxLog.severe('Error opening dictionary database: $e');
      // If there's an error, try to recreate the database
      if (await databaseExists(path)) {
        await deleteDatabase(path);
        AnxLog.info('Deleted corrupted database, trying to recreate...');
        return _initDatabase();
      }

      // If all else fails, create an empty database
      return _createEmptyDatabase(path);
    }
  }

  /// Copy database from assets to documents directory
  Future<void> _copyFromAsset(String dbPath) async {
    try {
      // Read the pre-populated database from assets
      final data =
          await rootBundle.load('assets/dictionary/chinese_dictionary.db');

      // Write to documents directory
      final bytes =
          data.buffer.asUint8List(data.offsetInBytes, data.lengthInBytes);
      await File(dbPath).writeAsBytes(bytes, flush: true);

      AnxLog.info(
          'Dictionary database copied from assets (${bytes.length} bytes)');
    } catch (e) {
      AnxLog.severe('Error copying dictionary database: $e');
      rethrow;
    }
  }

  /// Create an empty dictionary database
  Future<Database> _createEmptyDatabase(String dbPath) async {
    AnxLog.info('Creating empty dictionary database at $dbPath');

    final db = await openDatabase(
      dbPath,
      version: 1,
      onCreate: (db, version) async {
        // Create dictionary table
        await db.execute('''
          CREATE TABLE dictionary (
            id INTEGER PRIMARY KEY,
            traditional TEXT,
            simplified TEXT,
            pinyin TEXT,
            definitions TEXT,
            hsk_level INTEGER,
            frequency INTEGER
          )
        ''');

        // Create character info table
        await db.execute('''
          CREATE TABLE character_info (
            character TEXT PRIMARY KEY,
            radical TEXT,
            components TEXT
          )
        ''');

        // Create indices for faster lookups
        await db
            .execute('CREATE INDEX idx_simplified ON dictionary(simplified)');
        await db
            .execute('CREATE INDEX idx_traditional ON dictionary(traditional)');
        await db.execute('CREATE INDEX idx_pinyin ON dictionary(pinyin)');
      },
    );

    AnxLog.info('Empty dictionary database created successfully');
    return db;
  }

  /// Lookup Chinese word or character with optimized preloaded dictionary
  Future<DictionaryEntry?> lookupChinese(String text) async {
    if (text.isEmpty) return null;

    // Clean the word (remove whitespace, etc.)
    final cleanWord = text.trim();

    // Check cache first (this will be instant if dictionary is preloaded)
    if (_cache.containsKey(cleanWord)) {
      DictionaryEntry cachedEntry = _cache[cleanWord]!;

      // If the cached entry has "Unknown" pinyin, try to generate proper pinyin
      if (cachedEntry.pinyin == 'Unknown' && _isChinese(cleanWord)) {
        try {
          String pinyinText = await _generatePinyin(cleanWord);
          // Create a new entry with the generated pinyin
          cachedEntry = DictionaryEntry(
            traditional: cachedEntry.traditional,
            simplified: cachedEntry.simplified,
            pinyin: pinyinText,
            definitions: cachedEntry.definitions,
            hskLevel: cachedEntry.hskLevel,
            frequency: cachedEntry.frequency,
          );
          // Update the cache with the improved entry
          _cache[cleanWord] = cachedEntry;
        } catch (e) {
          AnxLog.warning('Error generating pinyin for cached entry: $e');
        }
      }

      return cachedEntry;
    }

    // If dictionary is preloaded, we should have found it already if it exists
    // But we'll still check for compound words and other special cases

    // For preloaded dictionary, try compound word match in memory first
    if (_isPreloaded && cleanWord.length > 1) {
      // Find entries that start with this word
      final compoundMatches = _cache.entries
          .where((entry) =>
              entry.key.startsWith(cleanWord) && entry.key != cleanWord)
          .map((entry) => entry.value)
          .toList();

      if (compoundMatches.isNotEmpty) {
        // Sort by length to get the closest match
        compoundMatches
            .sort((a, b) => a.simplified.length.compareTo(b.simplified.length));

        final match = compoundMatches.first;
        AnxLog.info(
            'Found compound match in preloaded dictionary: ${match.simplified}');

        // Cache this result for the exact query
        _cache[cleanWord] = match;
        return match;
      }
    }

    // If not in preloaded dictionary or preloading is not enabled,
    // perform regular database lookup
    if (!_isPreloaded) {
      DictionaryEntry? entry = await _lookupChineseImpl(cleanWord);

      // Cache the result if not null
      if (entry != null) {
        // Implement simple LRU cache - remove oldest entry if cache is full
        if (!_isPreloaded && _cache.length >= _maxCacheSize) {
          _cache.remove(_cache.keys.first);
        }
        _cache[cleanWord] = entry;
      }

      return entry;
    }

    // If we get here with preloaded dictionary, it means the word wasn't found
    // Create a fallback entry with pinyin if it's Chinese
    if (_isChinese(cleanWord)) {
      String pinyinText = await _generatePinyin(cleanWord);

      final fallbackEntry = DictionaryEntry(
        traditional: cleanWord,
        simplified: cleanWord,
        pinyin: pinyinText,
        definitions: ['No definition available'],
        hskLevel: 0,
        frequency: null,
      );

      // Cache this fallback entry
      if (!_isPreloaded && _cache.length >= _maxCacheSize) {
        _cache.remove(_cache.keys.first);
      }
      _cache[cleanWord] = fallbackEntry;

      AnxLog.info('Created fallback entry for "$cleanWord"');
      return fallbackEntry;
    }

    return null;
  }

  /// Helper method to generate pinyin for Chinese text
  Future<String> _generatePinyin(String text) async {
    try {
      // Use the pinyin package to generate pinyin with tone marks
      return PinyinHelper.getPinyin(text,
          separator: ' ', format: PinyinFormat.WITH_TONE_MARK);
    } catch (e) {
      AnxLog.warning('Error generating pinyin: $e');
      return 'Unknown';
    }
  }

  /// Check if the text is Chinese
  bool _isChinese(String text) {
    // Simple check: if the text contains any Chinese characters
    return RegExp(r'[\u4e00-\u9fa5]').hasMatch(text);
  }

  /// Implementation of the Chinese lookup without caching
  Future<DictionaryEntry?> _lookupChineseImpl(String text) async {
    if (text.isEmpty) return null;

    // Clean the word (remove whitespace, etc.)
    final cleanWord = text.trim();

    try {
      final db = await database;

      return synchronized(() async {
        try {
          // Step 1: Try exact match with simplified Chinese
          var results = await db.query(
            'dictionary',
            where: 'simplified = ?',
            whereArgs: [cleanWord],
            limit: 1,
          );

          if (results.isNotEmpty) {
            AnxLog.info(
                'Found exact dictionary entry in local database: ${results.first}');
            return DictionaryEntry.fromMap(results.first);
          }

          // Step 2: Try exact match with traditional Chinese
          results = await db.query(
            'dictionary',
            where: 'traditional = ?',
            whereArgs: [cleanWord],
            limit: 1,
          );

          if (results.isNotEmpty) {
            AnxLog.info(
                'Found exact dictionary entry by traditional form in local database: ${results.first}');
            return DictionaryEntry.fromMap(results.first);
          }

          // Step 3: Try compound word match (for terms like 船上交货 when searching for 船上)
          if (cleanWord.length > 1) {
            results = await db.query(
              'dictionary',
              where: 'simplified LIKE ? OR traditional LIKE ?',
              whereArgs: ['$cleanWord%', '$cleanWord%'],
              limit: 1,
            );

            if (results.isNotEmpty) {
              AnxLog.info(
                  'Found compound dictionary entry in local database: ${results.first}');
              return DictionaryEntry.fromMap(results.first);
            }
          }

          // Step 4: If no local results, try online lookup
          AnxLog.info('No local dictionary entry found, trying online lookup');
          final onlineEntry =
              await _onlineDictionaryService.lookupChinese(cleanWord);

          if (onlineEntry != null) {
            AnxLog.info('Found dictionary entry online');

            // Save the online entry to local database for future use
            try {
              final id = await db.insert('dictionary', onlineEntry.toMap());
              AnxLog.info('Saved online entry to local database with ID: $id');
            } catch (e) {
              AnxLog.info('Could not save online entry to local database: $e');
            }

            return onlineEntry;
          }

          // If all lookups fail, create a fallback entry with pinyin
          if (_isChinese(cleanWord)) {
            String pinyinText = await _generatePinyin(cleanWord);

            final fallbackEntry = DictionaryEntry(
              traditional: cleanWord,
              simplified: cleanWord,
              pinyin: pinyinText,
              definitions: ['No definition available'],
              hskLevel: 0,
              frequency: null,
            );

            AnxLog.info('Created fallback entry for "$cleanWord"');
            return fallbackEntry;
          }

          AnxLog.info('No dictionary entry found for "$cleanWord"');
          return null;
        } catch (e) {
          AnxLog.severe('Error looking up Chinese word: $e');

          // Try online lookup as fallback for any database errors
          try {
            return await _onlineDictionaryService.lookupChinese(cleanWord);
          } catch (e) {
            AnxLog.severe('Online lookup also failed: $e');
            return null;
          }
        }
      });
    } catch (e) {
      AnxLog.severe('Error accessing dictionary database: $e');

      // Try online lookup as fallback
      try {
        return await _onlineDictionaryService.lookupChinese(cleanWord);
      } catch (e) {
        AnxLog.severe('Online lookup also failed: $e');
        return null;
      }
    }
  }

  /// Find words containing a specific character
  Future<List<DictionaryEntry>> findWordsWithCharacter(String character) async {
    if (character.isEmpty) return [];

    // Initialize if needed
    if (!_isInitialized) {
      await initialize();
    }

    try {
      final db = await database;

      // Query the database for words containing the character
      final results = await db.query(
        'dictionary',
        where: 'simplified LIKE ? OR traditional LIKE ?',
        whereArgs: ['%$character%', '%$character%'],
        limit: 20, // Limit to 20 results for performance
      );

      if (results.isNotEmpty) {
        return results.map((map) => DictionaryEntry.fromMap(map)).toList();
      }

      // If no results in local database, try online service
      if (!_isOffline) {
        return await _onlineDictionaryService.findWordsWithCharacter(character);
      }

      return [];
    } catch (e) {
      AnxLog.severe('Error finding words with character: $e');
      return [];
    }
  }

  /// Lookup English word in definitions
  Future<List<DictionaryEntry>> lookupEnglish(String query) async {
    if (query.isEmpty) return [];

    // Initialize if needed
    if (!_isInitialized) {
      await initialize();
    }

    try {
      final db = await database;

      // Query the database for definitions containing the English word
      final results = await db.query(
        'dictionary',
        where: 'definitions LIKE ?',
        whereArgs: ['%$query%'],
        limit: 20, // Limit to 20 results for performance
      );

      if (results.isNotEmpty) {
        return results.map((map) => DictionaryEntry.fromMap(map)).toList();
      }

      // If no results in local database, return empty list
      // We could implement online English lookup in the future
      return [];
    } catch (e) {
      AnxLog.severe('Error looking up English word: $e');
      return [];
    }
  }

  /// Preload the entire dictionary into memory for faster lookups
  Future<bool> preloadDictionary() async {
    // Check if already preloaded
    if (_isPreloaded) {
      AnxLog.info('Dictionary already preloaded');
      return true;
    }

    // Check if already preloading
    if (_isPreloading) {
      AnxLog.info('Dictionary preloading already in progress');
      return false;
    }

    // Initialize if needed
    if (!_isInitialized) {
      await initialize();
    }

    // Set preloading status
    _isPreloading = true;
    Prefs().dictionaryPreloadStatus = 1; // Loading

    try {
      AnxLog.info('Starting dictionary preloading...');
      final startTime = DateTime.now();

      // Clear existing cache
      _cache.clear();

      final db = await database;

      // Get total count for progress tracking
      final totalCount = Sqflite.firstIntValue(
              await db.rawQuery('SELECT COUNT(*) FROM dictionary')) ??
          0;

      // Update entry count in preferences
      Prefs().dictionaryEntryCount = totalCount;

      AnxLog.info('Preloading $totalCount dictionary entries...');

      // Query all dictionary entries
      final results = await db.query('dictionary');

      // Process entries in batches to avoid UI freezing
      int processedCount = 0;

      for (var row in results) {
        final entry = DictionaryEntry.fromMap(row);

        // Cache by simplified form
        _cache[entry.simplified] = entry;

        // Also cache by traditional form if different
        if (entry.traditional != entry.simplified) {
          _cache[entry.traditional] = entry;
        }

        processedCount++;

        // Update status every 1000 entries
        if (processedCount % 1000 == 0) {
          AnxLog.info(
              'Preloaded $processedCount/$totalCount dictionary entries');
        }
      }

      final duration = DateTime.now().difference(startTime);

      // Set preloaded status
      _isPreloaded = true;
      _isPreloading = false;
      Prefs().dictionaryPreloadStatus = 2; // Loaded

      AnxLog.info(
          'Dictionary preloading completed: ${_cache.length} entries loaded in ${duration.inMilliseconds}ms');
      return true;
    } catch (e) {
      AnxLog.severe('Error preloading dictionary: $e');
      _isPreloading = false;
      _isPreloaded = false;
      Prefs().dictionaryPreloadStatus = 0; // Not loaded
      return false;
    }
  }

  /// Unload the preloaded dictionary to free memory
  void unloadDictionary() {
    if (!_isPreloaded) return;

    AnxLog.info('Unloading preloaded dictionary...');
    _cache.clear();
    _isPreloaded = false;
    Prefs().dictionaryPreloadStatus = 0; // Not loaded
    AnxLog.info('Dictionary unloaded');
  }

  /// Check if the dictionary is preloaded
  bool get isPreloaded => _isPreloaded;

  /// Check if the dictionary is currently being preloaded
  bool get isPreloading => _isPreloading;

  /// Dispose resources
  void dispose() async {
    // Unload dictionary first
    unloadDictionary();

    if (_database != null) {
      await _database!.close();
      _database = null;
    }

    _connectivitySubscription?.cancel();
    _onlineDictionaryService.dispose();

    _isInitialized = false;
  }

  /// Helper function for synchronized blocks
  Future<T> synchronized<T>(Future<T> Function() fn) async {
    if (!_lock.toString().contains('Mutex')) {
      // If using a simple Object for locking
      return await fn();
    } else {
      // If using a proper Mutex
      // This is a placeholder for proper mutex implementation
      return await fn();
    }
  }
}
